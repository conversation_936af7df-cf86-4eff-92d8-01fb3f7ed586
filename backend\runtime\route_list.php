Route List
+----------------------------------+-------------------------------------------+--------+-------------------------------------------+
| Rule                             | Route                                     | Method | Name                                      |
+----------------------------------+-------------------------------------------+--------+-------------------------------------------+
| api/auth/login                   | AuthController@login                      | post   | AuthController@login                      |
| api/auth/register                | AuthController@register                   | post   | AuthController@register                   |
| api/auth/forgot-password         | AuthController@forgotPassword             | post   | AuthController@forgotPassword             |
| api/auth/reset-password          | AuthController@resetPassword              | post   | AuthController@resetPassword              |
| api/auth/verify-email            | AuthController@verifyEmail                | post   | AuthController@verifyEmail                |
| api/auth/refresh-token           | AuthController@refreshToken               | post   | AuthController@refreshToken               |
| api/auth/me                      | AuthController@me                         | get    | AuthController@me                         |
| api/auth/logout                  | AuthController@logout                     | post   | AuthController@logout                     |
| api/auth/change-password         | AuthController@changePassword             | post   | AuthController@changePassword             |
| api/auth/profile                 | AuthController@updateProfile              | put    | AuthController@updateProfile              |
| api/dashboard/stats              | DashboardController@stats                 | get    | DashboardController@stats                 |
| api/dashboard/activities         | DashboardController@activities            | get    | DashboardController@activities            |
| api/dashboard/visit-trend        | DashboardController@visitTrend            | get    | DashboardController@visitTrend            |
| api/dashboard/quick-stats        | DashboardController@quickStats            | get    | DashboardController@quickStats            |
| api/user                         | UserController/index                      | get    | UserController/index                      |
| api/user                         | UserController/save                       | post   | UserController/save                       |
| api/user/create                  | UserController/create                     | get    | UserController/create                     |
| api/user/<id>/edit               | UserController/edit                       | get    | UserController/edit                       |
| api/user/<id>                    | UserController/read                       | get    | UserController/read                       |
| api/user/<id>                    | UserController/update                     | put    | UserController/update                     |
| api/user/<id>                    | UserController/delete                     | delete | UserController/delete                     |
| api/user/<id>/enable             | UserController@enable                     | post   | UserController@enable                     |
| api/user/<id>/disable            | UserController@disable                    | post   | UserController@disable                    |
| api/user/<id>/reset-password     | UserController@resetPassword              | post   | UserController@resetPassword              |
| api/user/<id>/assign-roles       | UserController@assignRoles                | post   | UserController@assignRoles                |
| api/user/batch                   | UserController@batchDelete                | delete | UserController@batchDelete                |
| api/user/statistics              | UserController@statistics                 | get    | UserController@statistics                 |
| api/user/export                  | UserController@export                     | get    | UserController@export                     |
| api/user/import                  | UserController@import                     | post   | UserController@import                     |
| api/user/check-username          | UserController@checkUsername              | get    | UserController@checkUsername              |
| api/user/check-email             | UserController@checkEmail                 | get    | UserController@checkEmail                 |
| api/user/check-phone             | UserController@checkPhone                 | get    | UserController@checkPhone                 |
| api/user/<id>/logs               | UserController@getLogs                    | get    | UserController@getLogs                    |
| api/user/<id>/login-history      | UserController@getLoginHistory            | get    | UserController@getLoginHistory            |
| api/role                         | RoleController/index                      | get    | RoleController/index                      |
| api/role                         | RoleController/save                       | post   | RoleController/save                       |
| api/role/create                  | RoleController/create                     | get    | RoleController/create                     |
| api/role/<id>/edit               | RoleController/edit                       | get    | RoleController/edit                       |
| api/role/<id>                    | RoleController/read                       | get    | RoleController/read                       |
| api/role/<id>                    | RoleController/update                     | put    | RoleController/update                     |
| api/role/<id>                    | RoleController/delete                     | delete | RoleController/delete                     |
| api/role/permissions             | RoleController@getPermissions             | get    | RoleController@getPermissions             |
| api/role/<id>/assign-permissions | RoleController@assignPermissions          | post   | RoleController@assignPermissions          |
| api/role/batch                   | RoleController@batchDelete                | delete | RoleController@batchDelete                |
| api/diy-page                     | DiyPageController/index                   | get    | DiyPageController/index                   |
| api/diy-page                     | DiyPageController/save                    | post   | DiyPageController/save                    |
| api/diy-page/create              | DiyPageController/create                  | get    | DiyPageController/create                  |
| api/diy-page/<id>/edit           | DiyPageController/edit                    | get    | DiyPageController/edit                    |
| api/diy-page/<id>                | DiyPageController/read                    | get    | DiyPageController/read                    |
| api/diy-page/<id>                | DiyPageController/update                  | put    | DiyPageController/update                  |
| api/diy-page/<id>                | DiyPageController/delete                  | delete | DiyPageController/delete                  |
| api/diy-page/<id>/publish        | DiyPageController@publish                 | post   | DiyPageController@publish                 |
| api/diy-page/<id>/unpublish      | DiyPageController@unpublish               | post   | DiyPageController@unpublish               |
| api/diy-page/<id>/copy           | DiyPageController@copy                    | post   | DiyPageController@copy                    |
| api/diy-page/batch               | DiyPageController@batchDelete             | delete | DiyPageController@batchDelete             |
| api/diy-page/statistics          | DiyPageController@statistics              | get    | DiyPageController@statistics              |
| api/diy-template                 | DiyTemplateController/index               | get    | DiyTemplateController/index               |
| api/diy-template                 | DiyTemplateController/save                | post   | DiyTemplateController/save                |
| api/diy-template/create          | DiyTemplateController/create              | get    | DiyTemplateController/create              |
| api/diy-template/<id>/edit       | DiyTemplateController/edit                | get    | DiyTemplateController/edit                |
| api/diy-template/<id>            | DiyTemplateController/read                | get    | DiyTemplateController/read                |
| api/diy-template/<id>            | DiyTemplateController/update              | put    | DiyTemplateController/update              |
| api/diy-template/<id>            | DiyTemplateController/delete              | delete | DiyTemplateController/delete              |
| api/diy-template/categories      | DiyTemplateController@getCategories       | get    | DiyTemplateController@getCategories       |
| api/diy-template/<id>/copy       | DiyTemplateController@copy                | post   | DiyTemplateController@copy                |
| api/diy-template/batch           | DiyTemplateController@batchDelete         | delete | DiyTemplateController@batchDelete         |
| api/diy/page                     | DiyController@getPages                    | get    | DiyController@getPages                    |
| api/diy/page                     | DiyController@createPage                  | post   | DiyController@createPage                  |
| api/diy/page/<id>                | DiyController@getPage                     | get    | DiyController@getPage                     |
| api/diy/page/<id>                | DiyController@updatePage                  | put    | DiyController@updatePage                  |
| api/diy/page/<id>                | DiyController@deletePage                  | delete | DiyController@deletePage                  |
| api/diy/page/<id>/publish        | DiyController@publishPage                 | post   | DiyController@publishPage                 |
| api/diy/page/<id>/unpublish      | DiyController@unpublishPage               | post   | DiyController@unpublishPage               |
| api/diy/page/<id>/content        | DiyController@savePageContent             | post   | DiyController@savePageContent             |
| api/diy/page/<id>/preview        | DiyController@previewPage                 | get    | DiyController@previewPage                 |
| api/diy/component                | DiyController@getComponents               | get    | DiyController@getComponents               |
| api/diy/component                | DiyController@createComponent             | post   | DiyController@createComponent             |
| api/diy/component/<id>           | DiyController@getComponent                | get    | DiyController@getComponent                |
| api/diy/component/<id>           | DiyController@updateComponent             | put    | DiyController@updateComponent             |
| api/diy/component/<id>           | DiyController@deleteComponent             | delete | DiyController@deleteComponent             |
| api/diy/template                 | DiyController@getTemplates                | get    | DiyController@getTemplates                |
| api/diy/template                 | DiyController@createTemplate              | post   | DiyController@createTemplate              |
| api/diy/template/all             | DiyController@getAllTemplates             | get    | DiyController@getAllTemplates             |
| api/diy/template/<id>            | DiyController@getTemplate                 | get    | DiyController@getTemplate                 |
| api/diy/template/<id>            | DiyController@updateTemplate              | put    | DiyController@updateTemplate              |
| api/diy/template/<id>            | DiyController@deleteTemplate              | delete | DiyController@deleteTemplate              |
| api/diy/template/<id>/copy       | DiyController@copyTemplate                | post   | DiyController@copyTemplate                |
| api/diy/statistics               | DiyController@getStatistics               | get    | DiyController@getStatistics               |
| api/diy/batch                    | DiyController@batchDelete                 | delete | DiyController@batchDelete                 |
| api/upload/image                 | UploadController@image                    | post   | UploadController@image                    |
| api/upload/video                 | UploadController@video                    | post   | UploadController@video                    |
| api/upload/document              | UploadController@document                 | post   | UploadController@document                 |
| api/upload/avatar                | UploadController@avatar                   | post   | UploadController@avatar                   |
| api/upload/batch                 | UploadController@batch                    | post   | UploadController@batch                    |
| api/upload/base64                | UploadController@base64                   | post   | UploadController@base64                   |
| api/upload/config                | UploadController@config                   | get    | UploadController@config                   |
| api/upload/delete                | UploadController@delete                   | delete | UploadController@delete                   |
| api/upload/info                  | UploadController@info                     | get    | UploadController@info                     |
| api/upload/thumbnail             | UploadController@thumbnail                | post   | UploadController@thumbnail                |
| api/upload/crop                  | UploadController@crop                     | post   | UploadController@crop                     |
| api/upload/compress              | UploadController@compress                 | post   | UploadController@compress                 |
| api/upload/statistics            | UploadController@statistics               | get    | UploadController@statistics               |
| api/search/global                | SearchController@global                   | get    | SearchController@global                   |
| api/search/users                 | SearchController@users                    | get    | SearchController@users                    |
| api/search/pages                 | SearchController@pages                    | get    | SearchController@pages                    |
| api/search/templates             | SearchController@templates                | get    | SearchController@templates                |
| api/system/info                  | SystemController@info                     | get    | SystemController@info                     |
| api/system/settings              | SystemController@getSettings              | get    | SystemController@getSettings              |
| api/system/settings              | SystemController@updateSettings           | post   | SystemController@updateSettings           |
| api/system/cache/clear           | SystemController@clearCache               | get    | SystemController@clearCache               |
| api/system/logs                  | SystemController@getLogs                  | get    | SystemController@getLogs                  |
| api/system/logs                  | SystemController@clearLogs                | delete | SystemController@clearLogs                |
| api/log/list                     | LogController@index                       | get    | LogController@index                       |
| api/log/<id>                     | LogController@read                        | get    | LogController@read                        |
| api/log/batch                    | LogController@batchDelete                 | delete | LogController@batchDelete                 |
| api/log/statistics               | LogController@statistics                  | get    | LogController@statistics                  |
| api/docs                         | ApiDocController@index                    | get    | ApiDocController@index                    |
| api/public/templates/featured    | DiyTemplateController@getFeatured         | get    | DiyTemplateController@getFeatured         |
| api/public/templates/categories  | DiyTemplateController@getPublicCategories | get    | DiyTemplateController@getPublicCategories |
| api/public/template/<slug>       | DiyTemplateController@getBySlug           | get    | DiyTemplateController@getBySlug           |
| api/public/page/<slug>           | DiyPageController@getBySlug               | get    | DiyPageController@getBySlug               |
| api/public/pages/published       | DiyPageController@getPublished            | get    | DiyPageController@getPublished            |
| api/public/contents/<type>       | ContentController@getByType               | get    | ContentController@getByType               |
| api/public/content/<slug>        | ContentController@getBySlug               | get    | ContentController@getBySlug               |
| api/public/site/info             | SystemController@getSiteInfo              | get    | SystemController@getSiteInfo              |
| api/test/index                   | TestController@index                      | get    | TestController@index                      |
| api/test/database                | TestController@database                   | get    | TestController@database                   |
| api/test/diy-stats               | TestController@diyStats                   | get    | TestController@diyStats                   |
+----------------------------------+-------------------------------------------+--------+-------------------------------------------+
