<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 环境变量修复脚本
 */

echo "=== 环境变量修复 ===\n\n";

// 切换到backend目录
chdir(__DIR__ . '/backend');

echo "1. 检查.env文件...\n";
if (file_exists('.env')) {
    echo "   ✅ .env文件存在\n";
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    echo "   行数: " . count($lines) . "\n";
    
    // 检查关键配置
    $keyConfigs = ['CACHE_DRIVER', 'DATABASE_HOSTNAME', 'DATABASE_DATABASE'];
    foreach ($keyConfigs as $key) {
        if (strpos($envContent, $key) !== false) {
            echo "   ✅ {$key} 配置存在\n";
        } else {
            echo "   ❌ {$key} 配置缺失\n";
        }
    }
} else {
    echo "   ❌ .env文件不存在\n";
}

echo "\n2. 检查ThinkPHP环境加载...\n";

try {
    require_once 'vendor/autoload.php';
    echo "   ✅ 自动加载成功\n";
    
    // 手动加载.env文件
    if (class_exists('\Dotenv\Dotenv')) {
        echo "   ✅ Dotenv类存在\n";
        
        try {
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/backend');
            $dotenv->load();
            echo "   ✅ .env文件加载成功\n";
            
            // 测试环境变量
            $cacheDriver = $_ENV['CACHE_DRIVER'] ?? getenv('CACHE_DRIVER');
            echo "   缓存驱动: " . ($cacheDriver ?: 'NULL') . "\n";
            
        } catch (Exception $e) {
            echo "   ❌ .env加载失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ Dotenv类不存在\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ 加载失败: " . $e->getMessage() . "\n";
}

echo "\n3. 修复配置文件...\n";

// 修复缓存配置 - 使用硬编码值而不是env()
$cacheConfig = '<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 缓存配置（修复版）
 */

return [
    // 默认缓存驱动
    \'default\' => \'file\',

    // 缓存连接方式配置
    \'stores\'  => [
        \'file\' => [
            // 驱动方式
            \'type\'       => \'File\',
            // 缓存保存目录
            \'path\'       => runtime_path() . \'cache\' . DIRECTORY_SEPARATOR,
            // 缓存前缀
            \'prefix\'     => \'qiyediy:cache:\',
            // 缓存有效期 0表示永久缓存
            \'expire\'     => 0,
        ],
        \'redis\' => [
            \'type\'       => \'redis\',
            \'host\'       => \'127.0.0.1\',
            \'port\'       => 6379,
            \'password\'   => \'\',
            \'select\'     => 0,
            \'timeout\'    => 0,
            \'expire\'     => 0,
            \'persistent\' => false,
            \'prefix\'     => \'qiyediy:cache:\',
        ],
    ],
];';

file_put_contents('config/cache.php', $cacheConfig);
echo "   ✅ 缓存配置已修复\n";

// 修复数据库配置
$databaseConfig = '<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 数据库配置（修复版）
 */

return [
    // 默认数据库连接标识
    \'default\' => \'mysql\',
    
    // 数据库连接信息
    \'connections\' => [
        \'mysql\' => [
            // 数据库类型
            \'type\' => \'mysql\',
            // 服务器地址
            \'hostname\' => \'127.0.0.1\',
            // 数据库名
            \'database\' => \'qiyediy\',
            // 用户名
            \'username\' => \'root\',
            // 密码
            \'password\' => \'123456\',
            // 端口
            \'hostport\' => \'3306\',
            // 数据库连接参数
            \'params\' => [],
            // 数据库编码默认采用utf8
            \'charset\' => \'utf8mb4\',
            // 数据库表前缀
            \'prefix\' => \'qd_\',
            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            \'deploy\' => 0,
            // 数据库读写是否分离 主从式有效
            \'rw_separate\' => false,
            // 读写分离后 主服务器数量
            \'master_num\' => 1,
            // 指定从服务器序号
            \'slave_no\' => \'\',
            // 自动读取主库数据
            \'read_master\' => false,
            // 是否严格检查字段是否存在
            \'fields_strict\' => true,
            // 是否需要断线重连
            \'break_reconnect\' => false,
            // 监听SQL
            \'trigger_sql\' => true,
            // 开启字段缓存
            \'fields_cache\' => false,
            // 字段缓存路径
            \'schema_cache_path\' => app()->getRuntimePath() . \'schema\' . DIRECTORY_SEPARATOR,
        ],
    ],
];';

file_put_contents('config/database.php', $databaseConfig);
echo "   ✅ 数据库配置已修复\n";

// 修复session配置
$sessionConfig = '<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - Session配置（修复版）
 */

return [
    // session name
    \'name\' => \'PHPSESSID\',
    
    // SESSION_ID的提交变量,解决flash上传跨域
    \'var_session_id\' => \'\',
    
    // 驱动方式 支持file cache
    \'type\' => \'file\',
    
    // 存储连接标识 当type使用cache的时候有效
    \'store\' => null,
    
    // 过期时间
    \'expire\' => 7200,
    
    // 前缀
    \'prefix\' => \'qiyediy:session:\',
    
    // 序列化机制 可选 php serialize
    \'serialize\' => \'serialize\',
    
    // 垃圾回收的概率 session.gc_probability
    \'gc_probability\' => 1,
    
    // 垃圾回收的除数 session.gc_divisor
    \'gc_divisor\' => 1000,
    
    // 垃圾回收的最大生存时间 session.gc_maxlifetime
    \'gc_maxlifetime\' => 7200,
    
    // session 保存路径
    \'save_path\' => runtime_path() . \'session\' . DIRECTORY_SEPARATOR,
    
    // 是否自动开启 SESSION
    \'auto_start\' => true,
    
    // httponly设置
    \'httponly\' => true,
    
    // 是否使用 use_strict_mode
    \'use_strict_mode\' => false,
    
    // 是否使用 use_cookies
    \'use_cookies\' => true,
    
    // 是否使用 use_only_cookies
    \'use_only_cookies\' => true,
    
    // 是否使用 use_trans_sid
    \'use_trans_sid\' => false,
    
    // 缓存限制器
    \'cache_limiter\' => \'nocache\',
    
    // 缓存过期时间
    \'cache_expire\' => 180,
];';

file_put_contents('config/session.php', $sessionConfig);
echo "   ✅ Session配置已修复\n";

echo "\n=== 修复完成 ===\n";
echo "请重启服务器并测试API\n";
