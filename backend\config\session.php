<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - Session配置（修复版）
 */

return [
    // session name
    'name' => 'PHPSESSID',
    
    // SESSION_ID的提交变量,解决flash上传跨域
    'var_session_id' => '',
    
    // 驱动方式 支持file cache
    'type' => 'file',
    
    // 存储连接标识 当type使用cache的时候有效
    'store' => null,
    
    // 过期时间
    'expire' => 7200,
    
    // 前缀
    'prefix' => 'qiyediy:session:',
    
    // 序列化机制 可选 php serialize
    'serialize' => 'serialize',
    
    // 垃圾回收的概率 session.gc_probability
    'gc_probability' => 1,
    
    // 垃圾回收的除数 session.gc_divisor
    'gc_divisor' => 1000,
    
    // 垃圾回收的最大生存时间 session.gc_maxlifetime
    'gc_maxlifetime' => 7200,
    
    // session 保存路径
    'save_path' => runtime_path() . 'session' . DIRECTORY_SEPARATOR,
    
    // 是否自动开启 SESSION
    'auto_start' => true,
    
    // httponly设置
    'httponly' => true,
    
    // 是否使用 use_strict_mode
    'use_strict_mode' => false,
    
    // 是否使用 use_cookies
    'use_cookies' => true,
    
    // 是否使用 use_only_cookies
    'use_only_cookies' => true,
    
    // 是否使用 use_trans_sid
    'use_trans_sid' => false,
    
    // 缓存限制器
    'cache_limiter' => 'nocache',
    
    // 缓存过期时间
    'cache_expire' => 180,
];