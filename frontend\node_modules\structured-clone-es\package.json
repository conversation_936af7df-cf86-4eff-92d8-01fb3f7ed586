{"name": "structured-clone-es", "type": "module", "version": "1.0.0", "description": "A re-distribution of @ungap/structured-clone for Node.js comptaible ES Modules", "author": "<PERSON>", "license": "ISC", "homepage": "https://github.com/antfu/structured-clone-es#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/structured-clone-es.git"}, "bugs": "https://github.com/antfu/structured-clone-es/issues", "keywords": [], "sideEffects": false, "exports": {".": "./dist/index.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@antfu/ni": "^23.2.0", "@antfu/utils": "^8.1.0", "@types/node": "^22.10.6", "@types/ungap__structured-clone": "^1.2.0", "@ungap/structured-clone": "^1.2.1", "bumpp": "^9.10.1", "eslint": "^9.18.0", "lint-staged": "^15.3.0", "pnpm": "^9.15.4", "simple-git-hooks": "^2.11.1", "tsx": "^4.19.2", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vite": "^6.0.7"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp && pnpm publish", "start": "tsx src/index.ts", "typecheck": "tsc --noEmit"}}