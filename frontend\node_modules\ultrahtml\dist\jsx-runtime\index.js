import{TEXT_NODE as a}from"../index.js";import{ELEMENT_NODE as s,Fragment as d,__unsafeRenderFn as f}from"../index.js";function g(n,{children:e,...o},i,m,u){let t={type:s,name:typeof n=="function"?n.name:n,attributes:o,children:(Array.isArray(e)?e:[e]).map(r=>typeof r=="string"?{type:a,value:r}:r),parent:void 0,loc:[]};return typeof n=="function"&&f(t,n),t}export{d as Fragment,g as jsx,g as jsxDEV,g as jsxs};
