// Generated by dts-bundle-generator v9.5.1

type Node$1 = DocumentNode | ElementNode | TextNode | CommentNode | DoctypeNode;
export type NodeType = typeof DOCUMENT_NODE | typeof ELEMENT_NODE | typeof TEXT_NODE | typeof COMMENT_NODE | typeof DOCTYPE_NODE;
interface Location$1 {
	start: number;
	end: number;
}
interface BaseNode {
	type: NodeType;
	loc: [
		Location$1,
		Location$1
	];
	parent: Node$1;
	[key: string]: any;
}
interface LiteralNode extends BaseNode {
	value: string;
}
interface ParentNode$1 extends BaseNode {
	children: Node$1[];
}
export interface DocumentNode extends Omit<ParentNode$1, "parent"> {
	type: typeof DOCUMENT_NODE;
	attributes: Record<string, string>;
	parent: undefined;
}
export interface ElementNode extends ParentNode$1 {
	type: typeof ELEMENT_NODE;
	name: string;
	attributes: Record<string, string>;
}
export interface TextNode extends LiteralNode {
	type: typeof TEXT_NODE;
}
export interface CommentNode extends LiteralNode {
	type: typeof COMMENT_NODE;
}
export interface DoctypeNode extends LiteralNode {
	type: typeof DOCTYPE_NODE;
}
export declare const DOCUMENT_NODE = 0;
export declare const ELEMENT_NODE = 1;
export declare const TEXT_NODE = 2;
export declare const COMMENT_NODE = 3;
export declare const DOCTYPE_NODE = 4;
export declare function h(type: any, props?: null | Record<string, any>, ...children: any[]): ElementNode;
export declare const Fragment: unique symbol;
export declare function parse(input: string | ReturnType<typeof html>): any;
export interface Visitor {
	(node: Node$1, parent?: Node$1, index?: number): void | Promise<void>;
}
export interface VisitorSync {
	(node: Node$1, parent?: Node$1, index?: number): void;
}
export declare const RenderFn: unique symbol;
export declare function __unsafeHTML(str: string): {
	value: string;
};
export declare function __unsafeRenderFn(node: ElementNode, fn: (props: Record<string, any>, ...children: Node$1[]) => Node$1): ElementNode;
export declare function attrs(attributes: Record<string, string>): {
	value: string;
};
export declare function html(tmpl: TemplateStringsArray, ...vals: any[]): {
	value: string;
};
export declare function walk(node: Node$1, callback: Visitor): Promise<void>;
export declare function walkSync(node: Node$1, callback: VisitorSync): void;
export declare function renderSync(node: Node$1): string;
export declare function render(node: Node$1): Promise<string>;
interface Transformer$1 {
	(node: Node$1): Node$1 | Promise<Node$1>;
}
export interface TransformerSync {
	(node: Node$1): Node$1;
}
export declare function transform(markup: string | Node$1, transformers?: Transformer$1[]): Promise<string>;
export declare function transformSync(markup: string | Node$1, transformers?: TransformerSync[]): string;

export {
	Location$1 as Location,
	Node$1 as Node,
	Transformer$1 as Transformer,
};

export {};
