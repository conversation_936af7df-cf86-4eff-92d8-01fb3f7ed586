<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 控制器测试脚本
 */

echo "=== 控制器测试 ===\n\n";

// 切换到backend目录
chdir(__DIR__ . '/backend');

try {
    require_once 'vendor/autoload.php';
    echo "✅ 自动加载成功\n";
    
    // 测试控制器类是否存在
    $controllers = [
        'app\\controller\\Index',
        'app\\controller\\AuthController',
        'app\\controller\\Api',
        'app\\controller\\TestController',
        'app\\controller\\SimpleController'
    ];
    
    foreach ($controllers as $controller) {
        if (class_exists($controller)) {
            echo "✅ {$controller} 类存在\n";
            
            // 尝试实例化
            try {
                $instance = new $controller();
                echo "   ✅ 实例化成功\n";
            } catch (Exception $e) {
                echo "   ❌ 实例化失败: " . $e->getMessage() . "\n";
            }
        } else {
            echo "❌ {$controller} 类不存在\n";
        }
    }
    
    echo "\n测试ThinkPHP应用解析...\n";
    
    // 创建应用实例
    $app = new \think\App();
    echo "✅ 应用实例创建成功\n";
    
    // 测试控制器解析
    $controllerNames = ['Index', 'AuthController', 'Api'];
    
    foreach ($controllerNames as $name) {
        try {
            $class = $app->parseClass('controller', $name);
            echo "✅ {$name} 解析为: {$class}\n";
            
            if (class_exists($class)) {
                echo "   ✅ 类存在\n";
            } else {
                echo "   ❌ 类不存在\n";
            }
        } catch (Exception $e) {
            echo "❌ {$name} 解析失败: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
