{"name": "ultrahtml", "type": "module", "version": "1.6.0", "types": "./dist/index.d.ts", "main": "./dist/index.js", "repository": {"type": "git", "url": "https://github.com/natemoo-re/ultrahtml"}, "bugs": {"url": "https://github.com/natemoo-re/ultrahtml/issues"}, "homepage": "https://github.com/natemoo-re/ultrahtml#README", "files": ["selector.d.ts", "transform.d.ts", "jsx-runtime.d.ts", "transformers", "dist", "CHANGELOG.md"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./package.json": "./package.json", "./selector": {"types": "./dist/selector.d.ts", "import": "./dist/selector.js"}, "./transformers/*": {"types": "./dist/transformers/*.d.ts", "import": "./dist/transformers/*.js"}, "./jsx-runtime": {"types": "./dist/jsx-runtime/index.d.ts", "import": "./dist/jsx-runtime/index.js"}}, "keywords": ["html", "template", "sanitize"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/n_moore"}, "license": "MIT", "volta": {"node": "18.20.4"}, "devDependencies": {"@biomejs/biome": "1.9.2", "@changesets/cli": "^2.27.8", "@types/stylis": "^4.2.6", "chalk": "^5.3.0", "dts-bundle-generator": "^9.5.1", "esbuild": "^0.25.2", "globby": "^13.2.2", "gzip-size": "^7.0.0", "markdown-it": "^13.0.2", "media-query-fns": "^2.0.0", "npm-run-all": "^4.1.5", "parsel-js": "^1.1.2", "pretty-bytes": "^6.1.1", "stylis": "^4.3.4", "vitest": "^2.1.1"}, "scripts": {"build": "node scripts/build.js", "format": "biome format --write", "dev": "vitest", "test": "vitest run"}}