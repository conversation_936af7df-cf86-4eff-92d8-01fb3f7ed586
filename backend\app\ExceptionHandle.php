<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 异常处理器
 */

declare(strict_types=1);

namespace app;

use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Response;
use Throwable;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     * @param \think\Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 添加自定义异常处理机制
        
        // 参数验证错误
        if ($e instanceof ValidateException) {
            return json([
                'code' => 422,
                'message' => $e->getError(),
                'data' => [],
                'timestamp' => time()
            ], 422);
        }

        // 数据不存在异常
        if ($e instanceof DataNotFoundException || $e instanceof ModelNotFoundException) {
            return json([
                'code' => 404,
                'message' => '数据不存在',
                'data' => [],
                'timestamp' => time()
            ], 404);
        }

        // HTTP异常
        if ($e instanceof HttpException && $request->isAjax()) {
            return json([
                'code' => $e->getStatusCode(),
                'message' => $e->getMessage() ?: 'HTTP错误',
                'data' => [],
                'timestamp' => time()
            ], $e->getStatusCode());
        }

        // 其他异常
        if ($request->isAjax() || $this->isApiRequest($request)) {
            $code = 500;
            $message = 'Internal Server Error';
            
            // 开发模式显示详细错误信息
            if (app()->isDebug()) {
                $message = $e->getMessage();
                $data = [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTrace()
                ];
            } else {
                $data = [];
            }

            return json([
                'code' => $code,
                'message' => $message,
                'data' => $data,
                'timestamp' => time()
            ], $code);
        }

        // 其他情况交给系统处理
        return parent::render($request, $e);
    }

    /**
     * 判断是否为API请求
     * @param \think\Request $request
     * @return bool
     */
    private function isApiRequest($request): bool
    {
        $path = $request->pathinfo();
        return strpos($path, 'api/') === 0;
    }
}
