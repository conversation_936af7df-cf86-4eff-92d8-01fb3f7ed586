<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - API基础控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Response;

/**
 * API基础控制器
 */
class Api extends BaseController
{
    /**
     * API信息
     * @return Response
     */
    public function index(): Response
    {
        return $this->success([
            'message' => 'QiyeDIY API Service',
            'version' => '1.0.0',
            'endpoints' => [
                'POST /api/auth/login' => '用户登录',
                'GET /api/diy/statistics' => 'DIY统计',
                'GET /api/test/database' => '数据库测试',
                'GET /simple/test' => '简单测试'
            ]
        ]);
    }
}
