<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 简化测试
 */

echo "=== 简化API测试 ===\n\n";

// 测试基础API
echo "1. 测试基础API...\n";
$response = file_get_contents('http://localhost:8000');
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['code'] == 200) {
        echo "✅ 基础API正常\n";
        echo "   消息: " . $data['data']['message'] . "\n";
        echo "   版本: " . $data['data']['version'] . "\n";
        echo "   框架: " . $data['data']['framework'] . "\n";
    } else {
        echo "❌ 基础API响应格式错误\n";
    }
} else {
    echo "❌ 基础API无响应\n";
}

echo "\n2. 测试登录API...\n";

// 创建登录请求
$loginData = json_encode([
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => $loginData,
        'ignore_errors' => true
    ]
]);

$response = file_get_contents('http://localhost:8000/api/auth/login', false, $context);
$httpCode = $http_response_header[0] ?? 'Unknown';

echo "   HTTP状态: {$httpCode}\n";

if ($response) {
    // 检查是否是JSON响应
    $data = json_decode($response, true);
    if ($data) {
        echo "   JSON响应: ✅\n";
        if (isset($data['code'])) {
            echo "   响应码: " . $data['code'] . "\n";
            echo "   消息: " . ($data['message'] ?? 'N/A') . "\n";
            
            if ($data['code'] == 200 && isset($data['data']['token'])) {
                echo "   Token: " . substr($data['data']['token'], 0, 20) . "...\n";
                $token = $data['data']['token'];
            } else {
                $token = null;
            }
        } else {
            echo "   响应格式异常\n";
            $token = null;
        }
    } else {
        echo "   非JSON响应: " . substr($response, 0, 200) . "...\n";
        $token = null;
    }
} else {
    echo "   无响应内容\n";
    $token = null;
}

echo "\n3. 测试简单GET API...\n";

// 测试docs接口
$response = file_get_contents('http://localhost:8000/docs', false, stream_context_create([
    'http' => ['ignore_errors' => true]
]));
$httpCode = $http_response_header[0] ?? 'Unknown';

echo "   测试 /docs 接口\n";
echo "   HTTP状态: {$httpCode}\n";

if ($response) {
    $data = json_decode($response, true);
    if ($data && isset($data['code'])) {
        echo "   ✅ docs接口正常\n";
        echo "   端点数量: " . count($data['data']['endpoints'] ?? []) . "\n";
    } else {
        echo "   ❌ docs接口响应异常\n";
    }
} else {
    echo "   ❌ docs接口无响应\n";
}

if ($token) {
    echo "\n4. 测试认证API...\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Authorization: Bearer {$token}\r\n",
            'ignore_errors' => true
        ]
    ]);
    
    $response = file_get_contents('http://localhost:8000/api/diy/statistics', false, $context);
    $httpCode = $http_response_header[0] ?? 'Unknown';
    
    echo "   测试 /api/diy/statistics 接口\n";
    echo "   HTTP状态: {$httpCode}\n";
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data && isset($data['code'])) {
            if ($data['code'] == 200) {
                echo "   ✅ DIY统计API正常\n";
                echo "   数据: " . json_encode($data['data'], JSON_UNESCAPED_UNICODE) . "\n";
            } else {
                echo "   ❌ DIY统计API错误: " . ($data['message'] ?? 'Unknown') . "\n";
            }
        } else {
            echo "   ❌ DIY统计API响应异常\n";
        }
    } else {
        echo "   ❌ DIY统计API无响应\n";
    }
}

echo "\n=== 测试完成 ===\n";
