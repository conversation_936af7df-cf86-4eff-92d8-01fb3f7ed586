var R=0,j=1,X=2,V=3,Y=4;function F(e,t={},...i){let r={type:1,name:typeof e=="function"?e.name:e,attributes:t||{},children:i.map(n=>typeof n=="string"?{type:2,value:y(String(n))}:n),parent:void 0,loc:[]};return typeof e=="function"&&$(r,e),r}var S=Symbol("Fragment"),D=new Set(["area","base","br","col","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),x=new Set(["script","style"]),o=/(?:<(\/?)([a-zA-Z][a-zA-Z0-9\:-]*)(?:\s([^>]*?))?((?:\s*\/)?)>|(<\!\-\-)([\s\S]*?)(\-\->)|(<\!)([\s\S]*?)(>))/gm,b=/[\@\.a-z0-9_\:\-]/i;function I(e){let t={};if(e){let i="none",r,n="",a,l;for(let c=0;c<e.length;c++){let d=e[c];i==="none"?b.test(d)?(r&&(t[r]=n,r=void 0,n=""),a=c,i="key"):d==="="&&r&&(i="value"):i==="key"?b.test(d)||(r=e.substring(a,c),d==="="?i="value":i="none"):d===l&&c>0&&e[c-1]!=="\\"?l&&(n=e.substring(a,c),l=void 0,i="none"):(d==='"'||d==="'")&&!l&&(a=c+1,l=d)}i==="key"&&a!=null&&a<e.length&&(r=e.substring(a,e.length)),r&&(t[r]=n)}return t}function P(e){let t=typeof e=="string"?e:e.value,i,r,n,a,l,c,d,m,s,u=[];o.lastIndex=0,r=i={type:0,children:[]};let g=0;function h(){a=t.substring(g,o.lastIndex-n[0].length),a&&r.children.push({type:2,value:a,parent:r})}for(;n=o.exec(t);){if(c=n[5]||n[8],d=n[6]||n[9],m=n[7]||n[10],x.has(r.name)&&n[2]!==r.name){l=o.lastIndex-n[0].length,r.children.length>0&&(r.children[0].value+=n[0]);continue}else if(c==="<!--"){if(l=o.lastIndex-n[0].length,x.has(r.name))continue;s={type:3,value:d,parent:r,loc:[{start:l,end:l+c.length},{start:o.lastIndex-m.length,end:o.lastIndex}]},u.push(s),s.parent.children.push(s)}else if(c==="<!")l=o.lastIndex-n[0].length,s={type:4,value:d,parent:r,loc:[{start:l,end:l+c.length},{start:o.lastIndex-m.length,end:o.lastIndex}]},u.push(s),s.parent.children.push(s);else if(n[1]!=="/")if(h(),x.has(r.name)){g=o.lastIndex,h();continue}else s={type:1,name:n[2]+"",attributes:I(n[3]),parent:r,children:[],loc:[{start:o.lastIndex-n[0].length,end:o.lastIndex}]},u.push(s),s.parent.children.push(s),n[4]&&n[4].indexOf("/")>-1||D.has(s.name)?(s.loc[1]=s.loc[0],s.isSelfClosingTag=!0):r=s;else h(),n[2]+""===r.name?(s=r,r=s.parent,s.loc.push({start:o.lastIndex-n[0].length,end:o.lastIndex}),a=t.substring(s.loc[0].end,s.loc[1].start),s.children.length===0&&s.children.push({type:2,value:a,parent:r})):n[2]+""===u[u.length-1].name&&u[u.length-1].isSelfClosingTag===!0&&(s=u[u.length-1],s.loc.push({start:o.lastIndex-n[0].length,end:o.lastIndex}));g=o.lastIndex}return a=t.slice(g),r.children.push({type:2,value:a,parent:r}),i}var T=class{constructor(t){this.callback=t}async visit(t,i,r){if(await this.callback(t,i,r),Array.isArray(t.children)){let n=[];for(let a=0;a<t.children.length;a++){let l=t.children[a];n.push(this.visit(l,t,a))}await Promise.all(n)}}},O=class{constructor(t){this.callback=t}visit(t,i,r){if(this.callback(t,i,r),Array.isArray(t.children))for(let n=0;n<t.children.length;n++){let a=t.children[n];this.visit(a,t,n)}}},p=Symbol("HTMLString"),M=Symbol("AttrString"),f=Symbol("RenderFn");function E(e,t=[p]){let i={value:e};for(let r of t)Object.defineProperty(i,r,{value:!0,enumerable:!1,writable:!1});return i}function U(e){return E(e)}function $(e,t){return Object.defineProperty(e,f,{value:t,enumerable:!1}),e}var A={"&":"&amp;","<":"&lt;",">":"&gt;"};function y(e){return e.replace(/[&<>]/g,t=>A[t]||t)}function N(e){let t="";for(let[i,r]of Object.entries(e))t+=` ${i}="${r}"`;return E(t,[p,M])}function H(e,...t){let i="";for(let r=0;r<e.length;r++){i+=e[r];let n=t[r];i.endsWith("...")&&n&&typeof n=="object"?(i=i.slice(0,-3).trimEnd(),i+=N(n).value):n&&n[M]?(i=i.trimEnd(),i+=n.value):n&&n[p]?i+=n.value:typeof n=="string"?i+=y(n):(n||n===0)&&(i+=String(n))}return E(i)}function z(e,t){return new T(t).visit(e)}function B(e,t){return new O(t).visit(e)}function C(e){if(e.children.length===0){let t=e;for(;t=t.parent;)if(t.name==="svg")return!0}return!1}async function k(e){let{name:t,attributes:i={}}=e,r=await Promise.all(e.children.map(a=>_(a))).then(a=>a.join(""));if(f in e){let a=await e[f](i,E(r));return a&&a[p]?a.value:y(String(a))}if(t===S)return r;let n=C(e);return n||D.has(t)?`<${e.name}${N(i).value}${n?" /":""}>`:`<${e.name}${N(i).value}>${r}</${e.name}>`}function L(e){let{name:t,attributes:i={}}=e,r=e.children.map(a=>v(a)).join("");if(f in e){let a=e[f](i,E(r));return a&&a[p]?a.value:y(String(a))}if(t===S)return r;let n=C(e);return n||D.has(t)?`<${e.name}${N(i).value}${n?" /":""}>`:`<${e.name}${N(i).value}>${r}</${e.name}>`}function v(e){switch(e.type){case 0:return e.children.map(t=>v(t)).join("");case 1:return L(e);case 2:return`${e.value}`;case 3:return`<!--${e.value}-->`;case 4:return`<!${e.value}>`}}async function _(e){switch(e.type){case 0:return Promise.all(e.children.map(t=>_(t))).then(t=>t.join(""));case 1:return k(e);case 2:return`${e.value}`;case 3:return`<!--${e.value}-->`;case 4:return`<!${e.value}>`}}function w(e,t){if(!Array.isArray(t))throw new Error(`Invalid second argument for \`transform\`! Expected \`Transformer[]\` but got \`${typeof t}\``);return{doc:typeof e=="string"?P(e):e}}async function G(e,t=[]){let{doc:i}=w(e,t),r=i;for(let n of t)r=await n(r);return _(r)}function K(e,t=[]){let{doc:i}=w(e,t),r=i;for(let n of t)r=n(r);return v(r)}export{V as COMMENT_NODE,Y as DOCTYPE_NODE,R as DOCUMENT_NODE,j as ELEMENT_NODE,S as Fragment,f as RenderFn,X as TEXT_NODE,U as __unsafeHTML,$ as __unsafeRenderFn,N as attrs,F as h,H as html,P as parse,_ as render,v as renderSync,G as transform,K as transformSync,z as walk,B as walkSync};
