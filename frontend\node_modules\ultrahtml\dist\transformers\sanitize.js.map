{"version": 3, "sources": ["../../src/transformers/sanitize.ts"], "sourcesContent": ["import { <PERSON>ementN<PERSON>, ELEMENT_NODE, Node, walkSync } from '../index.js';\n\nexport interface SanitizeOptions {\n\t/** An Array of strings indicating elements that the sanitizer should not remove. All elements not in the array will be dropped. */\n\tallowElements?: string[];\n\t/** An Array of strings indicating elements that the sanitizer should not remove. All elements not in the array will be removed while keeping their child content. */\n\tunblockElements?: string[];\n\t/** An Array of strings indicating elements that the sanitizer should remove, but keeping their child elements. */\n\tblockElements?: string[];\n\t/** An Array of strings indicating elements (including nested elements) that the sanitizer should remove. */\n\tdropElements?: string[];\n\t/** An Object where each key is the attribute name and the value is an Array of allowed tag names. Matching attributes will not be removed. All attributes that are not in the array will be dropped. */\n\tallowAttributes?: Record<string, string[]>;\n\t/** An Object where each key is the attribute name and the value is an Array of dropped tag names. Matching attributes will be removed. */\n\tdropAttributes?: Record<string, string[]>;\n\t/** A Boolean value set to false (default) to remove components and their children. If set to true, components will be subject to built-in and custom configuration checks (and will be retained or dropped based on those checks). */\n\tallowComponents?: boolean;\n\t/** A Boolean value set to false (default) to remove custom elements and their children. If set to true, custom elements will be subject to built-in and custom configuration checks (and will be retained or dropped based on those checks). */\n\tallowCustomElements?: boolean;\n\t/** A Boolean value set to false (default) to remove HTML comments. Set to true in order to keep comments. */\n\tallowComments?: boolean;\n}\n\nfunction resolveSantizeOptions(\n\tsanitize?: SanitizeOptions,\n): Required<SanitizeOptions> {\n\tif (sanitize === undefined) {\n\t\treturn {\n\t\t\tallowElements: [] as string[],\n\t\t\tdropElements: ['script'],\n\t\t\tallowComponents: false,\n\t\t\tallowCustomElements: false,\n\t\t\tallowComments: false,\n\t\t} as Required<SanitizeOptions>;\n\t} else {\n\t\tconst dropElements = new Set<string>([]);\n\t\tif (!sanitize.allowElements?.includes('script')) {\n\t\t\tdropElements.add('script');\n\t\t}\n\t\tfor (const dropElement of sanitize.dropElements ?? []) {\n\t\t\tdropElements.add(dropElement);\n\t\t}\n\t\treturn {\n\t\t\tallowComponents: false,\n\t\t\tallowCustomElements: false,\n\t\t\tallowComments: false,\n\t\t\t...sanitize,\n\t\t\tdropElements: Array.from(dropElements),\n\t\t} as Required<SanitizeOptions>;\n\t}\n}\n\ntype NodeKind = 'element' | 'component' | 'custom-element';\nfunction getNodeKind(node: ElementNode): NodeKind {\n\tif (node.name.includes('-')) return 'custom-element';\n\tif (/[\\_\\$A-Z]/.test(node.name[0]) || node.name.includes('.'))\n\t\treturn 'component';\n\treturn 'element';\n}\n\ntype ActionType = 'allow' | 'drop' | 'block';\nfunction getAction(\n\tname: string,\n\tkind: NodeKind,\n\tsanitize: Required<SanitizeOptions>,\n): ActionType {\n\tif (sanitize.allowElements?.length > 0) {\n\t\tif (sanitize.allowElements.includes(name)) return 'allow';\n\t}\n\tif (sanitize.blockElements?.length > 0) {\n\t\tif (sanitize.blockElements.includes(name)) return 'block';\n\t}\n\tif (sanitize.dropElements?.length > 0) {\n\t\tif (sanitize.dropElements.find((n) => n === name)) return 'drop';\n\t}\n\tif (kind === 'component' && !sanitize.allowComponents) return 'drop';\n\tif (kind === 'custom-element' && !sanitize.allowCustomElements) return 'drop';\n\tif (sanitize.unblockElements) {\n\t\treturn sanitize.unblockElements.some((n) => n === name) ? 'allow' : 'block';\n\t}\n\treturn sanitize.allowElements?.length > 0 ? 'drop' : 'allow';\n}\n\nfunction sanitizeAttributes(\n\tnode: ElementNode,\n\tsanitize: Required<SanitizeOptions>,\n): Record<string, string> {\n\tconst attrs: Record<string, string> = node.attributes;\n\tfor (const key of Object.keys(node.attributes)) {\n\t\tif (\n\t\t\t(sanitize.allowAttributes?.[key] &&\n\t\t\t\tsanitize.allowAttributes?.[key].includes(node.name)) ||\n\t\t\tsanitize.allowAttributes?.[key]?.includes('*')\n\t\t) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (\n\t\t\t(sanitize.dropAttributes?.[key] &&\n\t\t\t\tsanitize.dropAttributes?.[key].includes(node.name)) ||\n\t\t\tsanitize.dropAttributes?.[key]?.includes('*')\n\t\t) {\n\t\t\tdelete attrs[key];\n\t\t}\n\t}\n\treturn attrs;\n}\n\nfunction sanitizeElement(\n\topts: Required<SanitizeOptions>,\n\tnode: ElementNode,\n\tparent: Node,\n) {\n\tconst kind = getNodeKind(node);\n\tconst { name } = node;\n\tconst action = getAction(name, kind, opts);\n\tif (action === 'drop')\n\t\treturn () => {\n\t\t\tparent!.children = parent!.children.filter(\n\t\t\t\t(child: Node) => child !== node,\n\t\t\t);\n\t\t};\n\tif (action === 'block')\n\t\treturn () => {\n\t\t\tparent!.children = parent!.children\n\t\t\t\t.map((child: Node) => (child === node ? child.children : child))\n\t\t\t\t.flat(1);\n\t\t};\n\n\treturn () => {\n\t\tnode.attributes = sanitizeAttributes(node, opts);\n\t};\n}\n\nexport default function sanitize(opts?: SanitizeOptions) {\n\tconst sanitize = resolveSantizeOptions(opts);\n\treturn (doc: Node): Node => {\n\t\tlet actions: any[] = [];\n\t\twalkSync(doc, (node: Node, parent?: Node) => {\n\t\t\tswitch (node.type) {\n\t\t\t\tcase ELEMENT_NODE: {\n\t\t\t\t\tactions.push(sanitizeElement(sanitize, node, parent!));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t});\n\t\t// Execute actions in reverse order so that children are mutated before parents.\n\t\tfor (let i = actions.length - 1; i >= 0; i--) {\n\t\t\tactions[i]();\n\t\t}\n\t\treturn doc;\n\t};\n}\n"], "mappings": "AAAA,OAAsB,gBAAAA,EAAoB,YAAAC,MAAgB,cAuB1D,SAASC,EACRC,EAC4B,CAzB7B,IAAAC,EA0BC,GAAID,IAAa,OAChB,MAAO,CACN,cAAe,CAAC,EAChB,aAAc,CAAC,QAAQ,EACvB,gBAAiB,GACjB,oBAAqB,GACrB,cAAe,EAChB,EACM,CACN,IAAME,EAAe,IAAI,IAAY,CAAC,CAAC,GAClCD,EAAAD,EAAS,gBAAT,MAAAC,EAAwB,SAAS,WACrCC,EAAa,IAAI,QAAQ,EAE1B,QAAWC,KAAeH,EAAS,cAAgB,CAAC,EACnDE,EAAa,IAAIC,CAAW,EAE7B,MAAO,CACN,gBAAiB,GACjB,oBAAqB,GACrB,cAAe,GACf,GAAGH,EACH,aAAc,MAAM,KAAKE,CAAY,CACtC,CACD,CACD,CAGA,SAASE,EAAYC,EAA6B,CACjD,OAAIA,EAAK,KAAK,SAAS,GAAG,EAAU,iBAChC,YAAY,KAAKA,EAAK,KAAK,CAAC,CAAC,GAAKA,EAAK,KAAK,SAAS,GAAG,EACpD,YACD,SACR,CAGA,SAASC,EACRC,EACAC,EACAR,EACa,CAjEd,IAAAC,EAAAQ,EAAAC,EAAAC,EAkEC,QAAIV,EAAAD,EAAS,gBAAT,YAAAC,EAAwB,QAAS,GAChCD,EAAS,cAAc,SAASO,CAAI,EAAU,UAE/CE,EAAAT,EAAS,gBAAT,YAAAS,EAAwB,QAAS,GAChCT,EAAS,cAAc,SAASO,CAAI,EAAU,UAE/CG,EAAAV,EAAS,eAAT,YAAAU,EAAuB,QAAS,GAC/BV,EAAS,aAAa,KAAMY,GAAMA,IAAML,CAAI,GAE7CC,IAAS,aAAe,CAACR,EAAS,iBAClCQ,IAAS,kBAAoB,CAACR,EAAS,oBAA4B,OACnEA,EAAS,gBACLA,EAAS,gBAAgB,KAAMY,GAAMA,IAAML,CAAI,EAAI,QAAU,UAE9DI,EAAAX,EAAS,gBAAT,YAAAW,EAAwB,QAAS,EAAI,OAAS,OACtD,CAEA,SAASE,EACRR,EACAL,EACyB,CAtF1B,IAAAC,EAAAQ,EAAAC,EAAAC,EAAAG,EAAAC,EAAAC,EAAAC,EAuFC,IAAMC,EAAgCb,EAAK,WAC3C,QAAWc,KAAO,OAAO,KAAKd,EAAK,UAAU,GAE1CJ,EAAAD,EAAS,kBAAT,MAAAC,EAA2BkB,MAC3BV,EAAAT,EAAS,kBAAT,MAAAS,EAA2BU,GAAK,SAASd,EAAK,SAC/CM,GAAAD,EAAAV,EAAS,kBAAT,YAAAU,EAA2BS,KAA3B,MAAAR,EAAiC,SAAS,QAKzCG,EAAAd,EAAS,iBAAT,MAAAc,EAA0BK,MAC1BJ,EAAAf,EAAS,iBAAT,MAAAe,EAA0BI,GAAK,SAASd,EAAK,SAC9CY,GAAAD,EAAAhB,EAAS,iBAAT,YAAAgB,EAA0BG,KAA1B,MAAAF,EAAgC,SAAS,OAEzC,OAAOC,EAAMC,CAAG,EAGlB,OAAOD,CACR,CAEA,SAASE,EACRC,EACAhB,EACAiB,EACC,CACD,IAAMd,EAAOJ,EAAYC,CAAI,EACvB,CAAE,KAAAE,CAAK,EAAIF,EACXkB,EAASjB,EAAUC,EAAMC,EAAMa,CAAI,EACzC,OAAIE,IAAW,OACP,IAAM,CACZD,EAAQ,SAAWA,EAAQ,SAAS,OAClCE,GAAgBA,IAAUnB,CAC5B,CACD,EACGkB,IAAW,QACP,IAAM,CACZD,EAAQ,SAAWA,EAAQ,SACzB,IAAKE,GAAiBA,IAAUnB,EAAOmB,EAAM,SAAWA,CAAM,EAC9D,KAAK,CAAC,CACT,EAEM,IAAM,CACZnB,EAAK,WAAaQ,EAAmBR,EAAMgB,CAAI,CAChD,CACD,CAEe,SAARrB,EAA0BqB,EAAwB,CACxD,IAAMrB,EAAWD,EAAsBsB,CAAI,EAC3C,OAAQI,GAAoB,CAC3B,IAAIC,EAAiB,CAAC,EACtB5B,EAAS2B,EAAK,CAACpB,EAAYiB,IAAkB,CAC5C,OAAQjB,EAAK,KAAM,CAClB,KAAKR,EAAc,CAClB6B,EAAQ,KAAKN,EAAgBpB,EAAUK,EAAMiB,CAAO,CAAC,EACrD,MACD,CACA,QACC,MACF,CACD,CAAC,EAED,QAASK,EAAID,EAAQ,OAAS,EAAGC,GAAK,EAAGA,IACxCD,EAAQC,CAAC,EAAE,EAEZ,OAAOF,CACR,CACD", "names": ["ELEMENT_NODE", "walkSync", "resolveSantizeOptions", "sanitize", "_a", "dropElements", "dropElement", "getNodeKind", "node", "getAction", "name", "kind", "_b", "_c", "_d", "n", "sanitizeAttributes", "_e", "_f", "_g", "_h", "attrs", "key", "sanitizeElement", "opts", "parent", "action", "child", "doc", "actions", "i"]}