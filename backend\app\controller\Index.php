<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 首页控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Response;

/**
 * 首页控制器
 */
class Index extends BaseController
{
    /**
     * 首页
     * @return Response
     */
    public function index(): Response
    {
        return $this->success([
            'message' => 'QiyeDIY企业建站系统后端API',
            'version' => '1.0.0',
            'time' => date('Y-m-d H:i:s'),
            'server' => 'PHP ' . PHP_VERSION,
            'framework' => 'ThinkPHP 6.1.4'
        ]);
    }

    /**
     * API文档
     * @return Response
     */
    public function docs(): Response
    {
        return $this->success([
            'message' => 'QiyeDIY API 文档',
            'endpoints' => [
                'GET /' => '系统信息',
                'GET /docs' => 'API文档',
                'POST /api/auth/login' => '用户登录',
                'GET /api/test/index' => '测试接口',
                'GET /api/test/database' => '数据库测试',
                'GET /api/diy/statistics' => 'DIY统计（需认证）'
            ]
        ]);
    }
}
