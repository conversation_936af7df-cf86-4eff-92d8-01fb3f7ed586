#pragma once

#if (defined(_WIN32) || defined(__CYGWIN__)) && !defined(GOBJECT_STATIC_COMPILATION)
#  define _GOBJECT_EXPORT __declspec(dllexport)
#  define _GOBJECT_IMPORT __declspec(dllimport)
#elif __GNUC__ >= 4
#  define _GOBJECT_EXPORT __attribute__((visibility("default")))
#  define _GOBJECT_IMPORT
#else
#  define _GOBJECT_EXPORT
#  define _GOBJECT_IMPORT
#endif
#ifdef GOBJECT_COMPILATION
#  define _GOBJECT_API _GOBJECT_EXPORT
#else
#  define _GOBJECT_API _GOBJECT_IMPORT
#endif

#define _GOBJECT_EXTERN _GOBJECT_API extern

#define GOBJECT_VAR _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_IN_ALL _GOBJECT_EXTERN

#ifdef GLIB_DISABLE_DEPRECATION_WARNINGS
#define GOBJECT_DEPRECATED _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_UNAVAILABLE(maj,min) _GOBJECT_EXTERN
#define GOBJECT_UNAVAILABLE_STATIC_INLINE(maj,min)
#else
#define GOBJECT_DEPRECATED G_DEPRECATED _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_FOR(f) G_DEPRECATED_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_UNAVAILABLE(maj,min) G_UNAVAILABLE(maj,min) _GOBJECT_EXTERN
#define GOBJECT_UNAVAILABLE_STATIC_INLINE(maj,min) G_UNAVAILABLE(maj,min)
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_26
#define GOBJECT_DEPRECATED_IN_2_26 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_26_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_26 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_26_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_26 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_26_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_26 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_26_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_26 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_26_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_26
#define GOBJECT_DEPRECATED_MACRO_IN_2_26_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_26
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_26_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_26
#define GOBJECT_DEPRECATED_TYPE_IN_2_26_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_26
#define GOBJECT_AVAILABLE_IN_2_26 GOBJECT_UNAVAILABLE (2, 26)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_26 GLIB_UNAVAILABLE_STATIC_INLINE (2, 26)
#define GOBJECT_AVAILABLE_MACRO_IN_2_26 GLIB_UNAVAILABLE_MACRO (2, 26)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_26 GLIB_UNAVAILABLE_ENUMERATOR (2, 26)
#define GOBJECT_AVAILABLE_TYPE_IN_2_26 GLIB_UNAVAILABLE_TYPE (2, 26)
#else
#define GOBJECT_AVAILABLE_IN_2_26 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_26
#define GOBJECT_AVAILABLE_MACRO_IN_2_26
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_26
#define GOBJECT_AVAILABLE_TYPE_IN_2_26
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_28
#define GOBJECT_DEPRECATED_IN_2_28 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_28_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_28 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_28_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_28 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_28_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_28 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_28_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_28 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_28_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_28
#define GOBJECT_DEPRECATED_MACRO_IN_2_28_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_28
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_28_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_28
#define GOBJECT_DEPRECATED_TYPE_IN_2_28_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_28
#define GOBJECT_AVAILABLE_IN_2_28 GOBJECT_UNAVAILABLE (2, 28)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_28 GLIB_UNAVAILABLE_STATIC_INLINE (2, 28)
#define GOBJECT_AVAILABLE_MACRO_IN_2_28 GLIB_UNAVAILABLE_MACRO (2, 28)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_28 GLIB_UNAVAILABLE_ENUMERATOR (2, 28)
#define GOBJECT_AVAILABLE_TYPE_IN_2_28 GLIB_UNAVAILABLE_TYPE (2, 28)
#else
#define GOBJECT_AVAILABLE_IN_2_28 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_28
#define GOBJECT_AVAILABLE_MACRO_IN_2_28
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_28
#define GOBJECT_AVAILABLE_TYPE_IN_2_28
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_30
#define GOBJECT_DEPRECATED_IN_2_30 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_30_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_30 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_30_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_30 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_30_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_30 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_30_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_30 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_30_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_30
#define GOBJECT_DEPRECATED_MACRO_IN_2_30_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_30
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_30_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_30
#define GOBJECT_DEPRECATED_TYPE_IN_2_30_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_30
#define GOBJECT_AVAILABLE_IN_2_30 GOBJECT_UNAVAILABLE (2, 30)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_30 GLIB_UNAVAILABLE_STATIC_INLINE (2, 30)
#define GOBJECT_AVAILABLE_MACRO_IN_2_30 GLIB_UNAVAILABLE_MACRO (2, 30)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_30 GLIB_UNAVAILABLE_ENUMERATOR (2, 30)
#define GOBJECT_AVAILABLE_TYPE_IN_2_30 GLIB_UNAVAILABLE_TYPE (2, 30)
#else
#define GOBJECT_AVAILABLE_IN_2_30 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_30
#define GOBJECT_AVAILABLE_MACRO_IN_2_30
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_30
#define GOBJECT_AVAILABLE_TYPE_IN_2_30
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_32
#define GOBJECT_DEPRECATED_IN_2_32 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_32_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_32 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_32_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_32 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_32_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_32 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_32_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_32 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_32_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_32
#define GOBJECT_DEPRECATED_MACRO_IN_2_32_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_32
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_32_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_32
#define GOBJECT_DEPRECATED_TYPE_IN_2_32_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_32
#define GOBJECT_AVAILABLE_IN_2_32 GOBJECT_UNAVAILABLE (2, 32)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_32 GLIB_UNAVAILABLE_STATIC_INLINE (2, 32)
#define GOBJECT_AVAILABLE_MACRO_IN_2_32 GLIB_UNAVAILABLE_MACRO (2, 32)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_32 GLIB_UNAVAILABLE_ENUMERATOR (2, 32)
#define GOBJECT_AVAILABLE_TYPE_IN_2_32 GLIB_UNAVAILABLE_TYPE (2, 32)
#else
#define GOBJECT_AVAILABLE_IN_2_32 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_32
#define GOBJECT_AVAILABLE_MACRO_IN_2_32
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_32
#define GOBJECT_AVAILABLE_TYPE_IN_2_32
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_34
#define GOBJECT_DEPRECATED_IN_2_34 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_34_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_34 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_34_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_34 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_34_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_34 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_34_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_34 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_34_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_34
#define GOBJECT_DEPRECATED_MACRO_IN_2_34_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_34
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_34_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_34
#define GOBJECT_DEPRECATED_TYPE_IN_2_34_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_34
#define GOBJECT_AVAILABLE_IN_2_34 GOBJECT_UNAVAILABLE (2, 34)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_34 GLIB_UNAVAILABLE_STATIC_INLINE (2, 34)
#define GOBJECT_AVAILABLE_MACRO_IN_2_34 GLIB_UNAVAILABLE_MACRO (2, 34)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_34 GLIB_UNAVAILABLE_ENUMERATOR (2, 34)
#define GOBJECT_AVAILABLE_TYPE_IN_2_34 GLIB_UNAVAILABLE_TYPE (2, 34)
#else
#define GOBJECT_AVAILABLE_IN_2_34 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_34
#define GOBJECT_AVAILABLE_MACRO_IN_2_34
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_34
#define GOBJECT_AVAILABLE_TYPE_IN_2_34
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_36
#define GOBJECT_DEPRECATED_IN_2_36 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_36_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_36 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_36_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_36 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_36_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_36 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_36_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_36 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_36_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_36
#define GOBJECT_DEPRECATED_MACRO_IN_2_36_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_36
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_36_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_36
#define GOBJECT_DEPRECATED_TYPE_IN_2_36_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_36
#define GOBJECT_AVAILABLE_IN_2_36 GOBJECT_UNAVAILABLE (2, 36)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_36 GLIB_UNAVAILABLE_STATIC_INLINE (2, 36)
#define GOBJECT_AVAILABLE_MACRO_IN_2_36 GLIB_UNAVAILABLE_MACRO (2, 36)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_36 GLIB_UNAVAILABLE_ENUMERATOR (2, 36)
#define GOBJECT_AVAILABLE_TYPE_IN_2_36 GLIB_UNAVAILABLE_TYPE (2, 36)
#else
#define GOBJECT_AVAILABLE_IN_2_36 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_36
#define GOBJECT_AVAILABLE_MACRO_IN_2_36
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_36
#define GOBJECT_AVAILABLE_TYPE_IN_2_36
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_38
#define GOBJECT_DEPRECATED_IN_2_38 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_38_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_38 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_38_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_38 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_38_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_38 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_38_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_38 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_38_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_38
#define GOBJECT_DEPRECATED_MACRO_IN_2_38_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_38
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_38_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_38
#define GOBJECT_DEPRECATED_TYPE_IN_2_38_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_38
#define GOBJECT_AVAILABLE_IN_2_38 GOBJECT_UNAVAILABLE (2, 38)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_38 GLIB_UNAVAILABLE_STATIC_INLINE (2, 38)
#define GOBJECT_AVAILABLE_MACRO_IN_2_38 GLIB_UNAVAILABLE_MACRO (2, 38)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_38 GLIB_UNAVAILABLE_ENUMERATOR (2, 38)
#define GOBJECT_AVAILABLE_TYPE_IN_2_38 GLIB_UNAVAILABLE_TYPE (2, 38)
#else
#define GOBJECT_AVAILABLE_IN_2_38 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_38
#define GOBJECT_AVAILABLE_MACRO_IN_2_38
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_38
#define GOBJECT_AVAILABLE_TYPE_IN_2_38
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_40
#define GOBJECT_DEPRECATED_IN_2_40 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_40_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_40 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_40_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_40 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_40_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_40 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_40_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_40 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_40_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_40
#define GOBJECT_DEPRECATED_MACRO_IN_2_40_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_40
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_40_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_40
#define GOBJECT_DEPRECATED_TYPE_IN_2_40_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_40
#define GOBJECT_AVAILABLE_IN_2_40 GOBJECT_UNAVAILABLE (2, 40)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_40 GLIB_UNAVAILABLE_STATIC_INLINE (2, 40)
#define GOBJECT_AVAILABLE_MACRO_IN_2_40 GLIB_UNAVAILABLE_MACRO (2, 40)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_40 GLIB_UNAVAILABLE_ENUMERATOR (2, 40)
#define GOBJECT_AVAILABLE_TYPE_IN_2_40 GLIB_UNAVAILABLE_TYPE (2, 40)
#else
#define GOBJECT_AVAILABLE_IN_2_40 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_40
#define GOBJECT_AVAILABLE_MACRO_IN_2_40
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_40
#define GOBJECT_AVAILABLE_TYPE_IN_2_40
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_42
#define GOBJECT_DEPRECATED_IN_2_42 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_42_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_42 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_42_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_42 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_42_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_42 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_42_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_42 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_42_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_42
#define GOBJECT_DEPRECATED_MACRO_IN_2_42_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_42
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_42_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_42
#define GOBJECT_DEPRECATED_TYPE_IN_2_42_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_42
#define GOBJECT_AVAILABLE_IN_2_42 GOBJECT_UNAVAILABLE (2, 42)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_42 GLIB_UNAVAILABLE_STATIC_INLINE (2, 42)
#define GOBJECT_AVAILABLE_MACRO_IN_2_42 GLIB_UNAVAILABLE_MACRO (2, 42)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_42 GLIB_UNAVAILABLE_ENUMERATOR (2, 42)
#define GOBJECT_AVAILABLE_TYPE_IN_2_42 GLIB_UNAVAILABLE_TYPE (2, 42)
#else
#define GOBJECT_AVAILABLE_IN_2_42 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_42
#define GOBJECT_AVAILABLE_MACRO_IN_2_42
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_42
#define GOBJECT_AVAILABLE_TYPE_IN_2_42
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_44
#define GOBJECT_DEPRECATED_IN_2_44 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_44_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_44 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_44_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_44 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_44_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_44 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_44_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_44 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_44_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_44
#define GOBJECT_DEPRECATED_MACRO_IN_2_44_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_44
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_44_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_44
#define GOBJECT_DEPRECATED_TYPE_IN_2_44_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_44
#define GOBJECT_AVAILABLE_IN_2_44 GOBJECT_UNAVAILABLE (2, 44)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_44 GLIB_UNAVAILABLE_STATIC_INLINE (2, 44)
#define GOBJECT_AVAILABLE_MACRO_IN_2_44 GLIB_UNAVAILABLE_MACRO (2, 44)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_44 GLIB_UNAVAILABLE_ENUMERATOR (2, 44)
#define GOBJECT_AVAILABLE_TYPE_IN_2_44 GLIB_UNAVAILABLE_TYPE (2, 44)
#else
#define GOBJECT_AVAILABLE_IN_2_44 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_44
#define GOBJECT_AVAILABLE_MACRO_IN_2_44
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_44
#define GOBJECT_AVAILABLE_TYPE_IN_2_44
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_46
#define GOBJECT_DEPRECATED_IN_2_46 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_46_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_46 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_46_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_46 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_46_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_46 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_46_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_46 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_46_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_46
#define GOBJECT_DEPRECATED_MACRO_IN_2_46_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_46
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_46_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_46
#define GOBJECT_DEPRECATED_TYPE_IN_2_46_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_46
#define GOBJECT_AVAILABLE_IN_2_46 GOBJECT_UNAVAILABLE (2, 46)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_46 GLIB_UNAVAILABLE_STATIC_INLINE (2, 46)
#define GOBJECT_AVAILABLE_MACRO_IN_2_46 GLIB_UNAVAILABLE_MACRO (2, 46)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_46 GLIB_UNAVAILABLE_ENUMERATOR (2, 46)
#define GOBJECT_AVAILABLE_TYPE_IN_2_46 GLIB_UNAVAILABLE_TYPE (2, 46)
#else
#define GOBJECT_AVAILABLE_IN_2_46 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_46
#define GOBJECT_AVAILABLE_MACRO_IN_2_46
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_46
#define GOBJECT_AVAILABLE_TYPE_IN_2_46
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_48
#define GOBJECT_DEPRECATED_IN_2_48 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_48_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_48 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_48_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_48 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_48_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_48 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_48_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_48 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_48_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_48
#define GOBJECT_DEPRECATED_MACRO_IN_2_48_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_48
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_48_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_48
#define GOBJECT_DEPRECATED_TYPE_IN_2_48_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_48
#define GOBJECT_AVAILABLE_IN_2_48 GOBJECT_UNAVAILABLE (2, 48)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_48 GLIB_UNAVAILABLE_STATIC_INLINE (2, 48)
#define GOBJECT_AVAILABLE_MACRO_IN_2_48 GLIB_UNAVAILABLE_MACRO (2, 48)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_48 GLIB_UNAVAILABLE_ENUMERATOR (2, 48)
#define GOBJECT_AVAILABLE_TYPE_IN_2_48 GLIB_UNAVAILABLE_TYPE (2, 48)
#else
#define GOBJECT_AVAILABLE_IN_2_48 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_48
#define GOBJECT_AVAILABLE_MACRO_IN_2_48
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_48
#define GOBJECT_AVAILABLE_TYPE_IN_2_48
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_50
#define GOBJECT_DEPRECATED_IN_2_50 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_50_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_50 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_50_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_50 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_50_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_50 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_50_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_50 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_50_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_50
#define GOBJECT_DEPRECATED_MACRO_IN_2_50_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_50
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_50_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_50
#define GOBJECT_DEPRECATED_TYPE_IN_2_50_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_50
#define GOBJECT_AVAILABLE_IN_2_50 GOBJECT_UNAVAILABLE (2, 50)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_50 GLIB_UNAVAILABLE_STATIC_INLINE (2, 50)
#define GOBJECT_AVAILABLE_MACRO_IN_2_50 GLIB_UNAVAILABLE_MACRO (2, 50)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_50 GLIB_UNAVAILABLE_ENUMERATOR (2, 50)
#define GOBJECT_AVAILABLE_TYPE_IN_2_50 GLIB_UNAVAILABLE_TYPE (2, 50)
#else
#define GOBJECT_AVAILABLE_IN_2_50 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_50
#define GOBJECT_AVAILABLE_MACRO_IN_2_50
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_50
#define GOBJECT_AVAILABLE_TYPE_IN_2_50
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_52
#define GOBJECT_DEPRECATED_IN_2_52 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_52_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_52 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_52_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_52 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_52_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_52 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_52_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_52 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_52_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_52
#define GOBJECT_DEPRECATED_MACRO_IN_2_52_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_52
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_52_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_52
#define GOBJECT_DEPRECATED_TYPE_IN_2_52_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_52
#define GOBJECT_AVAILABLE_IN_2_52 GOBJECT_UNAVAILABLE (2, 52)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_52 GLIB_UNAVAILABLE_STATIC_INLINE (2, 52)
#define GOBJECT_AVAILABLE_MACRO_IN_2_52 GLIB_UNAVAILABLE_MACRO (2, 52)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_52 GLIB_UNAVAILABLE_ENUMERATOR (2, 52)
#define GOBJECT_AVAILABLE_TYPE_IN_2_52 GLIB_UNAVAILABLE_TYPE (2, 52)
#else
#define GOBJECT_AVAILABLE_IN_2_52 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_52
#define GOBJECT_AVAILABLE_MACRO_IN_2_52
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_52
#define GOBJECT_AVAILABLE_TYPE_IN_2_52
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_54
#define GOBJECT_DEPRECATED_IN_2_54 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_54_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_54 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_54_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_54 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_54_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_54 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_54_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_54 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_54_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_54
#define GOBJECT_DEPRECATED_MACRO_IN_2_54_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_54
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_54_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_54
#define GOBJECT_DEPRECATED_TYPE_IN_2_54_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_54
#define GOBJECT_AVAILABLE_IN_2_54 GOBJECT_UNAVAILABLE (2, 54)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_54 GLIB_UNAVAILABLE_STATIC_INLINE (2, 54)
#define GOBJECT_AVAILABLE_MACRO_IN_2_54 GLIB_UNAVAILABLE_MACRO (2, 54)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_54 GLIB_UNAVAILABLE_ENUMERATOR (2, 54)
#define GOBJECT_AVAILABLE_TYPE_IN_2_54 GLIB_UNAVAILABLE_TYPE (2, 54)
#else
#define GOBJECT_AVAILABLE_IN_2_54 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_54
#define GOBJECT_AVAILABLE_MACRO_IN_2_54
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_54
#define GOBJECT_AVAILABLE_TYPE_IN_2_54
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_56
#define GOBJECT_DEPRECATED_IN_2_56 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_56_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_56 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_56_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_56 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_56_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_56 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_56_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_56 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_56_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_56
#define GOBJECT_DEPRECATED_MACRO_IN_2_56_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_56
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_56_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_56
#define GOBJECT_DEPRECATED_TYPE_IN_2_56_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_56
#define GOBJECT_AVAILABLE_IN_2_56 GOBJECT_UNAVAILABLE (2, 56)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_56 GLIB_UNAVAILABLE_STATIC_INLINE (2, 56)
#define GOBJECT_AVAILABLE_MACRO_IN_2_56 GLIB_UNAVAILABLE_MACRO (2, 56)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_56 GLIB_UNAVAILABLE_ENUMERATOR (2, 56)
#define GOBJECT_AVAILABLE_TYPE_IN_2_56 GLIB_UNAVAILABLE_TYPE (2, 56)
#else
#define GOBJECT_AVAILABLE_IN_2_56 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_56
#define GOBJECT_AVAILABLE_MACRO_IN_2_56
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_56
#define GOBJECT_AVAILABLE_TYPE_IN_2_56
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_58
#define GOBJECT_DEPRECATED_IN_2_58 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_58_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_58 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_58_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_58 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_58_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_58 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_58_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_58 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_58_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_58
#define GOBJECT_DEPRECATED_MACRO_IN_2_58_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_58
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_58_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_58
#define GOBJECT_DEPRECATED_TYPE_IN_2_58_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_58
#define GOBJECT_AVAILABLE_IN_2_58 GOBJECT_UNAVAILABLE (2, 58)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_58 GLIB_UNAVAILABLE_STATIC_INLINE (2, 58)
#define GOBJECT_AVAILABLE_MACRO_IN_2_58 GLIB_UNAVAILABLE_MACRO (2, 58)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_58 GLIB_UNAVAILABLE_ENUMERATOR (2, 58)
#define GOBJECT_AVAILABLE_TYPE_IN_2_58 GLIB_UNAVAILABLE_TYPE (2, 58)
#else
#define GOBJECT_AVAILABLE_IN_2_58 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_58
#define GOBJECT_AVAILABLE_MACRO_IN_2_58
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_58
#define GOBJECT_AVAILABLE_TYPE_IN_2_58
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_60
#define GOBJECT_DEPRECATED_IN_2_60 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_60_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_60 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_60_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_60 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_60_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_60 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_60_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_60 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_60_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_60
#define GOBJECT_DEPRECATED_MACRO_IN_2_60_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_60
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_60_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_60
#define GOBJECT_DEPRECATED_TYPE_IN_2_60_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_60
#define GOBJECT_AVAILABLE_IN_2_60 GOBJECT_UNAVAILABLE (2, 60)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_60 GLIB_UNAVAILABLE_STATIC_INLINE (2, 60)
#define GOBJECT_AVAILABLE_MACRO_IN_2_60 GLIB_UNAVAILABLE_MACRO (2, 60)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_60 GLIB_UNAVAILABLE_ENUMERATOR (2, 60)
#define GOBJECT_AVAILABLE_TYPE_IN_2_60 GLIB_UNAVAILABLE_TYPE (2, 60)
#else
#define GOBJECT_AVAILABLE_IN_2_60 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_60
#define GOBJECT_AVAILABLE_MACRO_IN_2_60
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_60
#define GOBJECT_AVAILABLE_TYPE_IN_2_60
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_62
#define GOBJECT_DEPRECATED_IN_2_62 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_62_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_62 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_62_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_62 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_62_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_62 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_62_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_62 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_62_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_62
#define GOBJECT_DEPRECATED_MACRO_IN_2_62_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_62
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_62_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_62
#define GOBJECT_DEPRECATED_TYPE_IN_2_62_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_62
#define GOBJECT_AVAILABLE_IN_2_62 GOBJECT_UNAVAILABLE (2, 62)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_62 GLIB_UNAVAILABLE_STATIC_INLINE (2, 62)
#define GOBJECT_AVAILABLE_MACRO_IN_2_62 GLIB_UNAVAILABLE_MACRO (2, 62)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_62 GLIB_UNAVAILABLE_ENUMERATOR (2, 62)
#define GOBJECT_AVAILABLE_TYPE_IN_2_62 GLIB_UNAVAILABLE_TYPE (2, 62)
#else
#define GOBJECT_AVAILABLE_IN_2_62 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_62
#define GOBJECT_AVAILABLE_MACRO_IN_2_62
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_62
#define GOBJECT_AVAILABLE_TYPE_IN_2_62
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_64
#define GOBJECT_DEPRECATED_IN_2_64 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_64_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_64 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_64_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_64 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_64_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_64 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_64_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_64 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_64_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_64
#define GOBJECT_DEPRECATED_MACRO_IN_2_64_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_64
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_64_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_64
#define GOBJECT_DEPRECATED_TYPE_IN_2_64_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_64
#define GOBJECT_AVAILABLE_IN_2_64 GOBJECT_UNAVAILABLE (2, 64)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_64 GLIB_UNAVAILABLE_STATIC_INLINE (2, 64)
#define GOBJECT_AVAILABLE_MACRO_IN_2_64 GLIB_UNAVAILABLE_MACRO (2, 64)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_64 GLIB_UNAVAILABLE_ENUMERATOR (2, 64)
#define GOBJECT_AVAILABLE_TYPE_IN_2_64 GLIB_UNAVAILABLE_TYPE (2, 64)
#else
#define GOBJECT_AVAILABLE_IN_2_64 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_64
#define GOBJECT_AVAILABLE_MACRO_IN_2_64
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_64
#define GOBJECT_AVAILABLE_TYPE_IN_2_64
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_66
#define GOBJECT_DEPRECATED_IN_2_66 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_66_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_66 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_66_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_66 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_66_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_66 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_66_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_66 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_66_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_66
#define GOBJECT_DEPRECATED_MACRO_IN_2_66_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_66
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_66_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_66
#define GOBJECT_DEPRECATED_TYPE_IN_2_66_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_66
#define GOBJECT_AVAILABLE_IN_2_66 GOBJECT_UNAVAILABLE (2, 66)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_66 GLIB_UNAVAILABLE_STATIC_INLINE (2, 66)
#define GOBJECT_AVAILABLE_MACRO_IN_2_66 GLIB_UNAVAILABLE_MACRO (2, 66)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_66 GLIB_UNAVAILABLE_ENUMERATOR (2, 66)
#define GOBJECT_AVAILABLE_TYPE_IN_2_66 GLIB_UNAVAILABLE_TYPE (2, 66)
#else
#define GOBJECT_AVAILABLE_IN_2_66 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_66
#define GOBJECT_AVAILABLE_MACRO_IN_2_66
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_66
#define GOBJECT_AVAILABLE_TYPE_IN_2_66
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_68
#define GOBJECT_DEPRECATED_IN_2_68 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_68_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_68 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_68_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_68 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_68_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_68 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_68_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_68 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_68_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_68
#define GOBJECT_DEPRECATED_MACRO_IN_2_68_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_68
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_68_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_68
#define GOBJECT_DEPRECATED_TYPE_IN_2_68_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_68
#define GOBJECT_AVAILABLE_IN_2_68 GOBJECT_UNAVAILABLE (2, 68)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_68 GLIB_UNAVAILABLE_STATIC_INLINE (2, 68)
#define GOBJECT_AVAILABLE_MACRO_IN_2_68 GLIB_UNAVAILABLE_MACRO (2, 68)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_68 GLIB_UNAVAILABLE_ENUMERATOR (2, 68)
#define GOBJECT_AVAILABLE_TYPE_IN_2_68 GLIB_UNAVAILABLE_TYPE (2, 68)
#else
#define GOBJECT_AVAILABLE_IN_2_68 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_68
#define GOBJECT_AVAILABLE_MACRO_IN_2_68
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_68
#define GOBJECT_AVAILABLE_TYPE_IN_2_68
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_70
#define GOBJECT_DEPRECATED_IN_2_70 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_70_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_70 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_70_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_70 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_70_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_70 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_70_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_70 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_70_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_70
#define GOBJECT_DEPRECATED_MACRO_IN_2_70_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_70
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_70_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_70
#define GOBJECT_DEPRECATED_TYPE_IN_2_70_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_70
#define GOBJECT_AVAILABLE_IN_2_70 GOBJECT_UNAVAILABLE (2, 70)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_70 GLIB_UNAVAILABLE_STATIC_INLINE (2, 70)
#define GOBJECT_AVAILABLE_MACRO_IN_2_70 GLIB_UNAVAILABLE_MACRO (2, 70)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_70 GLIB_UNAVAILABLE_ENUMERATOR (2, 70)
#define GOBJECT_AVAILABLE_TYPE_IN_2_70 GLIB_UNAVAILABLE_TYPE (2, 70)
#else
#define GOBJECT_AVAILABLE_IN_2_70 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_70
#define GOBJECT_AVAILABLE_MACRO_IN_2_70
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_70
#define GOBJECT_AVAILABLE_TYPE_IN_2_70
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_72
#define GOBJECT_DEPRECATED_IN_2_72 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_72_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_72 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_72_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_72 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_72_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_72 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_72_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_72 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_72_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_72
#define GOBJECT_DEPRECATED_MACRO_IN_2_72_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_72
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_72_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_72
#define GOBJECT_DEPRECATED_TYPE_IN_2_72_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_72
#define GOBJECT_AVAILABLE_IN_2_72 GOBJECT_UNAVAILABLE (2, 72)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_72 GLIB_UNAVAILABLE_STATIC_INLINE (2, 72)
#define GOBJECT_AVAILABLE_MACRO_IN_2_72 GLIB_UNAVAILABLE_MACRO (2, 72)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_72 GLIB_UNAVAILABLE_ENUMERATOR (2, 72)
#define GOBJECT_AVAILABLE_TYPE_IN_2_72 GLIB_UNAVAILABLE_TYPE (2, 72)
#else
#define GOBJECT_AVAILABLE_IN_2_72 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_72
#define GOBJECT_AVAILABLE_MACRO_IN_2_72
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_72
#define GOBJECT_AVAILABLE_TYPE_IN_2_72
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_74
#define GOBJECT_DEPRECATED_IN_2_74 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_74_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_74 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_74_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_74 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_74_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_74 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_74_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_74 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_74_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_74
#define GOBJECT_DEPRECATED_MACRO_IN_2_74_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_74
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_74_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_74
#define GOBJECT_DEPRECATED_TYPE_IN_2_74_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_74
#define GOBJECT_AVAILABLE_IN_2_74 GOBJECT_UNAVAILABLE (2, 74)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_74 GLIB_UNAVAILABLE_STATIC_INLINE (2, 74)
#define GOBJECT_AVAILABLE_MACRO_IN_2_74 GLIB_UNAVAILABLE_MACRO (2, 74)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_74 GLIB_UNAVAILABLE_ENUMERATOR (2, 74)
#define GOBJECT_AVAILABLE_TYPE_IN_2_74 GLIB_UNAVAILABLE_TYPE (2, 74)
#else
#define GOBJECT_AVAILABLE_IN_2_74 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_74
#define GOBJECT_AVAILABLE_MACRO_IN_2_74
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_74
#define GOBJECT_AVAILABLE_TYPE_IN_2_74
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_76
#define GOBJECT_DEPRECATED_IN_2_76 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_76_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_76 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_76_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_76 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_76_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_76 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_76_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_76 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_76_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_76
#define GOBJECT_DEPRECATED_MACRO_IN_2_76_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_76
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_76_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_76
#define GOBJECT_DEPRECATED_TYPE_IN_2_76_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_76
#define GOBJECT_AVAILABLE_IN_2_76 GOBJECT_UNAVAILABLE (2, 76)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_76 GLIB_UNAVAILABLE_STATIC_INLINE (2, 76)
#define GOBJECT_AVAILABLE_MACRO_IN_2_76 GLIB_UNAVAILABLE_MACRO (2, 76)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_76 GLIB_UNAVAILABLE_ENUMERATOR (2, 76)
#define GOBJECT_AVAILABLE_TYPE_IN_2_76 GLIB_UNAVAILABLE_TYPE (2, 76)
#else
#define GOBJECT_AVAILABLE_IN_2_76 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_76
#define GOBJECT_AVAILABLE_MACRO_IN_2_76
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_76
#define GOBJECT_AVAILABLE_TYPE_IN_2_76
#endif

#if GLIB_VERSION_MIN_REQUIRED >= GLIB_VERSION_2_78
#define GOBJECT_DEPRECATED_IN_2_78 GOBJECT_DEPRECATED
#define GOBJECT_DEPRECATED_IN_2_78_FOR(f) GOBJECT_DEPRECATED_FOR (f)
#define GOBJECT_DEPRECATED_MACRO_IN_2_78 GLIB_DEPRECATED_MACRO
#define GOBJECT_DEPRECATED_MACRO_IN_2_78_FOR(f) GLIB_DEPRECATED_MACRO_FOR (f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_78 GLIB_DEPRECATED_ENUMERATOR
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_78_FOR(f) GLIB_DEPRECATED_ENUMERATOR_FOR (f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_78 GLIB_DEPRECATED_TYPE
#define GOBJECT_DEPRECATED_TYPE_IN_2_78_FOR(f) GLIB_DEPRECATED_TYPE_FOR (f)
#else
#define GOBJECT_DEPRECATED_IN_2_78 _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_IN_2_78_FOR(f) _GOBJECT_EXTERN
#define GOBJECT_DEPRECATED_MACRO_IN_2_78
#define GOBJECT_DEPRECATED_MACRO_IN_2_78_FOR(f)
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_78
#define GOBJECT_DEPRECATED_ENUMERATOR_IN_2_78_FOR(f)
#define GOBJECT_DEPRECATED_TYPE_IN_2_78
#define GOBJECT_DEPRECATED_TYPE_IN_2_78_FOR(f)
#endif

#if GLIB_VERSION_MAX_ALLOWED < GLIB_VERSION_2_78
#define GOBJECT_AVAILABLE_IN_2_78 GOBJECT_UNAVAILABLE (2, 78)
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_78 GLIB_UNAVAILABLE_STATIC_INLINE (2, 78)
#define GOBJECT_AVAILABLE_MACRO_IN_2_78 GLIB_UNAVAILABLE_MACRO (2, 78)
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_78 GLIB_UNAVAILABLE_ENUMERATOR (2, 78)
#define GOBJECT_AVAILABLE_TYPE_IN_2_78 GLIB_UNAVAILABLE_TYPE (2, 78)
#else
#define GOBJECT_AVAILABLE_IN_2_78 _GOBJECT_EXTERN
#define GOBJECT_AVAILABLE_STATIC_INLINE_IN_2_78
#define GOBJECT_AVAILABLE_MACRO_IN_2_78
#define GOBJECT_AVAILABLE_ENUMERATOR_IN_2_78
#define GOBJECT_AVAILABLE_TYPE_IN_2_78
#endif
