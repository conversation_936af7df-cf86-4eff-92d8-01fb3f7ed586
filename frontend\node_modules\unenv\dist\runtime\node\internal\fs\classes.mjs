import { notImplementedClass } from "../../../_internal/utils.mjs";
export const Dir = /* @__PURE__ */ notImplementedClass("fs.Dir");
export const Dirent = /* @__PURE__ */ notImplementedClass("fs.Dirent");
export const Stats = /* @__PURE__ */ notImplementedClass("fs.Stats");
export const ReadStream = /* @__PURE__ */ notImplementedClass("fs.ReadStream");
export const WriteStream = /* @__PURE__ */ notImplementedClass("fs.WriteStream");
export const FileReadStream = ReadStream;
export const FileWriteStream = WriteStream;
