import { MergeDetail, <PERSON>rge<PERSON><PERSON><PERSON> } from '../../../typings';
import { TaskParser } from '../types';
/**
 * Parse the complete response from `git.merge`
 */
export declare const parseMergeResult: TaskParser<string, MergeResult>;
/**
 * Parse the merge specific detail (ie: not the content also available in the pull detail) from `git.mnerge`
 * @param stdOut
 */
export declare const parseMergeDetail: TaskParser<string, MergeDetail>;
