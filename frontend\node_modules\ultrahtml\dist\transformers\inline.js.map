{"version": 3, "sources": ["../../src/transformers/inline.ts", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Enum.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Utility.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Tokenizer.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Parser.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/ast/ast.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/flatten/flatten.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/internals.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/lexer/codepoints.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/lexer/process.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/lexer/tokens.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/lexer/lexer.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/utils.js", "../../node_modules/.pnpm/media-query-parser@3.0.0-beta.1/node_modules/media-query-parser/dist/index.js", "../../node_modules/.pnpm/media-query-fns@2.0.0/node_modules/media-query-fns/dist/helpers.js", "../../node_modules/.pnpm/media-query-fns@2.0.0/node_modules/media-query-fns/dist/units.js", "../../node_modules/.pnpm/media-query-fns@2.0.0/node_modules/media-query-fns/dist/compile.js", "../../node_modules/.pnpm/media-query-fns@2.0.0/node_modules/media-query-fns/dist/matches.js"], "sourcesContent": ["import { walkSync, ELEMENT_NODE, TEXT_NODE, Node } from '../index.js';\nimport { querySelectorAll, specificity } from '../selector.js';\nimport { type Element as CSSEntry, compile } from 'stylis';\nimport { compileQuery, matches, type Environment } from 'media-query-fns';\n\nexport interface InlineOptions {\n\t/** Emit `style` attributes as objects rather than strings. */\n\tuseObjectSyntax: boolean;\n\tenv: Partial<Environment> & { width: number; height: number };\n}\nexport default function inline(opts?: Partial<InlineOptions>) {\n\tconst { useObjectSyntax = false } = opts ?? {};\n\treturn (doc: Node): Node => {\n\t\tconst style: string[] = useObjectSyntax ? [':where([style]) {}'] : [];\n\t\tconst actions: (() => void)[] = [];\n\t\twalkSync(doc, (node: Node, parent?: Node) => {\n\t\t\tif (node.type === ELEMENT_NODE) {\n\t\t\t\tif (node.name === 'style') {\n\t\t\t\t\tstyle.push(\n\t\t\t\t\t\tnode.children\n\t\t\t\t\t\t\t.map((c: Node) => (c.type === TEXT_NODE ? c.value : ''))\n\t\t\t\t\t\t\t.join(''),\n\t\t\t\t\t);\n\t\t\t\t\tactions.push(() => {\n\t\t\t\t\t\tparent!.children = parent!.children.filter((c: Node) => c !== node);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\tfor (const action of actions) {\n\t\t\taction();\n\t\t}\n\t\tconst styles = style.join('\\n');\n\t\tconst css = compile(styles);\n\t\tconst selectors = new Map<string, Record<string, string>>();\n\n\t\tfunction applyRule(rule: CSSEntry) {\n\t\t\tif (rule.type === 'rule') {\n\t\t\t\tconst rules = Object.fromEntries(\n\t\t\t\t\t(rule.children as unknown as Element[]).map((child: any) => [\n\t\t\t\t\t\tchild.props,\n\t\t\t\t\t\tchild.children,\n\t\t\t\t\t]),\n\t\t\t\t);\n\t\t\t\tfor (const selector of rule.props) {\n\t\t\t\t\tconst value = Object.assign(selectors.get(selector) ?? {}, rules);\n\t\t\t\t\tselectors.set(selector, value);\n\t\t\t\t}\n\t\t\t} else if (rule.type === '@media' && opts?.env) {\n\t\t\t\tconst env = getEnvironment(opts.env);\n\t\t\t\tconst args = Array.isArray(rule.props) ? rule.props : [rule.props];\n\t\t\t\tconst queries = args.map((arg) => compileQuery(arg));\n\t\t\t\tfor (const query of queries) {\n\t\t\t\t\tif (matches(query, env)) {\n\t\t\t\t\t\tfor (const child of rule.children) {\n\t\t\t\t\t\t\tapplyRule(child as CSSEntry);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tfor (const rule of css) {\n\t\t\tapplyRule(rule);\n\t\t}\n\t\tconst rules = new Map<Node, Record<string, string>>();\n\t\tfor (const [selector, styles] of Array.from(selectors).sort(([a], [b]) => {\n\t\t\tconst $a = specificity(a);\n\t\t\tconst $b = specificity(b);\n\t\t\tif ($a > $b) return 1;\n\t\t\tif ($b > $a) return -1;\n\t\t\treturn 0;\n\t\t})) {\n\t\t\tconst nodes = querySelectorAll(doc, selector);\n\t\t\tfor (const node of nodes) {\n\t\t\t\tconst curr = rules.get(node) ?? {};\n\t\t\t\trules.set(node, Object.assign(curr, styles));\n\t\t\t}\n\t\t}\n\n\t\tfor (const [node, rule] of rules) {\n\t\t\tlet style = node.attributes.style ?? '';\n\t\t\tlet styleObj: Record<string, string> = {};\n\t\t\tfor (const decl of compile(style)) {\n\t\t\t\tif (decl.type === 'decl') {\n\t\t\t\t\tif (\n\t\t\t\t\t\ttypeof decl.props === 'string' &&\n\t\t\t\t\t\ttypeof decl.children === 'string'\n\t\t\t\t\t) {\n\t\t\t\t\t\tstyleObj[decl.props] = decl.children;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tstyleObj = Object.assign({}, rule, styleObj);\n\t\t\tif (useObjectSyntax) {\n\t\t\t\tnode.attributes.style = styleObj;\n\t\t\t} else {\n\t\t\t\tnode.attributes.style = `${Object.entries(styleObj)\n\t\t\t\t\t.map(([decl, value]) => `${decl}:${value.replace('!important', '')};`)\n\t\t\t\t\t.join('')}`;\n\t\t\t}\n\t\t}\n\t\treturn doc;\n\t};\n}\n\ntype AlwaysDefinedValues =\n\t| 'widthPx'\n\t| 'heightPx'\n\t| 'deviceWidthPx'\n\t| 'deviceHeightPx'\n\t| 'dppx';\ntype ResolvedEnvironment = Omit<Partial<Environment>, AlwaysDefinedValues> &\n\tRecord<AlwaysDefinedValues, number>;\nfunction getEnvironment(baseEnv: InlineOptions['env']): ResolvedEnvironment {\n\tconst {\n\t\twidth,\n\t\theight,\n\t\tdppx = 1,\n\t\twidthPx = width,\n\t\theightPx = height,\n\t\tdeviceWidthPx = width * dppx,\n\t\tdeviceHeightPx = height * dppx,\n\t\t...env\n\t} = baseEnv;\n\treturn {\n\t\twidthPx,\n\t\theightPx,\n\t\tdeviceWidthPx,\n\t\tdeviceHeightPx,\n\t\tdppx,\n\t\t...env,\n\t};\n}\n", "export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, token, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tif ((token(previous || 1) == 5 || token(peek() || 1) == 5) && strlen(characters) && substr(characters, -1, void 0) !== ' ') characters += ' '\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length || (variable === 0 && previous === 47)))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "export const isParserError = e => 'object' == typeof e && null !== e && 'errid' in e\nexport const splitMediaQueryList = e => {\n\tconst t = [[]],\n\t\tr = []\n\tfor (const n of e)\n\t\tif ('comma' === n.type && 0 === r.length) t.push([])\n\t\telse {\n\t\t\tswitch (n.type) {\n\t\t\t\tcase 'function':\n\t\t\t\tcase '(':\n\t\t\t\t\tr.push(')')\n\t\t\t\t\tbreak\n\t\t\t\tcase '[':\n\t\t\t\t\tr.push(']')\n\t\t\t\t\tbreak\n\t\t\t\tcase '{':\n\t\t\t\t\tr.push('}')\n\t\t\t\t\tbreak\n\t\t\t\tcase ')':\n\t\t\t\tcase ']':\n\t\t\t\tcase '}':\n\t\t\t\t\tr.at(-1) === n.type && r.pop()\n\t\t\t\t\tbreak\n\t\t\t}\n\t\t\tt[t.length - 1].push(n)\n\t\t}\n\treturn t\n}\nexport const readMediaQueryList = e => {\n\tconst t = splitMediaQueryList(e)\n\tif (1 === t.length && 0 === t[0].length)\n\t\treturn {type: 'query-list', mediaQueries: [{type: 'query'}]}\n\t{\n\t\tconst e = []\n\t\tfor (const r of t) {\n\t\t\tconst t = readMediaQuery(r)\n\t\t\tisParserError(t) ? e.push({type: 'query', prefix: 'not'}) : e.push(t)\n\t\t}\n\t\treturn {type: 'query-list', mediaQueries: e}\n\t}\n}\nexport const readMediaQuery = e => {\n\tvar t, r, n\n\tconst i = e.at(0)\n\tif (i) {\n\t\tif ('(' === i.type) {\n\t\t\tconst r = readMediaCondition(e, !0)\n\t\t\tif (isParserError(r)) {\n\t\t\t\tconst {start: n, end: a} = null !== (t = e.at(1)) && void 0 !== t ? t : i\n\t\t\t\treturn {errid: 'EXPECT_FEATURE_OR_CONDITION', start: n, end: a, child: r}\n\t\t\t}\n\t\t\treturn {type: 'query', mediaCondition: r}\n\t\t}\n\t\tif ('ident' === i.type) {\n\t\t\tlet t, a\n\t\t\tconst {value: d, end: s} = i\n\t\t\t;('only' !== d && 'not' !== d) || (t = d)\n\t\t\tconst o = void 0 === t ? 0 : 1,\n\t\t\t\tl = e.at(o)\n\t\t\tif (!l) return {errid: 'EXPECT_LPAREN_OR_TYPE', start: s, end: s}\n\t\t\tif ('ident' !== l.type) {\n\t\t\t\tif ('not' === t && '(' === l.type) {\n\t\t\t\t\tconst t = readMediaCondition(e.slice(o), !0)\n\t\t\t\t\tif (isParserError(t)) {\n\t\t\t\t\t\tconst {start: n, end: i} = null !== (r = e.at(o + 1)) && void 0 !== r ? r : l\n\t\t\t\t\t\treturn {errid: 'EXPECT_CONDITION', start: n, end: i, child: t}\n\t\t\t\t\t}\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'query',\n\t\t\t\t\t\tmediaCondition: {type: 'condition', operator: 'not', children: [t]}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t{\n\t\t\t\t\tconst {start: e, end: t} = l\n\t\t\t\t\treturn {errid: 'EXPECT_TYPE', start: e, end: t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t{\n\t\t\t\tconst {value: e, start: r, end: n} = l\n\t\t\t\tif ('all' === e) a = void 0\n\t\t\t\telse if ('print' === e || 'screen' === e) a = e\n\t\t\t\telse {\n\t\t\t\t\tif (\n\t\t\t\t\t\t'tty' !== e &&\n\t\t\t\t\t\t'tv' !== e &&\n\t\t\t\t\t\t'projection' !== e &&\n\t\t\t\t\t\t'handheld' !== e &&\n\t\t\t\t\t\t'braille' !== e &&\n\t\t\t\t\t\t'embossed' !== e &&\n\t\t\t\t\t\t'aural' !== e &&\n\t\t\t\t\t\t'speech' !== e\n\t\t\t\t\t)\n\t\t\t\t\t\treturn {errid: 'EXPECT_TYPE', start: r, end: n}\n\t\t\t\t\t;(t = 'not' === t ? void 0 : 'not'), (a = void 0)\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (o + 1 === e.length) return {type: 'query', prefix: t, mediaType: a}\n\t\t\t{\n\t\t\t\tconst r = e[o + 1]\n\t\t\t\tif ('ident' === r.type && 'and' === r.value) {\n\t\t\t\t\tconst r = e.at(-1),\n\t\t\t\t\t\ti = e.at(o + 2)\n\t\t\t\t\tlet d,\n\t\t\t\t\t\ts = r.end + 1\n\t\t\t\t\tif ('ident' === (null == i ? void 0 : i.type) && 'not' === i.value) {\n\t\t\t\t\t\ts += 1\n\t\t\t\t\t\tconst t = readMediaCondition(e.slice(o + 3), !1)\n\t\t\t\t\t\td = isParserError(t) ? t : {type: 'condition', operator: 'not', children: [t]}\n\t\t\t\t\t} else d = readMediaCondition(e.slice(o + 2), !1)\n\t\t\t\t\tconst {start: l, end: u} =\n\t\t\t\t\t\tnull !== (n = e.at(o + 2)) && void 0 !== n ? n : {start: s, end: s}\n\t\t\t\t\treturn isParserError(d)\n\t\t\t\t\t\t? {errid: 'EXPECT_CONDITION', start: l, end: u, child: d}\n\t\t\t\t\t\t: {type: 'query', prefix: t, mediaType: a, mediaCondition: d}\n\t\t\t\t}\n\t\t\t\treturn {errid: 'EXPECT_AND', start: r.start, end: r.end}\n\t\t\t}\n\t\t}\n\t\treturn {errid: 'EXPECT_LPAREN_OR_TYPE_OR_MODIFIER', start: i.start, end: i.end}\n\t}\n\treturn {errid: 'EMPTY_QUERY', start: 0, end: 0}\n}\nexport const readMediaCondition = (e, t, r) => {\n\tconst n = e.at(0)\n\tif (n) {\n\t\tif ('(' !== n.type) return {errid: 'EXPECT_LPAREN', start: n.start, end: n.end}\n\t\tlet i,\n\t\t\ta = e.length - 1,\n\t\t\td = 0,\n\t\t\ts = 0\n\t\tfor (const [t, r] of e.entries())\n\t\t\tif (\n\t\t\t\t('(' === r.type ? ((s += 1), (d = Math.max(d, s))) : ')' === r.type && (s -= 1), 0 === s)\n\t\t\t) {\n\t\t\t\ta = t\n\t\t\t\tbreak\n\t\t\t}\n\t\tif (0 !== s) return {errid: 'MISMATCH_PARENS', start: n.start, end: e[e.length - 1].end}\n\t\tconst o = e.slice(0, a + 1)\n\t\tif (\n\t\t\t((i =\n\t\t\t\t1 === d\n\t\t\t\t\t? readMediaFeature(o)\n\t\t\t\t\t: 'ident' === o[1].type && 'not' === o[1].value\n\t\t\t\t\t? readMediaCondition(o.slice(2, -1), !0, 'not')\n\t\t\t\t\t: readMediaCondition(o.slice(1, -1), !0)),\n\t\t\tisParserError(i))\n\t\t)\n\t\t\treturn {\n\t\t\t\terrid: 'EXPECT_FEATURE_OR_CONDITION',\n\t\t\t\tstart: n.start,\n\t\t\t\tend: o[o.length - 1].end,\n\t\t\t\tchild: i\n\t\t\t}\n\t\tif (a === e.length - 1) return {type: 'condition', operator: r, children: [i]}\n\t\t{\n\t\t\tconst n = e[a + 1]\n\t\t\tif ('ident' !== n.type || ('and' !== n.value && 'or' !== n.value))\n\t\t\t\treturn {errid: 'EXPECT_AND_OR_OR', start: n.start, end: n.end}\n\t\t\tif (void 0 !== r && r !== n.value)\n\t\t\t\treturn {errid: 'MIX_AND_WITH_OR', start: n.start, end: n.end}\n\t\t\tif ('or' === n.value && !t) return {errid: 'MIX_AND_WITH_OR', start: n.start, end: n.end}\n\t\t\tconst d = readMediaCondition(e.slice(a + 2), t, n.value)\n\t\t\treturn isParserError(d)\n\t\t\t\t? d\n\t\t\t\t: {type: 'condition', operator: n.value, children: [i, ...d.children]}\n\t\t}\n\t}\n\treturn {errid: 'EMPTY_CONDITION', start: 0, end: 0}\n}\nexport const readMediaFeature = e => {\n\tconst t = e.at(0)\n\tif (t) {\n\t\tif ('(' !== t.type) return {errid: 'EXPECT_LPAREN', start: t.start, end: t.end}\n\t\tconst r = e[e.length - 1]\n\t\tif (')' !== r.type) return {errid: 'EXPECT_RPAREN', start: r.end + 1, end: r.end + 1}\n\t\tconst n = [e[0]]\n\t\tfor (let t = 1; t < e.length; t++) {\n\t\t\tif (t < e.length - 2) {\n\t\t\t\tconst r = e[t],\n\t\t\t\t\ti = e[t + 1],\n\t\t\t\t\ta = e[t + 2]\n\t\t\t\tif (\n\t\t\t\t\t'number' === r.type &&\n\t\t\t\t\tr.value > 0 &&\n\t\t\t\t\t'delim' === i.type &&\n\t\t\t\t\t47 === i.value &&\n\t\t\t\t\t'number' === a.type &&\n\t\t\t\t\ta.value > 0\n\t\t\t\t) {\n\t\t\t\t\tn.push({\n\t\t\t\t\t\ttype: 'ratio',\n\t\t\t\t\t\tnumerator: r.value,\n\t\t\t\t\t\tdenominator: a.value,\n\t\t\t\t\t\thasSpaceBefore: r.hasSpaceBefore,\n\t\t\t\t\t\thasSpaceAfter: a.hasSpaceAfter,\n\t\t\t\t\t\tstart: r.start,\n\t\t\t\t\t\tend: a.end\n\t\t\t\t\t}),\n\t\t\t\t\t\t(t += 2)\n\t\t\t\t\tcontinue\n\t\t\t\t}\n\t\t\t}\n\t\t\tn.push(e[t])\n\t\t}\n\t\tconst i = n[1]\n\t\tif ('ident' === i.type && 3 === n.length)\n\t\t\treturn {type: 'feature', context: 'boolean', feature: i.value}\n\t\tif (5 === n.length && 'ident' === n[1].type && 'colon' === n[2].type) {\n\t\t\tconst e = n[3]\n\t\t\tif (\n\t\t\t\t'number' === e.type ||\n\t\t\t\t'dimension' === e.type ||\n\t\t\t\t'ratio' === e.type ||\n\t\t\t\t'ident' === e.type\n\t\t\t) {\n\t\t\t\tlet t,\n\t\t\t\t\tr = n[1].value\n\t\t\t\tconst i = r.slice(0, 4)\n\t\t\t\t'min-' === i\n\t\t\t\t\t? ((t = 'min'), (r = r.slice(4)))\n\t\t\t\t\t: 'max-' === i && ((t = 'max'), (r = r.slice(4)))\n\t\t\t\tconst {hasSpaceBefore: a, hasSpaceAfter: d, start: s, end: o, ...l} = e\n\t\t\t\treturn {type: 'feature', context: 'value', prefix: t, feature: r, value: l}\n\t\t\t}\n\t\t\treturn {errid: 'EXPECT_VALUE', start: e.start, end: e.end}\n\t\t}\n\t\tif (n.length >= 5) {\n\t\t\tconst e = readRange(n)\n\t\t\tif (isParserError(e))\n\t\t\t\treturn {errid: 'EXPECT_RANGE', start: t.start, end: n[n.length - 1].end, child: e}\n\t\t\t{\n\t\t\t\tconst {feature: t, ...r} = e\n\t\t\t\treturn {type: 'feature', context: 'range', feature: t, range: r}\n\t\t\t}\n\t\t}\n\t\treturn {errid: 'INVALID_FEATURE', start: t.start, end: e[e.length - 1].end}\n\t}\n\treturn {errid: 'EMPTY_FEATURE', start: 0, end: 0}\n}\nexport const readRange = e => {\n\tvar t, r, n, i, a, d, s, o\n\tif (e.length < 5)\n\t\treturn {\n\t\t\terrid: 'INVALID_RANGE',\n\t\t\tstart:\n\t\t\t\tnull !== (r = null === (t = e.at(0)) || void 0 === t ? void 0 : t.start) && void 0 !== r\n\t\t\t\t\t? r\n\t\t\t\t\t: 0,\n\t\t\tend:\n\t\t\t\tnull !== (i = null === (n = e.at(-1)) || void 0 === n ? void 0 : n.end) && void 0 !== i\n\t\t\t\t\t? i\n\t\t\t\t\t: 0\n\t\t}\n\tif ('(' !== e[0].type) return {errid: 'EXPECT_LPAREN', start: e[0].start, end: e[0].end}\n\tconst l = e[e.length - 1]\n\tif (')' !== l.type) return {errid: 'EXPECT_RPAREN', start: l.start, end: l.end}\n\tconst u = {feature: ''},\n\t\tp =\n\t\t\t'number' === e[1].type ||\n\t\t\t'dimension' === e[1].type ||\n\t\t\t'ratio' === e[1].type ||\n\t\t\t('ident' === e[1].type && 'infinite' === e[1].value)\n\tif ('delim' === e[2].type) {\n\t\tif (60 === e[2].value)\n\t\t\t'delim' !== e[3].type || 61 !== e[3].value || e[3].hasSpaceBefore\n\t\t\t\t? (u[p ? 'leftOp' : 'rightOp'] = '<')\n\t\t\t\t: (u[p ? 'leftOp' : 'rightOp'] = '<=')\n\t\telse if (62 === e[2].value)\n\t\t\t'delim' !== e[3].type || 61 !== e[3].value || e[3].hasSpaceBefore\n\t\t\t\t? (u[p ? 'leftOp' : 'rightOp'] = '>')\n\t\t\t\t: (u[p ? 'leftOp' : 'rightOp'] = '>=')\n\t\telse {\n\t\t\tif (61 !== e[2].value) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\tu[p ? 'leftOp' : 'rightOp'] = '='\n\t\t}\n\t\tif (p) u.leftToken = e[1]\n\t\telse {\n\t\t\tif ('ident' !== e[1].type) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\tu.feature = e[1].value\n\t\t}\n\t\tconst t =\n\t\t\t\t2 +\n\t\t\t\t(null !==\n\t\t\t\t\t(d = null === (a = u[p ? 'leftOp' : 'rightOp']) || void 0 === a ? void 0 : a.length) &&\n\t\t\t\tvoid 0 !== d\n\t\t\t\t\t? d\n\t\t\t\t\t: 0),\n\t\t\tr = e[t]\n\t\tif (p) {\n\t\t\tif ('ident' !== r.type) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\tif (((u.feature = r.value), e.length >= 7)) {\n\t\t\t\tconst r = e[t + 1],\n\t\t\t\t\tn = e[t + 2]\n\t\t\t\tif ('delim' !== r.type) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\t\t{\n\t\t\t\t\tconst i = r.value\n\t\t\t\t\tif (60 === i)\n\t\t\t\t\t\t'delim' !== n.type || 61 !== n.value || n.hasSpaceBefore\n\t\t\t\t\t\t\t? (u.rightOp = '<')\n\t\t\t\t\t\t\t: (u.rightOp = '<=')\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (62 !== i) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\t\t\t\t'delim' !== n.type || 61 !== n.value || n.hasSpaceBefore\n\t\t\t\t\t\t\t? (u.rightOp = '>')\n\t\t\t\t\t\t\t: (u.rightOp = '>=')\n\t\t\t\t\t}\n\t\t\t\t\tconst a =\n\t\t\t\t\t\t\tt +\n\t\t\t\t\t\t\t1 +\n\t\t\t\t\t\t\t(null !== (o = null === (s = u.rightOp) || void 0 === s ? void 0 : s.length) &&\n\t\t\t\t\t\t\tvoid 0 !== o\n\t\t\t\t\t\t\t\t? o\n\t\t\t\t\t\t\t\t: 0),\n\t\t\t\t\t\td = e.at(a)\n\t\t\t\t\tif (a + 2 !== e.length) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\t\t\tu.rightToken = d\n\t\t\t\t}\n\t\t\t} else if (t + 2 !== e.length) return {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t} else u.rightToken = r\n\t\tlet n\n\t\tconst {leftToken: i, leftOp: f, feature: c, rightOp: y, rightToken: h} = u\n\t\tlet E, v\n\t\tif (void 0 !== i)\n\t\t\tif ('ident' === i.type) {\n\t\t\t\tconst {type: e, value: t} = i\n\t\t\t\t'infinite' === t && (E = {type: e, value: t})\n\t\t\t} else if ('number' === i.type || 'dimension' === i.type || 'ratio' === i.type) {\n\t\t\t\tconst {hasSpaceBefore: e, hasSpaceAfter: t, start: r, end: n, ...a} = i\n\t\t\t\tE = a\n\t\t\t}\n\t\tif (void 0 !== h)\n\t\t\tif ('ident' === h.type) {\n\t\t\t\tconst {type: e, value: t} = h\n\t\t\t\t'infinite' === t && (v = {type: e, value: t})\n\t\t\t} else if ('number' === h.type || 'dimension' === h.type || 'ratio' === h.type) {\n\t\t\t\tconst {hasSpaceBefore: e, hasSpaceAfter: t, start: r, end: n, ...i} = h\n\t\t\t\tv = i\n\t\t\t}\n\t\tif (void 0 !== E && void 0 !== v)\n\t\t\tif (('<' !== f && '<=' !== f) || ('<' !== y && '<=' !== y)) {\n\t\t\t\tif (('>' !== f && '>=' !== f) || ('>' !== y && '>=' !== y))\n\t\t\t\t\treturn {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t\t\t\tn = {leftToken: E, leftOp: f, feature: c, rightOp: y, rightToken: v}\n\t\t\t} else n = {leftToken: E, leftOp: f, feature: c, rightOp: y, rightToken: v}\n\t\telse\n\t\t\t((void 0 === E && void 0 === f && void 0 !== y && void 0 !== v) ||\n\t\t\t\t(void 0 !== E && void 0 !== f && void 0 === y && void 0 === v)) &&\n\t\t\t\t(n = {leftToken: E, leftOp: f, feature: c, rightOp: y, rightToken: v})\n\t\treturn null != n ? n : {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n\t}\n\treturn {errid: 'INVALID_RANGE', start: e[0].start, end: l.end}\n}\n", "export const flattenMediaQueryList = e => ({\n\ttype: 'query-list',\n\tmediaQueries: e.mediaQueries.map(e => flattenMediaQuery(e))\n})\nexport const flattenMediaQuery = e =>\n\te.mediaCondition\n\t\t? {\n\t\t\t\ttype: 'query',\n\t\t\t\tprefix: e.prefix,\n\t\t\t\tmediaType: e.mediaType,\n\t\t\t\tmediaCondition: flattenMediaCondition(e.mediaCondition)\n\t\t  }\n\t\t: e\nexport const flattenMediaCondition = e => {\n\tconst o = []\n\tfor (const t of e.children)\n\t\tif ('condition' === t.type) {\n\t\t\tconst i = flattenMediaCondition(t)\n\t\t\tvoid 0 === i.operator && 1 === i.children.length\n\t\t\t\t? o.push(i.children[0])\n\t\t\t\t: i.operator !== e.operator || ('and' !== i.operator && 'or' !== i.operator)\n\t\t\t\t? o.push(i)\n\t\t\t\t: o.push(...i.children)\n\t\t} else o.push(t)\n\tif (1 === o.length) {\n\t\tconst t = o[0]\n\t\tif ('condition' === t.type) {\n\t\t\tif (void 0 === e.operator) return t\n\t\t\tif ('not' === e.operator && 'not' === t.operator)\n\t\t\t\treturn {type: 'condition', children: t.children}\n\t\t}\n\t}\n\treturn {type: 'condition', operator: e.operator, children: o}\n}\n", "export const invertParserError = e => {\n\tconst t = [e]\n\tfor (let d = e.child; void 0 !== d; d = d.child) t.push(d)\n\tfor (let e = t.length - 2; e >= 0; e--) t[e + 1].child = t.at(e)\n\treturn delete t[0].child, t.at(-1)\n}\nexport const deleteUndefinedValues = e => {\n\tswitch (e.type) {\n\t\tcase 'query-list':\n\t\t\tfor (const t of e.mediaQueries) deleteUndefinedValues(t)\n\t\t\treturn e\n\t\tcase 'query':\n\t\t\treturn (\n\t\t\t\tvoid 0 === e.prefix && delete e.prefix,\n\t\t\t\tvoid 0 === e.mediaType && delete e.mediaType,\n\t\t\t\tvoid 0 === e.mediaCondition\n\t\t\t\t\t? delete e.mediaCondition\n\t\t\t\t\t: deleteUndefinedValues(e.mediaCondition),\n\t\t\t\te\n\t\t\t)\n\t\tcase 'condition':\n\t\t\tvoid 0 === e.operator && delete e.operator\n\t\t\tfor (const t of e.children) deleteUndefinedValues(t)\n\t\t\treturn e\n\t\tcase 'feature':\n\t\t\treturn (\n\t\t\t\t'value' === e.context\n\t\t\t\t\t? (void 0 === e.prefix && delete e.prefix, deleteUndefinedValues(e.value))\n\t\t\t\t\t: 'range' === e.context &&\n\t\t\t\t\t  (void 0 === e.range.leftOp && delete e.range.leftOp,\n\t\t\t\t\t  void 0 === e.range.rightOp && delete e.range.rightOp,\n\t\t\t\t\t  void 0 === e.range.leftToken\n\t\t\t\t\t\t\t? delete e.range.leftToken\n\t\t\t\t\t\t\t: deleteUndefinedValues(e.range.leftToken),\n\t\t\t\t\t  void 0 === e.range.rightToken\n\t\t\t\t\t\t\t? delete e.range.rightToken\n\t\t\t\t\t\t\t: deleteUndefinedValues(e.range.rightToken)),\n\t\t\t\te\n\t\t\t)\n\t\tdefault:\n\t\t\treturn e\n\t}\n}\n", "let e\nexport const readCodepoints = s => {\n\tconst t = (() => {\n\t\t\tlet s\n\t\t\treturn e ? (s = e) : ((s = new TextEncoder()), (e = s)), s\n\t\t})().encode(s),\n\t\tr = [],\n\t\ta = t.length\n\tfor (let e = 0; e < a; e += 1) {\n\t\tconst s = t.at(e)\n\t\tif (s < 128)\n\t\t\tswitch (s) {\n\t\t\t\tcase 0:\n\t\t\t\t\tr.push(65533)\n\t\t\t\t\tbreak\n\t\t\t\tcase 12:\n\t\t\t\t\tr.push(10)\n\t\t\t\t\tbreak\n\t\t\t\tcase 13:\n\t\t\t\t\tr.push(10), 10 === t.at(e + 1) && (e += 1)\n\t\t\t\t\tbreak\n\t\t\t\tdefault:\n\t\t\t\t\tr.push(s)\n\t\t\t}\n\t\telse\n\t\t\ts < 224\n\t\t\t\t? r.push(((s << 59) >>> 53) | ((t[++e] << 58) >>> 58))\n\t\t\t\t: s < 240\n\t\t\t\t? r.push(((s << 60) >>> 48) | ((t[++e] << 58) >>> 52) | ((t[++e] << 58) >>> 58))\n\t\t\t\t: r.push(\n\t\t\t\t\t\t((s << 61) >>> 43) |\n\t\t\t\t\t\t\t((t[++e] << 58) >>> 46) |\n\t\t\t\t\t\t\t((t[++e] << 58) >>> 52) |\n\t\t\t\t\t\t\t((t[++e] << 58) >>> 58)\n\t\t\t\t  )\n\t}\n\treturn r\n}\n", "export const convertToParserTokens = e => {\n\tconst t = []\n\tlet r = !1\n\tfor (const s of e)\n\t\tswitch (s.type) {\n\t\t\tcase '{':\n\t\t\t\treturn {errid: 'NO_LCURLY', start: s.start, end: s.end}\n\t\t\tcase 'semicolon':\n\t\t\t\treturn {errid: 'NO_SEMICOLON', start: s.start, end: s.end}\n\t\t\tcase 'whitespace':\n\t\t\t\t;(r = !0), t.length > 0 && (t[t.length - 1].hasSpaceAfter = !0)\n\t\t\t\tbreak\n\t\t\tcase 'EOF':\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\tt.push({...s, hasSpaceBefore: r, hasSpaceAfter: !1}), (r = !1)\n\t\t}\n\treturn t\n}\n", "const e = 10,\n\tt = 32,\n\tn = 45,\n\ts = 48,\n\tu = 57,\n\tr = 65,\n\to = 92,\n\tl = 97,\n\ti = 122,\n\ta = 128\nexport const codepointsToTokens = (f, c = 0) => {\n\tconst p = []\n\tfor (; c < f.length; c += 1) {\n\t\tconst d = f.at(c),\n\t\t\th = c\n\t\tif (47 === d && 42 === f.at(c + 1)) {\n\t\t\tc += 2\n\t\t\tfor (let e = f.at(c); void 0 !== e; e = f.at(++c))\n\t\t\t\tif (42 === e && 47 === f.at(c + 1)) {\n\t\t\t\t\tc += 1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t} else if (9 === d || d === t || d === e) {\n\t\t\tlet n = f.at(++c)\n\t\t\tfor (; 9 === n || n === t || n === e; ) n = f.at(++c)\n\t\t\tc -= 1\n\t\t\tconst s = p.at(-1)\n\t\t\t'whitespace' === (null == s ? void 0 : s.type)\n\t\t\t\t? (p.pop(), p.push({type: 'whitespace', start: s.start, end: c}))\n\t\t\t\t: p.push({type: 'whitespace', start: h, end: c})\n\t\t} else if (34 === d) {\n\t\t\tconst e = consumeString(f, c)\n\t\t\tif (null === e) return {errid: 'INVALID_STRING', start: c, end: c}\n\t\t\tconst [t, n] = e\n\t\t\t;(c = t), p.push({type: 'string', value: n, start: h, end: c})\n\t\t} else if (35 === d) {\n\t\t\tif (c + 1 < f.length) {\n\t\t\t\tconst t = f.at(c + 1)\n\t\t\t\tif (\n\t\t\t\t\t95 === t ||\n\t\t\t\t\t(t >= r && t <= 90) ||\n\t\t\t\t\t(t >= l && t <= i) ||\n\t\t\t\t\tt >= a ||\n\t\t\t\t\t(t >= s && t <= u) ||\n\t\t\t\t\t(t === o && c + 2 < f.length && f.at(c + 2) !== e)\n\t\t\t\t) {\n\t\t\t\t\tconst e = wouldStartIdentifier(f, c + 1) ? 'id' : 'unrestricted',\n\t\t\t\t\t\tt = consumeIdentUnsafe(f, c + 1)\n\t\t\t\t\tif (null !== t) {\n\t\t\t\t\t\tconst [n, s] = t\n\t\t\t\t\t\t;(c = n), p.push({type: 'hash', value: s.toLowerCase(), flag: e, start: h, end: c})\n\t\t\t\t\t\tcontinue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tp.push({type: 'delim', value: d, start: h, end: c})\n\t\t} else if (39 === d) {\n\t\t\tconst e = consumeString(f, c)\n\t\t\tif (null === e) return {errid: 'INVALID_STRING', start: c, end: c}\n\t\t\tconst [t, n] = e\n\t\t\t;(c = t), p.push({type: 'string', value: n, start: h, end: c})\n\t\t} else if (40 === d) p.push({type: '(', start: h, end: c})\n\t\telse if (41 === d) p.push({type: ')', start: h, end: c})\n\t\telse if (43 === d) {\n\t\t\tconst e = consumeNumeric(f, c)\n\t\t\tif (null === e) p.push({type: 'delim', value: d, start: h, end: c})\n\t\t\telse {\n\t\t\t\tconst [t, n] = e\n\t\t\t\t;(c = t),\n\t\t\t\t\t'dimension' === n[0]\n\t\t\t\t\t\t? p.push({\n\t\t\t\t\t\t\t\ttype: 'dimension',\n\t\t\t\t\t\t\t\tvalue: n[1],\n\t\t\t\t\t\t\t\tunit: n[2].toLowerCase(),\n\t\t\t\t\t\t\t\tflag: 'number',\n\t\t\t\t\t\t\t\tstart: h,\n\t\t\t\t\t\t\t\tend: c\n\t\t\t\t\t\t  })\n\t\t\t\t\t\t: 'number' === n[0]\n\t\t\t\t\t\t? p.push({type: n[0], value: n[1], flag: n[2], start: h, end: c})\n\t\t\t\t\t\t: p.push({type: n[0], value: n[1], flag: 'number', start: h, end: c})\n\t\t\t}\n\t\t} else if (44 === d) p.push({type: 'comma', start: h, end: c})\n\t\telse if (d === n) {\n\t\t\tconst e = consumeNumeric(f, c)\n\t\t\tif (null !== e) {\n\t\t\t\tconst [t, n] = e\n\t\t\t\t;(c = t),\n\t\t\t\t\t'dimension' === n[0]\n\t\t\t\t\t\t? p.push({\n\t\t\t\t\t\t\t\ttype: 'dimension',\n\t\t\t\t\t\t\t\tvalue: n[1],\n\t\t\t\t\t\t\t\tunit: n[2].toLowerCase(),\n\t\t\t\t\t\t\t\tflag: 'number',\n\t\t\t\t\t\t\t\tstart: h,\n\t\t\t\t\t\t\t\tend: c\n\t\t\t\t\t\t  })\n\t\t\t\t\t\t: 'number' === n[0]\n\t\t\t\t\t\t? p.push({type: n[0], value: n[1], flag: n[2], start: h, end: c})\n\t\t\t\t\t\t: p.push({type: n[0], value: n[1], flag: 'number', start: h, end: c})\n\t\t\t\tcontinue\n\t\t\t}\n\t\t\tif (c + 2 < f.length) {\n\t\t\t\tconst e = f.at(c + 1),\n\t\t\t\t\tt = f.at(c + 2)\n\t\t\t\tif (e === n && 62 === t) {\n\t\t\t\t\t;(c += 2), p.push({type: 'CDC', start: h, end: c})\n\t\t\t\t\tcontinue\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst t = consumeIdentLike(f, c)\n\t\t\tif (null !== t) {\n\t\t\t\tconst [e, n, s] = t\n\t\t\t\t;(c = e), p.push({type: s, value: n, start: h, end: c})\n\t\t\t\tcontinue\n\t\t\t}\n\t\t\tp.push({type: 'delim', value: d, start: h, end: c})\n\t\t} else if (46 === d) {\n\t\t\tconst e = consumeNumeric(f, c)\n\t\t\tif (null !== e) {\n\t\t\t\tconst [t, n] = e\n\t\t\t\t;(c = t),\n\t\t\t\t\t'dimension' === n[0]\n\t\t\t\t\t\t? p.push({\n\t\t\t\t\t\t\t\ttype: 'dimension',\n\t\t\t\t\t\t\t\tvalue: n[1],\n\t\t\t\t\t\t\t\tunit: n[2].toLowerCase(),\n\t\t\t\t\t\t\t\tflag: 'number',\n\t\t\t\t\t\t\t\tstart: h,\n\t\t\t\t\t\t\t\tend: c\n\t\t\t\t\t\t  })\n\t\t\t\t\t\t: 'number' === n[0]\n\t\t\t\t\t\t? p.push({type: n[0], value: n[1], flag: n[2], start: h, end: c})\n\t\t\t\t\t\t: p.push({type: n[0], value: n[1], flag: 'number', start: h, end: c})\n\t\t\t\tcontinue\n\t\t\t}\n\t\t\tp.push({type: 'delim', value: d, start: h, end: c})\n\t\t} else if (58 === d) p.push({type: 'colon', start: h, end: c})\n\t\telse if (59 === d) p.push({type: 'semicolon', start: h, end: c})\n\t\telse if (60 === d) {\n\t\t\tif (c + 3 < f.length) {\n\t\t\t\tconst e = f.at(c + 1),\n\t\t\t\t\tt = f.at(c + 2),\n\t\t\t\t\ts = f.at(c + 3)\n\t\t\t\tif (33 === e && t === n && s === n) {\n\t\t\t\t\t;(c += 3), p.push({type: 'CDO', start: h, end: c})\n\t\t\t\t\tcontinue\n\t\t\t\t}\n\t\t\t}\n\t\t\tp.push({type: 'delim', value: d, start: h, end: c})\n\t\t} else if (64 === d) {\n\t\t\tconst e = consumeIdent(f, c + 1)\n\t\t\tif (null !== e) {\n\t\t\t\tconst [t, n] = e\n\t\t\t\t;(c = t), p.push({type: 'at-keyword', value: n.toLowerCase(), start: h, end: c})\n\t\t\t\tcontinue\n\t\t\t}\n\t\t\tp.push({type: 'delim', value: d, start: h, end: c})\n\t\t} else if (91 === d) p.push({type: '[', start: h, end: c})\n\t\telse if (93 === d) p.push({type: ']', start: h, end: c})\n\t\telse if (123 === d) p.push({type: '{', start: h, end: c})\n\t\telse if (125 === d) p.push({type: '}', start: h, end: c})\n\t\telse if (d >= s && d <= u) {\n\t\t\tconst e = consumeNumeric(f, c),\n\t\t\t\t[t, n] = e\n\t\t\t;(c = t),\n\t\t\t\t'dimension' === n[0]\n\t\t\t\t\t? p.push({\n\t\t\t\t\t\t\ttype: 'dimension',\n\t\t\t\t\t\t\tvalue: n[1],\n\t\t\t\t\t\t\tunit: n[2].toLowerCase(),\n\t\t\t\t\t\t\tflag: 'number',\n\t\t\t\t\t\t\tstart: h,\n\t\t\t\t\t\t\tend: c\n\t\t\t\t\t  })\n\t\t\t\t\t: 'number' === n[0]\n\t\t\t\t\t? p.push({type: n[0], value: n[1], flag: n[2], start: h, end: c})\n\t\t\t\t\t: p.push({type: n[0], value: n[1], flag: 'number', start: h, end: c})\n\t\t} else if (95 === d || (d >= r && d <= 90) || (d >= l && d <= i) || d >= a || d === o) {\n\t\t\tconst e = consumeIdentLike(f, c)\n\t\t\tif (null === e) p.push({type: 'delim', value: d, start: h, end: c})\n\t\t\telse {\n\t\t\t\tconst [t, n, s] = e\n\t\t\t\t;(c = t), p.push({type: s, value: n, start: h, end: c})\n\t\t\t}\n\t\t} else p.push({type: 'delim', value: d, start: h, end: c})\n\t}\n\treturn p.push({type: 'EOF', start: c, end: c}), p\n}\nexport const consumeString = (t, n) => {\n\tif (t.length <= n + 1) return null\n\tconst s = t.at(n),\n\t\tu = []\n\tfor (let r = n + 1; r < t.length; r += 1) {\n\t\tconst n = t.at(r)\n\t\tif (n === s) return [r, String.fromCodePoint(...u)]\n\t\tif (n === o) {\n\t\t\tconst e = consumeEscape(t, r)\n\t\t\tif (null === e) return null\n\t\t\tconst [n, s] = e\n\t\t\tu.push(s), (r = n)\n\t\t} else {\n\t\t\tif (n === e) return null\n\t\t\tu.push(n)\n\t\t}\n\t}\n\treturn null\n}\nexport const wouldStartIdentifier = (t, s) => {\n\tconst u = t.at(s)\n\tif (void 0 === u) return !1\n\tif (u === n) {\n\t\tconst u = t.at(s + 1)\n\t\tif (void 0 === u) return !1\n\t\tif (u === n || 95 === u || (u >= r && u <= 90) || (u >= l && u <= i) || u >= a) return !0\n\t\tif (u === o) {\n\t\t\tif (t.length <= s + 2) return !1\n\t\t\treturn t.at(s + 2) !== e\n\t\t}\n\t\treturn !1\n\t}\n\tif (95 === u || (u >= r && u <= 90) || (u >= l && u <= i) || u >= a) return !0\n\tif (u === o) {\n\t\tif (t.length <= s + 1) return !1\n\t\treturn t.at(s + 1) !== e\n\t}\n\treturn !1\n}\nexport const consumeEscape = (n, i) => {\n\tif (n.length <= i + 1) return null\n\tif (n.at(i) !== o) return null\n\tconst a = n.at(i + 1)\n\tif (a === e) return null\n\tif ((a >= s && a <= u) || (a >= r && a <= 70) || (a >= l && a <= 102)) {\n\t\tconst o = [a],\n\t\t\tf = Math.min(i + 7, n.length)\n\t\tlet c = i + 2\n\t\tfor (; c < f; c += 1) {\n\t\t\tconst e = n.at(c)\n\t\t\tif (!((e >= s && e <= u) || (e >= r && e <= 70) || (e >= l && e <= 102))) break\n\t\t\to.push(e)\n\t\t}\n\t\tif (c < n.length) {\n\t\t\tconst s = n.at(c)\n\t\t\t;(9 !== s && s !== t && s !== e) || (c += 1)\n\t\t}\n\t\treturn [c - 1, Number.parseInt(String.fromCodePoint(...o), 16)]\n\t}\n\treturn [i + 1, a]\n}\nexport const consumeNumeric = (e, t) => {\n\tconst n = consumeNumber(e, t)\n\tif (null === n) return null\n\tconst [s, u, r] = n,\n\t\to = consumeIdent(e, s + 1)\n\tif (null !== o) {\n\t\tconst [e, t] = o\n\t\treturn [e, ['dimension', u, t]]\n\t}\n\treturn s + 1 < e.length && 37 === e.at(s + 1) ? [s + 1, ['percentage', u]] : [s, ['number', u, r]]\n}\nexport const consumeNumber = (e, t) => {\n\tconst r = e.at(t)\n\tif (void 0 === r) return null\n\tlet o = 'integer'\n\tconst l = []\n\tfor ((43 !== r && r !== n) || ((t += 1), r === n && l.push(n)); t < e.length; ) {\n\t\tconst n = e.at(t)\n\t\tif (!(n >= s && n <= u)) break\n\t\tl.push(n), (t += 1)\n\t}\n\tif (t + 1 < e.length) {\n\t\tconst n = e.at(t),\n\t\t\tr = e.at(t + 1)\n\t\tif (46 === n && r >= s && r <= u)\n\t\t\tfor (l.push(n, r), o = 'number', t += 2; t < e.length; ) {\n\t\t\t\tconst n = e.at(t)\n\t\t\t\tif (!(n >= s && n <= u)) break\n\t\t\t\tl.push(n), (t += 1)\n\t\t\t}\n\t}\n\tif (t + 1 < e.length) {\n\t\tconst r = e.at(t),\n\t\t\ti = e.at(t + 1),\n\t\t\ta = e.at(t + 2)\n\t\tif (69 === r || 101 === r) {\n\t\t\tlet r = !1\n\t\t\tif (\n\t\t\t\t(i >= s && i <= u\n\t\t\t\t\t? (l.push(69, i), (t += 2), (r = !0))\n\t\t\t\t\t: (i === n || 43 === i) &&\n\t\t\t\t\t  void 0 !== a &&\n\t\t\t\t\t  a >= s &&\n\t\t\t\t\t  a <= u &&\n\t\t\t\t\t  (l.push(69), i === n && l.push(n), l.push(a), (t += 3), (r = !0)),\n\t\t\t\tr)\n\t\t\t)\n\t\t\t\tfor (o = 'number'; t < e.length; ) {\n\t\t\t\t\tconst n = e.at(t)\n\t\t\t\t\tif (!(n >= s && n <= u)) break\n\t\t\t\t\tl.push(n), (t += 1)\n\t\t\t\t}\n\t\t}\n\t}\n\tconst i = String.fromCodePoint(...l)\n\tlet a = 'number' === o ? Number.parseFloat(i) : Number.parseInt(i)\n\treturn 0 === a && (a = 0), Number.isNaN(a) ? null : [t - 1, a, o]\n}\nexport const consumeIdentUnsafe = (e, t) => {\n\tif (e.length <= t) return null\n\tconst o = []\n\tfor (let f = e.at(t); t < e.length; f = e.at(++t)) {\n\t\tif (\n\t\t\t!(\n\t\t\t\tf === n ||\n\t\t\t\t95 === f ||\n\t\t\t\t(f >= r && f <= 90) ||\n\t\t\t\t(f >= l && f <= i) ||\n\t\t\t\tf >= a ||\n\t\t\t\t(f >= s && f <= u)\n\t\t\t)\n\t\t) {\n\t\t\t{\n\t\t\t\tconst n = consumeEscape(e, t)\n\t\t\t\tif (null !== n) {\n\t\t\t\t\tconst [e, s] = n\n\t\t\t\t\to.push(s), (t = e)\n\t\t\t\t\tcontinue\n\t\t\t\t}\n\t\t\t}\n\t\t\tbreak\n\t\t}\n\t\to.push(f)\n\t}\n\treturn 0 === t ? null : [t - 1, String.fromCodePoint(...o)]\n}\nexport const consumeIdent = (e, t) => (wouldStartIdentifier(e, t) ? consumeIdentUnsafe(e, t) : null)\nexport const consumeUrl = (n, s) => {\n\tlet u = n.at(s)\n\tfor (; 9 === u || u === t || u === e; ) u = n.at(++s)\n\tconst r = []\n\tlet l = !1\n\tfor (; s < n.length; ) {\n\t\tif (41 === u) return [s, String.fromCodePoint(...r)]\n\t\tif (34 === u || 39 === u || 40 === u) return null\n\t\tif (9 === u || u === t || u === e) !l && r.length > 0 && (l = !0)\n\t\telse if (u === o) {\n\t\t\tconst e = consumeEscape(n, s)\n\t\t\tif (null === e || l) return null\n\t\t\tconst [t, u] = e\n\t\t\tr.push(u), (s = t)\n\t\t} else {\n\t\t\tif (l) return null\n\t\t\tr.push(u)\n\t\t}\n\t\tu = n.at(++s)\n\t}\n\treturn null\n}\nexport const consumeIdentLike = (n, s) => {\n\tconst u = consumeIdent(n, s)\n\tif (null === u) return null\n\tconst [r, o] = u\n\tif ('url' === o.toLowerCase()) {\n\t\tif (n.length > r + 1) {\n\t\t\tif (40 === n.at(r + 1)) {\n\t\t\t\tfor (let s = 2; r + s < n.length; s += 1) {\n\t\t\t\t\tconst u = n.at(r + s)\n\t\t\t\t\tif (34 === u || 39 === u) return [r + 1, o.toLowerCase(), 'function']\n\t\t\t\t\tif (9 !== u && u !== t && u !== e) {\n\t\t\t\t\t\tconst e = consumeUrl(n, r + s)\n\t\t\t\t\t\tif (null === e) return null\n\t\t\t\t\t\tconst [t, u] = e\n\t\t\t\t\t\treturn [t, u, 'url']\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn [r + 1, o.toLowerCase(), 'function']\n\t\t\t}\n\t\t}\n\t} else if (n.length > r + 1) {\n\t\tif (40 === n.at(r + 1)) return [r + 1, o.toLowerCase(), 'function']\n\t}\n\treturn [r, o.toLowerCase(), 'ident']\n}\n", "import {isParserError as o} from '../ast/ast.js'\nimport {readCodepoints as r} from './codepoints.js'\nimport {convertToParserTokens as s} from './process.js'\nimport {codepointsToTokens as t} from './tokens.js'\nexport const lexer = m => {\n\tconst e = t(r(m))\n\treturn o(e) ? e : s(e)\n}\n", "export const isParserError = r => 'object' == typeof r && null !== r && 'errid' in r\n", "/**! media-query-parser | <PERSON> <<EMAIL>> (https://tom.bio) | @license MIT  */\nimport {\n\treadMediaQueryList as r,\n\treadMediaQuery as t,\n\treadMediaCondition as e,\n\treadMediaFeature as o\n} from './ast/ast.js'\nimport {\n\tflattenMediaQueryList as s,\n\tflattenMediaQuery as n,\n\tflattenMediaCondition as i\n} from './flatten/flatten.js'\nimport {\n\tgenerateMediaQueryList as a,\n\tgenerateMediaQuery as u,\n\tgenerateMediaCondition as c,\n\tgenerateMediaFeature as p,\n\tgenerateValidValueToken as f\n} from './generator/generator.js'\nimport {deleteUndefinedValues as m, invertParserError as l} from './internals.js'\nimport {lexer as x} from './lexer/lexer.js'\nimport {isParserError as d} from './utils.js'\nexport const parseMediaQueryList = t => {\n\tconst e = x(t)\n\treturn d(e) ? l(e) : m(s(r(e)))\n}\nexport const parseMediaQuery = r => {\n\tconst e = x(r)\n\tif (d(e)) return l(e)\n\t{\n\t\tconst r = t(e)\n\t\treturn d(r) ? l(r) : m(n(r))\n\t}\n}\nexport const parseMediaCondition = r => {\n\tconst t = x(r)\n\tif (d(t)) return l(t)\n\t{\n\t\tconst r = e(t, !0)\n\t\treturn d(r) ? l(r) : m(i(r))\n\t}\n}\nexport const parseMediaFeature = r => {\n\tconst t = x(r)\n\tif (d(t)) return l(t)\n\t{\n\t\tconst r = o(t)\n\t\treturn d(r) ? r : m(r)\n\t}\n}\nexport const stringify = r => {\n\tswitch (r.type) {\n\t\tcase 'query-list':\n\t\t\treturn a(r)\n\t\tcase 'query':\n\t\t\treturn u(r)\n\t\tcase 'condition':\n\t\t\treturn c(r)\n\t\tcase 'feature':\n\t\t\treturn p(r)\n\t\tdefault:\n\t\t\treturn f(r)\n\t}\n}\nexport * from './utils.js'\n", "export const DISCRETE_FEATURES = {\n\t'any-hover': {none: 1, hover: 1},\n\t'any-pointer': {none: 1, coarse: 1, fine: 1},\n\t'color-gamut': {srgb: 1, p3: 1, rec2020: 1},\n\tgrid: {0: 1, 1: 1},\n\thover: {none: 1, hover: 1},\n\t'overflow-block': {none: 1, scroll: 1, paged: 1},\n\t'overflow-inline': {none: 1, scroll: 1},\n\tpointer: {none: 1, coarse: 1, fine: 1},\n\tscan: {interlace: 1, progressive: 1},\n\tupdate: {none: 1, slow: 1, fast: 1},\n\t'display-mode': {fullscreen: 1, standalone: 1, 'minimal-ui': 1, browser: 1},\n\t'dynamic-range': {standard: 1, high: 1},\n\t'environment-blending': {opaque: 1, additive: 1, subtractive: 1},\n\t'forced-colors': {none: 1, active: 1},\n\t'inverted-colors': {none: 1, inverted: 1},\n\t'nav-controls': {none: 1, back: 1},\n\t'prefers-color-scheme': {light: 1, dark: 1},\n\t'prefers-contrast': {'no-preference': 1, less: 1, more: 1, custom: 1},\n\t'prefers-reduced-data': {'no-preference': 1, reduce: 1},\n\t'prefers-reduced-motion': {'no-preference': 1, reduce: 1},\n\t'prefers-reduced-transparency': {'no-preference': 1, reduce: 1},\n\tscripting: {none: 1, 'initial-only': 1, enabled: 1},\n\t'video-color-gamut': {srgb: 1, p3: 1, rec2020: 1},\n\t'video-dynamic-range': {standard: 1, high: 1}\n}\nexport const RANGE_NUMBER_FEATURES = {\n\tcolor: {feature: 'color', type: 'integer', bounds: [!0, 0, 1 / 0, !1]},\n\t'color-index': {feature: 'color-index', type: 'integer', bounds: [!0, 0, 1 / 0, !1]},\n\tmonochrome: {feature: 'monochrome', type: 'integer', bounds: [!0, 0, 1 / 0, !1]},\n\t'device-height': {feature: 'device-height', type: 'length', bounds: [!0, 0, 1 / 0, !1]},\n\t'device-width': {feature: 'device-width', type: 'length', bounds: [!0, 0, 1 / 0, !1]},\n\theight: {feature: 'height', type: 'length', bounds: [!0, 0, 1 / 0, !1]},\n\twidth: {feature: 'width', type: 'length', bounds: [!0, 0, 1 / 0, !1]},\n\tresolution: {feature: 'resolution', type: 'resolution', bounds: [!0, 0, 1 / 0, !1]},\n\t'horizontal-viewport-segments': {\n\t\tfeature: 'horizontal-viewport-segments',\n\t\ttype: 'integer',\n\t\tbounds: [!0, 0, 1 / 0, !1]\n\t},\n\t'vertical-viewport-segments': {\n\t\tfeature: 'vertical-viewport-segments',\n\t\ttype: 'integer',\n\t\tbounds: [!0, 0, 1 / 0, !1]\n\t}\n}\nexport const RANGE_RATIO_FEATURES = {\n\t'aspect-ratio': {feature: 'aspect-ratio', type: 'ratio', bounds: [!1, [0, 1], [1 / 0, 1], !1]},\n\t'device-aspect-ratio': {\n\t\tfeature: 'device-aspect-ratio',\n\t\ttype: 'ratio',\n\t\tbounds: [!1, [0, 1], [1 / 0, 1], !1]\n\t}\n}\nexport const permToConditionPairs = e => Object.entries(e).filter(e => void 0 !== e[1])\nconst e = new Set(Object.keys(DISCRETE_FEATURES))\nexport const hasDiscreteKey = t => e.has(t[0])\nexport const isDiscreteKey = t => e.has(t)\nconst t = new Set(Object.keys(RANGE_RATIO_FEATURES))\nexport const hasRangeRatioKey = e => t.has(e[0])\nexport const isRangeRatioKey = e => t.has(e)\nconst o = new Set(Object.keys(RANGE_NUMBER_FEATURES))\nexport const hasRangeNumberKey = e => o.has(e[0])\nexport const isRangeNumberKey = e => o.has(e)\nexport const hasRangeKey = e => hasRangeNumberKey(e) || hasRangeRatioKey(e)\nexport const isRangeKey = e => isRangeNumberKey(e) || isRangeRatioKey(e)\nconst n = new Set([\n\t'any-hover',\n\t'any-pointer',\n\t'color-gamut',\n\t'grid',\n\t'hover',\n\t'overflow-block',\n\t'overflow-inline',\n\t'pointer',\n\t'scan',\n\t'update',\n\t'aspect-ratio',\n\t'color',\n\t'color-index',\n\t'device-aspect-ratio',\n\t'device-height',\n\t'device-width',\n\t'height',\n\t'monochrome',\n\t'resolution',\n\t'width',\n\t'media-type',\n\t'invalid-features'\n])\nexport const isPermKey = e => n.has(e)\nexport const isFeatureKey = e => isRangeNumberKey(e) || isRangeRatioKey(e) || isDiscreteKey(e)\nexport const attachPair = (e, t) => {\n\te[t[0]] = t[1]\n}\nexport const andRanges = (...e) =>\n\te.reduce(\n\t\t(e, t) =>\n\t\t\t'{true}' === e\n\t\t\t\t? t\n\t\t\t\t: '{true}' === t\n\t\t\t\t? e\n\t\t\t\t: '{false}' === e || '{false}' === t\n\t\t\t\t? '{false}'\n\t\t\t\t: ((e, t) => {\n\t\t\t\t\t\tconst [o, n, r, s] = e,\n\t\t\t\t\t\t\ta = 'number' == typeof n ? n : n[0] / n[1],\n\t\t\t\t\t\t\ti = 'number' == typeof r ? r : r[0] / r[1],\n\t\t\t\t\t\t\t[c, p, u, d] = t,\n\t\t\t\t\t\t\tl = 'number' == typeof p ? p : p[0] / p[1],\n\t\t\t\t\t\t\tg = 'number' == typeof u ? u : u[0] / u[1]\n\t\t\t\t\t\tlet f = o !== c && !o\n\t\t\t\t\t\ta !== l && (f = a > l)\n\t\t\t\t\t\tlet h = s !== d && !s\n\t\t\t\t\t\ti !== g && (h = i < g)\n\t\t\t\t\t\tconst y = f ? o : c,\n\t\t\t\t\t\t\tR = f ? n : p,\n\t\t\t\t\t\t\tb = h ? r : u,\n\t\t\t\t\t\t\tm = h ? s : d\n\t\t\t\t\t\treturn R > b || (R === b && (!y || !m)) ? '{false}' : [y, R, b, m]\n\t\t\t\t  })(e, t),\n\t\t'{true}'\n\t)\nexport const boundRange = e => {\n\tif (hasRangeRatioKey(e)) {\n\t\tconst {bounds: t} = RANGE_RATIO_FEATURES[e[0]],\n\t\t\to = andRanges(e[1], t)\n\t\tif ('string' == typeof o) return o\n\t\tif (\n\t\t\to[0] === t[0] &&\n\t\t\to[1][0] === t[1][0] &&\n\t\t\to[1][1] === t[1][1] &&\n\t\t\to[2][0] === t[2][0] &&\n\t\t\to[2][1] === t[2][1] &&\n\t\t\to[3] === t[3]\n\t\t)\n\t\t\treturn '{true}'\n\t\t{\n\t\t\tconst e = o[1][0] / o[1][1],\n\t\t\t\tt = o[2][0] / o[2][1]\n\t\t\treturn e > t || (e === t && (!o[0] || !o[3])) ? '{false}' : o\n\t\t}\n\t}\n\t{\n\t\tconst {bounds: t} = RANGE_NUMBER_FEATURES[e[0]],\n\t\t\to = andRanges(e[1], t)\n\t\treturn 'string' == typeof o\n\t\t\t? o\n\t\t\t: o[0] === t[0] && o[1] === t[1] && o[2] === t[2] && o[3] === t[3]\n\t\t\t? '{true}'\n\t\t\t: o[1] > o[2] || (o[1] === o[2] && (!o[0] || !o[3]))\n\t\t\t? '{false}'\n\t\t\t: o\n\t}\n}\nexport const binaryOrRanges = (e, t) => {\n\tconst [o, n, r, s] = e,\n\t\ta = 'number' == typeof n ? n : n[0] / n[1],\n\t\ti = 'number' == typeof r ? r : r[0] / r[1],\n\t\t[c, p, u, d] = t,\n\t\tl = 'number' == typeof p ? p : p[0] / p[1],\n\t\tg = 'number' == typeof u ? u : u[0] / u[1]\n\tlet f = !1\n\tif (\n\t\t((f =\n\t\t\ta < l || (a === l && o) ? l < i || (l === i && (c || s)) : a < g || (a === g && (o || d))),\n\t\tf)\n\t) {\n\t\tlet e = o || c\n\t\ta > l ? (e = c) : a < l && (e = o)\n\t\tlet t = s || d\n\t\treturn i > g ? (t = s) : i < g && (t = d), [[e, a < l ? n : p, i < g ? r : u, t]]\n\t}\n\treturn [e, t]\n}\nexport const notRatioRange = e => {\n\tif ('string' == typeof e[1]) throw new Error('expected range')\n\tconst {bounds: t} = RANGE_RATIO_FEATURES[e[0]],\n\t\t[o, n, r, s] = e[1],\n\t\ta = n[0] / n[1],\n\t\ti = r[0] / r[1],\n\t\tc = t[1][0] / t[1][1],\n\t\tp = t[0],\n\t\tu = t[2][0] / t[2][1],\n\t\td = t[3],\n\t\tl = i > u || (i === u && !(d && !s))\n\treturn a < c || (a === c && !(p && !o))\n\t\t? l\n\t\t\t? '{false}'\n\t\t\t: [[!s, r, t[2], t[3]]]\n\t\t: l\n\t\t? [[t[0], t[1], n, !o]]\n\t\t: [\n\t\t\t\t[t[0], t[1], n, !o],\n\t\t\t\t[!s, r, t[2], t[3]]\n\t\t  ]\n}\nexport function notNumberRange(e) {\n\tif ('string' == typeof e[1]) throw new Error('expected range')\n\tconst {bounds: t} = RANGE_NUMBER_FEATURES[e[0]],\n\t\t[o, n, r, s] = e[1],\n\t\ta = t[1],\n\t\ti = t[0],\n\t\tc = t[2],\n\t\tp = t[3],\n\t\tu = r > c || (r === c && !(p && !s))\n\treturn n < a || (n === a && !(i && !o))\n\t\t? u\n\t\t\t? '{false}'\n\t\t\t: [[!s, r, t[2], t[3]]]\n\t\t: u\n\t\t? [[t[0], t[1], n, !o]]\n\t\t: [\n\t\t\t\t[t[0], t[1], n, !o],\n\t\t\t\t[!s, r, t[2], t[3]]\n\t\t  ]\n}\n", "import {is<PERSON><PERSON><PERSON><PERSON><PERSON> as e, is<PERSON><PERSON><PERSON><PERSON><PERSON> as t, RANGE_NUMBER_FEATURES as n} from './helpers.js'\nconst i = {\n\twidthPx: 1920,\n\theightPx: 1080,\n\twritingMode: 'horizontal-tb',\n\temPx: 16,\n\tlhPx: 16,\n\texPx: 8,\n\tchPx: 8,\n\tcapPx: 11,\n\ticPx: 16\n}\nexport const convertToUnit = (e, t) => {\n\tif ('number' === e.type) return {type: 'number', value: e.value}\n\tif ('dimension' === e.type) {\n\t\tlet n\n\t\tswitch (e.unit) {\n\t\t\tcase 's':\n\t\t\tcase 'ms':\n\t\t\t\tn = 'time'\n\t\t\t\tbreak\n\t\t\tcase 'hz':\n\t\t\tcase 'khz':\n\t\t\t\tn = 'frequency'\n\t\t\t\tbreak\n\t\t\tcase 'dpi':\n\t\t\tcase 'dpcm':\n\t\t\tcase 'dppx':\n\t\t\tcase 'x':\n\t\t\t\tn = 'resolution'\n\t\t\t\tbreak\n\t\t\tdefault:\n\t\t\t\tn = 'length'\n\t\t}\n\t\tif ('px' === e.unit) return {type: 'dimension', subtype: 'length', px: e.value}\n\t\tif ('time' === n)\n\t\t\treturn {\n\t\t\t\ttype: 'dimension',\n\t\t\t\tsubtype: 'time',\n\t\t\t\tms: 's' === e.unit ? Math.round(1e3 * e.value) : e.value\n\t\t\t}\n\t\tif ('frequency' === n)\n\t\t\treturn {\n\t\t\t\ttype: 'dimension',\n\t\t\t\tsubtype: 'frequency',\n\t\t\t\thz: 'khz' === e.unit ? Math.round(1e3 * e.value) : e.value\n\t\t\t}\n\t\tif ('resolution' === n) {\n\t\t\tlet t = e.value\n\t\t\treturn (\n\t\t\t\t'dpi' === e.unit\n\t\t\t\t\t? (t = Number.parseFloat((0.0104166667 * e.value).toFixed(3)))\n\t\t\t\t\t: 'dpcm' === e.unit && (t = Number.parseFloat((0.0264583333 * e.value).toFixed(3))),\n\t\t\t\t{type: 'dimension', subtype: 'resolution', dppx: t}\n\t\t\t)\n\t\t}\n\t\tif (e.unit in t) {\n\t\t\tconst n = t[e.unit]\n\t\t\treturn {type: 'dimension', subtype: 'length', px: Number.parseFloat((e.value * n).toFixed(3))}\n\t\t}\n\t\treturn {type: 'ident', value: '{never}'}\n\t}\n\treturn 'ident' === e.type\n\t\t? 'infinite' === e.value\n\t\t\t? {type: 'infinite'}\n\t\t\t: {type: 'ident', value: e.value}\n\t\t: {type: 'ratio', numerator: e.numerator, denominator: e.denominator}\n}\nexport const compileStaticUnitConversions = e => {\n\tlet t = {}\n\t'number' == typeof e.emPx &&\n\t\t(t = {\n\t\t\texPx: Math.round(0.5 * e.emPx),\n\t\t\tchPx: Math.round(0.5 * e.emPx),\n\t\t\tcapPx: Math.round(0.7 * e.emPx),\n\t\t\ticPx: Math.round(e.emPx)\n\t\t})\n\tconst n = {...i, ...t, ...e},\n\t\t{\n\t\t\twidthPx: r,\n\t\t\theightPx: o,\n\t\t\twritingMode: a,\n\t\t\temPx: u,\n\t\t\tlhPx: p,\n\t\t\texPx: l,\n\t\t\tchPx: m,\n\t\t\tcapPx: s,\n\t\t\ticPx: f\n\t\t} = n,\n\t\tv = r / 100,\n\t\tc = o / 100\n\treturn {\n\t\tem: u,\n\t\trem: u,\n\t\tlh: p,\n\t\trlh: p,\n\t\tex: l,\n\t\tch: m,\n\t\tcap: s,\n\t\tic: f,\n\t\tvw: v,\n\t\tvh: c,\n\t\tvmin: Math.min(c, v),\n\t\tvmax: Math.max(c, v),\n\t\tvi: 'horizontal-tb' === a ? v : c,\n\t\tvb: 'horizontal-tb' === a ? c : v,\n\t\tcm: 37.79527559,\n\t\tmm: 0.03779527559,\n\t\tin: 96,\n\t\tq: 0.009448818898,\n\t\tpc: 16,\n\t\tpt: 16\n\t}\n}\nconst r = {'<': '>', '<=': '>=', '>': '<', '>=': '<='}\nexport const simplifyMediaFeature = (n, i) => {\n\tif ('range' === n.context) {\n\t\tif (t(n.feature)) {\n\t\t\tconst {range: e, feature: t} = n\n\t\t\treturn void 0 !== e.leftToken && void 0 !== e.rightToken\n\t\t\t\t? '<' === e.leftOp || '<=' === e.leftOp\n\t\t\t\t\t? {\n\t\t\t\t\t\t\ttype: 'double',\n\t\t\t\t\t\t\tname: t,\n\t\t\t\t\t\t\tminOp: e.leftOp,\n\t\t\t\t\t\t\tmin: convertToUnit(e.leftToken, i),\n\t\t\t\t\t\t\tmaxOp: e.rightOp,\n\t\t\t\t\t\t\tmax: convertToUnit(e.rightToken, i)\n\t\t\t\t\t  }\n\t\t\t\t\t: {\n\t\t\t\t\t\t\ttype: 'double',\n\t\t\t\t\t\t\tname: t,\n\t\t\t\t\t\t\tminOp: '>' === e.rightOp ? '<' : '<=',\n\t\t\t\t\t\t\tmin: convertToUnit(e.rightToken, i),\n\t\t\t\t\t\t\tmaxOp: e.leftOp ? '<' : '<=',\n\t\t\t\t\t\t\tmax: convertToUnit(e.leftToken, i)\n\t\t\t\t\t  }\n\t\t\t\t: void 0 === e.rightToken\n\t\t\t\t? '=' === e.leftOp\n\t\t\t\t\t? {type: 'equals', name: t, value: convertToUnit(e.leftToken, i)}\n\t\t\t\t\t: {type: 'single', name: t, op: r[e.leftOp], value: convertToUnit(e.leftToken, i)}\n\t\t\t\t: '=' === e.rightOp\n\t\t\t\t? {type: 'equals', name: t, value: convertToUnit(e.rightToken, i)}\n\t\t\t\t: {type: 'single', name: t, op: e.rightOp, value: convertToUnit(e.rightToken, i)}\n\t\t}\n\t} else if ('value' === n.context) {\n\t\tif ('orientation' === n.feature) {\n\t\t\tif (void 0 === n.prefix && 'ident' === n.value.type) {\n\t\t\t\tif ('portrait' === n.value.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'single',\n\t\t\t\t\t\tname: 'aspect-ratio',\n\t\t\t\t\t\top: '<=',\n\t\t\t\t\t\tvalue: {type: 'ratio', numerator: 1, denominator: 1}\n\t\t\t\t\t}\n\t\t\t\tif ('landscape' === n.value.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\ttype: 'single',\n\t\t\t\t\t\tname: 'aspect-ratio',\n\t\t\t\t\t\top: '>=',\n\t\t\t\t\t\tvalue: {type: 'ratio', numerator: 1, denominator: 1}\n\t\t\t\t\t}\n\t\t\t}\n\t\t} else if (e(n.feature)) {\n\t\t\tif (void 0 === n.prefix)\n\t\t\t\treturn {type: 'equals', name: n.feature, value: convertToUnit(n.value, i)}\n\t\t\tif (t(n.feature))\n\t\t\t\treturn 'min' === n.prefix\n\t\t\t\t\t? {type: 'single', name: n.feature, op: '>=', value: convertToUnit(n.value, i)}\n\t\t\t\t\t: {type: 'single', name: n.feature, op: '<=', value: convertToUnit(n.value, i)}\n\t\t}\n\t} else {\n\t\tif ('orientation' === n.feature)\n\t\t\treturn {\n\t\t\t\ttype: 'double',\n\t\t\t\tname: 'aspect-ratio',\n\t\t\t\tmin: {type: 'ratio', numerator: 0, denominator: 1},\n\t\t\t\tminOp: '<',\n\t\t\t\tmaxOp: '<',\n\t\t\t\tmax: {type: 'ratio', numerator: Number.POSITIVE_INFINITY, denominator: 1}\n\t\t\t}\n\t\tif (e(n.feature)) return {type: 'boolean', name: n.feature}\n\t}\n\treturn {type: 'invalid', name: n.feature}\n}\nexport const getRatio = e =>\n\t'number' === e.type && e.value > 0\n\t\t? [e.value, 1]\n\t\t: 'ratio' === e.type\n\t\t? [e.numerator, e.denominator]\n\t\t: null\nexport const getValue = (e, t) => {\n\tconst i = n[t]\n\tif ('infinite' === e.type) {\n\t\tif ('resolution' === t) return Number.POSITIVE_INFINITY\n\t} else if ('integer' === i.type) {\n\t\tif ('number' === e.type && Number.isInteger(e.value)) return e.value\n\t} else if ('resolution' === i.type) {\n\t\tif ('dimension' === e.type && 'resolution' === e.subtype) return e.dppx\n\t} else if ('length' === i.type) {\n\t\tif ('dimension' === e.type && 'length' === e.subtype) return e.px\n\t\tif ('number' === e.type && 0 === e.value) return 0\n\t}\n\treturn null\n}\n", "import {isParserError as e, parseMediaQueryList as r} from 'media-query-parser'\nimport {\n\tpermToConditionPairs as t,\n\thasRangeNumberKey as o,\n\thasRangeRatioKey as n,\n\tattachPair as s,\n\tandRanges as i,\n\tDISCRETE_FEATURES as a,\n\tisDiscreteKey as l,\n\tisRangeRatioKey as m,\n\thasRangeKey as u,\n\thasDiscreteKey as f,\n\tnotRatioRange as p,\n\tnotNumberRange as d,\n\tboundRange as c\n} from './helpers.js'\nimport {\n\tcompileStaticUnitConversions as I,\n\tgetRatio as N,\n\tgetValue as T,\n\tsimplifyMediaFeature as y\n} from './units.js'\nexport const andPerms = (e, r) => {\n\tconst t = []\n\tfor (const o of e)\n\t\tfor (const e of r) {\n\t\t\tconst r = mergePerms(o, e)\n\t\t\tObject.keys(r).length > 0 && t.push(r)\n\t\t}\n\treturn t\n}\nexport const mergePerms = (e, r) => {\n\tconst a = {}\n\tfor (const r of t(e)) void 0 !== r[1] && s(a, r)\n\tfor (const e of t(r))\n\t\tif (e[0] in a) {\n\t\t\tif (void 0 !== a[e[0]]) {\n\t\t\t\tconst r = a\n\t\t\t\tif ('media-type' === e[0]);\n\t\t\t\telse if ('invalid-features' === e[0]) r[e[0]].push(...e[1])\n\t\t\t\telse if ('{false}' === r[e[0]] || '{false}' === e[1]) r[e[0]] = '{false}'\n\t\t\t\telse if ('{true}' === r[e[0]]) s(r, e)\n\t\t\t\telse if ('{true}' === e[1]);\n\t\t\t\telse {\n\t\t\t\t\tconst r = a\n\t\t\t\t\to(e) || n(e)\n\t\t\t\t\t\t? s(r, [e[0], i(r[e[0]], e[1])])\n\t\t\t\t\t\t: 'color-gamut' === e[0] || 'video-color-gamut' === e[0]\n\t\t\t\t\t\t? (r[e[0]] = [\n\t\t\t\t\t\t\t\tr[e[0]][0] && e[1][0],\n\t\t\t\t\t\t\t\tr[e[0]][1] && e[1][1],\n\t\t\t\t\t\t\t\tr[e[0]][2] && e[1][2],\n\t\t\t\t\t\t\t\tr[e[0]][3] && e[1][3]\n\t\t\t\t\t\t  ])\n\t\t\t\t\t\t: s(r, [e[0], r[e[0]] === e[1] ? r[e[0]] : '{false}'])\n\t\t\t\t}\n\t\t\t}\n\t\t} else s(a, e)\n\treturn a\n}\nexport const notPerms = e => e.map(e => invertPerm(e)).reduce((e, r) => andPerms(e, r))\nexport const invertPerm = e => {\n\tconst r = t(e),\n\t\to = []\n\tfor (const e of r)\n\t\tif (void 0 !== e[1]) {\n\t\t\tlet r, t\n\t\t\tif ('invalid-features' === e[0]) return [{[e[0]]: e[1]}]\n\t\t\tif ('media-type' === e[0]) continue\n\t\t\tif (((r = e), '{false}' === e[1])) t = [[e[0], '{true}']]\n\t\t\telse if ('{true}' === e[1]) t = [[e[0], '{false}']]\n\t\t\telse if (f(e))\n\t\t\t\tif ('color-gamut' === e[0]) {\n\t\t\t\t\tconst r = e[1]\n\t\t\t\t\tt = [['color-gamut', [!r[0], !r[1], !r[2], !r[3]]]]\n\t\t\t\t} else\n\t\t\t\t\tt =\n\t\t\t\t\t\t'grid' === e[0]\n\t\t\t\t\t\t\t? [['grid', 0 === e[1] ? 1 : 0]]\n\t\t\t\t\t\t\t: Object.keys(a[e[0]])\n\t\t\t\t\t\t\t\t\t.filter(r => r !== e[1])\n\t\t\t\t\t\t\t\t\t.map(r => [e[0], r])\n\t\t\telse if (n(e)) {\n\t\t\t\tconst r = p(e)\n\t\t\t\tt = ('{false}' === r ? ['{false}'] : r).map(r => [e[0], r])\n\t\t\t} else {\n\t\t\t\tconst r = d(e)\n\t\t\t\tt = ('{false}' === r ? ['{false}'] : r).map(r => [e[0], r])\n\t\t\t}\n\t\t\to.push([r, t])\n\t\t}\n\tconst s = []\n\tfor (const [, e] of o) for (const r of e) s.push({[r[0]]: r[1]})\n\treturn s\n}\nexport const mediaFeatureToPerms = (e, r) => {\n\tconst t = y(e, r),\n\t\to = [{'invalid-features': [e.feature]}]\n\tif ('invalid' === t.type) return o\n\tif ('boolean' === t.type)\n\t\treturn 'color-gamut' === t.name\n\t\t\t? [{'color-gamut': [!1, !0, !0, !0]}]\n\t\t\t: 'grid' === t.name\n\t\t\t? [{grid: 1}]\n\t\t\t: l(t.name)\n\t\t\t? invertPerm({[t.name]: 'none'})\n\t\t\t: m(t.name)\n\t\t\t? [{[t.name]: [!1, [0, 1], [Number.POSITIVE_INFINITY, 1], !0]}]\n\t\t\t: [{[t.name]: [!1, 0, Number.POSITIVE_INFINITY, !0]}]\n\tif (l(t.name)) {\n\t\tif ('equals' === t.type) {\n\t\t\tconst e = t.value\n\t\t\tif ('grid' === t.name) {\n\t\t\t\tif ('number' === e.type && (0 === e.value || 1 === e.value)) return [{grid: e.value}]\n\t\t\t} else if ('ident' === e.type && e.value in a[t.name]) {\n\t\t\t\tif ('color-gamut' !== t.name) return [{[t.name]: e.value}]\n\t\t\t\t{\n\t\t\t\t\tconst r = ['srgb', 'p3', 'rec2020'].indexOf(e.value)\n\t\t\t\t\tif (-1 !== r) return [{'color-gamut': [!1, r <= 0, r <= 1, r <= 2]}]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn o\n\t}\n\tif (m(t.name)) {\n\t\tlet e = null\n\t\tif ('equals' === t.type) {\n\t\t\tconst r = N(t.value)\n\t\t\tnull !== r && (e = [!0, r, r, !0])\n\t\t} else if ('single' === t.type) {\n\t\t\tconst r = N(t.value)\n\t\t\tnull !== r &&\n\t\t\t\t(e =\n\t\t\t\t\t'<' === t.op\n\t\t\t\t\t\t? [!0, [Number.NEGATIVE_INFINITY, 1], r, !1]\n\t\t\t\t\t\t: '<=' === t.op\n\t\t\t\t\t\t? [!0, [Number.NEGATIVE_INFINITY, 1], r, !0]\n\t\t\t\t\t\t: '>' === t.op\n\t\t\t\t\t\t? [!1, r, [Number.POSITIVE_INFINITY, 1], !0]\n\t\t\t\t\t\t: [!0, r, [Number.POSITIVE_INFINITY, 1], !0])\n\t\t} else if ('double' === t.type) {\n\t\t\tconst r = N(t.min),\n\t\t\t\to = N(t.max)\n\t\t\tnull !== r && null !== o && (e = ['<=' === t.minOp, r, o, '<=' === t.maxOp])\n\t\t}\n\t\treturn null === e ? o : [{[t.name]: c([t.name, e])}]\n\t}\n\t{\n\t\tlet e = null\n\t\tif ('equals' === t.type) {\n\t\t\tconst r = T(t.value, t.name)\n\t\t\tnull !== r && (e = [!0, r, r, !0])\n\t\t} else if ('single' === t.type) {\n\t\t\tconst r = T(t.value, t.name)\n\t\t\tnull !== r &&\n\t\t\t\t(e =\n\t\t\t\t\t'<' === t.op\n\t\t\t\t\t\t? [!0, Number.NEGATIVE_INFINITY, r, !1]\n\t\t\t\t\t\t: '<=' === t.op\n\t\t\t\t\t\t? [!0, Number.NEGATIVE_INFINITY, r, !0]\n\t\t\t\t\t\t: '>' === t.op\n\t\t\t\t\t\t? [!1, r, Number.POSITIVE_INFINITY, !0]\n\t\t\t\t\t\t: [!0, r, Number.POSITIVE_INFINITY, !0])\n\t\t} else if ('double' === t.type) {\n\t\t\tconst r = T(t.min, t.name),\n\t\t\t\to = T(t.max, t.name)\n\t\t\tnull !== r && null !== o && (e = ['<=' === t.minOp, r, o, '<=' === t.maxOp])\n\t\t}\n\t\treturn null === e ? o : [{[t.name]: c([t.name, e])}]\n\t}\n}\nexport const mediaConditionToPerms = (e, r) => {\n\tconst t = []\n\tfor (const o of e.children)\n\t\t'context' in o ? t.push(mediaFeatureToPerms(o, r)) : t.push(mediaConditionToPerms(o, r))\n\treturn 'or' === e.operator || void 0 === e.operator\n\t\t? t.flat()\n\t\t: 'and' === e.operator\n\t\t? t.reduce((e, r) => andPerms(e, r))\n\t\t: notPerms(t[0])\n}\nexport const simplifyPerms = e => {\n\tconst r = [],\n\t\to = new Set(),\n\t\tn = new Set()\n\tfor (const i of e) {\n\t\tlet e = !1\n\t\tif (Array.isArray(i['invalid-features']) && i['invalid-features'].length > 0) {\n\t\t\tfor (const e of i['invalid-features']) o.add(e)\n\t\t\te = !0\n\t\t}\n\t\tconst a = {}\n\t\tfor (const r of t(i))\n\t\t\tif ('invalid-features' !== r[0]) {\n\t\t\t\tif ('color-gamut' === r[0]) {\n\t\t\t\t\tconst e = r[1].toString()\n\t\t\t\t\t'false,false,false,false' === e\n\t\t\t\t\t\t? (r[1] = '{false}')\n\t\t\t\t\t\t: 'true,true,true,true' === e && (r[1] = '{true}')\n\t\t\t\t} else u(r) && (r[1] = c(r))\n\t\t\t\t'{false}' === r[1]\n\t\t\t\t\t? (n.add(r[0]), (e = !0))\n\t\t\t\t\t: '{true}' === r[1] || ('media-type' === r[0] && 'all' === r[1]) || s(a, r)\n\t\t\t}\n\t\te || r.push(a)\n\t}\n\treturn {simplePerms: r, invalidFeatures: [...o].sort(), falseFeatures: [...n].sort()}\n}\nexport const compileAST = (e, r = {}) => {\n\tconst t = I(r),\n\t\to = []\n\tfor (const r of e.mediaQueries) {\n\t\tconst e = []\n\t\t'not' === r.prefix\n\t\t\t? ('print' === r.mediaType\n\t\t\t\t\t? e.push({'media-type': 'not-print'})\n\t\t\t\t\t: 'screen' === r.mediaType && e.push({'media-type': 'not-screen'}),\n\t\t\t  void 0 !== r.mediaCondition &&\n\t\t\t\t\te.push(...notPerms(mediaConditionToPerms(r.mediaCondition, t))))\n\t\t\t: void 0 === r.mediaCondition\n\t\t\t? e.push({'media-type': r.mediaType})\n\t\t\t: e.push(\n\t\t\t\t\t...mediaConditionToPerms(r.mediaCondition, t).map(e => ({\n\t\t\t\t\t\t...e,\n\t\t\t\t\t\t'media-type': r.mediaType\n\t\t\t\t\t}))\n\t\t\t  ),\n\t\t\to.push(...e)\n\t}\n\treturn simplifyPerms(o)\n}\nexport const compileQuery = (t, o = {}) => {\n\tconst n = r(t)\n\tif (e(n))\n\t\tthrow new Error(`Error parsing media query list: ${n.errid} at chars ${n.start}:${n.end}`)\n\treturn compileAST(n, o)\n}\n", "export const DESKTOP_ENVIRONMENT = {\n\tmediaType: 'screen',\n\tanyHover: 'hover',\n\tanyPointer: 'fine',\n\tcolorGamut: 'srgb-but-not-p3',\n\tgrid: 'bitmap',\n\thover: 'hover',\n\toverflowBlock: 'scroll',\n\toverflowInline: 'scroll',\n\tpointer: 'fine',\n\tscan: 'progressive',\n\tupdate: 'fast',\n\tcolorIndex: 'none',\n\tcolorBits: 8,\n\tmonochromeBits: 'not-monochrome',\n\tdisplayMode: 'browser',\n\tdynamicRange: 'not-hdr',\n\tenvironmentBlending: 'opaque',\n\tforcedColors: 'none',\n\tinvertedColors: 'none',\n\tnavControls: 'back',\n\tprefersColorScheme: 'no-preference',\n\tprefersContrast: 'no-preference',\n\tprefersReducedData: 'no-preference',\n\tprefersReducedMotion: 'no-preference',\n\tprefersReducedTransparency: 'no-preference',\n\tscripting: 'enabled',\n\tvideoColorGamut: 'srgb-but-not-p3',\n\tvideoDynamicRange: 'not-hdr',\n\thorizontalViewportSegments: 1,\n\tverticalViewportSegments: 1\n}\nconst e = e => new Error(`Invalid option: ${e}`)\nexport const validateEnv = o => {\n\tif ('screen' !== o.mediaType && 'print' !== o.mediaType && 'not-screen-or-print' !== o.mediaType)\n\t\tthrow e('mediaType')\n\tif ('none' !== o.anyHover && 'hover' !== o.anyHover) throw e('anyHover')\n\tif ('none' !== o.anyPointer && 'coarse' !== o.anyPointer && 'fine' !== o.anyPointer)\n\t\tthrow e('anyPointer')\n\tif (\n\t\t'not-srgb' !== o.colorGamut &&\n\t\t'srgb-but-not-p3' !== o.colorGamut &&\n\t\t'p3-but-not-rec2020' !== o.colorGamut &&\n\t\t'rec2020' !== o.colorGamut\n\t)\n\t\tthrow e('colorGamut')\n\tif ('bitmap' !== o.grid && 'grid' !== o.grid) throw e('grid')\n\tif ('none' !== o.hover && 'hover' !== o.hover) throw e('hover')\n\tif ('none' !== o.overflowBlock && 'scroll' !== o.overflowBlock && 'paged' !== o.overflowBlock)\n\t\tthrow e('overflowBlock')\n\tif ('none' !== o.overflowInline && 'scroll' !== o.overflowInline) throw e('overflowInline')\n\tif ('none' !== o.pointer && 'coarse' !== o.pointer && 'fine' !== o.pointer) throw e('pointer')\n\tif ('interlace' !== o.scan && 'progressive' !== o.scan) throw e('scan')\n\tif ('none' !== o.update && 'slow' !== o.update && 'fast' !== o.update) throw e('update')\n\tif (!(Number.isInteger(o.widthPx) && o.widthPx >= 0)) throw e('widthPx')\n\tif (!(Number.isInteger(o.heightPx) && o.heightPx >= 0)) throw e('heightPx')\n\tif (!(Number.isInteger(o.deviceWidthPx) && o.deviceWidthPx >= 0)) throw e('deviceWidthPx')\n\tif (!(Number.isInteger(o.deviceHeightPx) && o.deviceHeightPx >= 0)) throw e('deviceHeightPx')\n\tif (!(Number.isInteger(o.colorBits) && o.colorBits >= 0)) throw e('colorBits')\n\tif (o.dppx <= 0) throw e('dppx')\n\tif (\n\t\t'not-monochrome' !== o.monochromeBits &&\n\t\t!(Number.isInteger(o.monochromeBits) && o.monochromeBits >= 0)\n\t)\n\t\tthrow e('monochromeBits')\n\tif ('none' !== o.colorIndex && !(Number.isInteger(o.colorIndex) && o.colorIndex >= 0))\n\t\tthrow e('colorIndex')\n}\nexport const matches = (e, o) => {\n\tconst r = {...DESKTOP_ENVIRONMENT, ...o}\n\tvalidateEnv(r)\n\tfor (const o of e.simplePerms) {\n\t\tlet e = !0\n\t\tfor (const i in o) {\n\t\t\tconst t = i,\n\t\t\t\tn = o\n\t\t\tif ('media-type' === t) {\n\t\t\t\tconst o = n[t]\n\t\t\t\tif ('print' === o) {\n\t\t\t\t\tif ('screen' === r.mediaType || 'not-screen-or-print' === r.mediaType) {\n\t\t\t\t\t\te = !1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t} else if ('screen' === o) {\n\t\t\t\t\tif ('print' === r.mediaType || 'not-screen-or-print' === r.mediaType) {\n\t\t\t\t\t\te = !1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t} else if ('not-screen' === o) {\n\t\t\t\t\tif ('screen' === r.mediaType) {\n\t\t\t\t\t\te = !1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t} else if ('print' === r.mediaType) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('any-hover' === t) {\n\t\t\t\tif (n[t] !== r.anyHover) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('hover' === t) {\n\t\t\t\tif (n[t] !== r.hover) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('any-pointer' === t) {\n\t\t\t\tif (n[t] !== r.anyPointer) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('pointer' === t) {\n\t\t\t\tif (n[t] !== r.pointer) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('grid' === t) {\n\t\t\t\tconst o = n[t]\n\t\t\t\tif ((0 === o && 'grid' === r.grid) || (1 === o && 'bitmap' === r.grid)) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('color-gamut' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\t('not-srgb' === r.colorGamut && !o) ||\n\t\t\t\t\t('srgb-but-not-p3' === r.colorGamut && !i) ||\n\t\t\t\t\t('p3-but-not-rec2020' === r.colorGamut && !s) ||\n\t\t\t\t\t('rec2020' === r.colorGamut && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('video-color-gamut' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\t('not-srgb' === r.videoColorGamut && !o) ||\n\t\t\t\t\t('srgb-but-not-p3' === r.videoColorGamut && !i) ||\n\t\t\t\t\t('p3-but-not-rec2020' === r.videoColorGamut && !s) ||\n\t\t\t\t\t('rec2020' === r.videoColorGamut && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('overflow-block' === t) {\n\t\t\t\tif (n[t] !== r.overflowBlock) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('overflow-inline' === t) {\n\t\t\t\tif (n[t] !== r.overflowInline) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('scan' === t) {\n\t\t\t\tif (n[t] !== r.scan) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('update' === t) {\n\t\t\t\tif (n[t] !== r.update) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('scripting' === t) {\n\t\t\t\tif (n[t] !== r.scripting) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('display-mode' === t) {\n\t\t\t\tif (n[t] !== r.displayMode) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('environment-blending' === t) {\n\t\t\t\tif (n[t] !== r.environmentBlending) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('forced-colors' === t) {\n\t\t\t\tif (n[t] !== r.forcedColors) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('inverted-colors' === t) {\n\t\t\t\tif (n[t] !== r.invertedColors) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('nav-controls' === t) {\n\t\t\t\tif (n[t] !== r.navControls) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('prefers-color-scheme' === t) {\n\t\t\t\tif (n[t] !== r.prefersColorScheme) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('prefers-contrast' === t) {\n\t\t\t\tif (n[t] !== r.prefersContrast) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('prefers-reduced-data' === t) {\n\t\t\t\tif (n[t] !== r.prefersReducedData) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('prefers-reduced-motion' === t) {\n\t\t\t\tif (n[t] !== r.prefersReducedMotion) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('prefers-reduced-transparency' === t) {\n\t\t\t\tif (n[t] !== r.prefersReducedTransparency) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('dynamic-range' === t) {\n\t\t\t\tif ('high' === n[t] && 'not-hdr' === r.dynamicRange) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('video-dynamic-range' === t) {\n\t\t\t\tif ('high' === n[t] && 'not-hdr' === r.videoDynamicRange) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('vertical-viewport-segments' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\tr.verticalViewportSegments < i ||\n\t\t\t\t\tr.verticalViewportSegments > s ||\n\t\t\t\t\t(i === r.verticalViewportSegments && !o) ||\n\t\t\t\t\t(s === r.verticalViewportSegments && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('horizontal-viewport-segments' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\tr.horizontalViewportSegments < i ||\n\t\t\t\t\tr.horizontalViewportSegments > s ||\n\t\t\t\t\t(i === r.horizontalViewportSegments && !o) ||\n\t\t\t\t\t(s === r.horizontalViewportSegments && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('width' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (r.widthPx < i || r.widthPx > s || (i === r.widthPx && !o) || (s === r.widthPx && !c)) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('device-width' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\tr.deviceWidthPx < i ||\n\t\t\t\t\tr.deviceWidthPx > s ||\n\t\t\t\t\t(i === r.deviceWidthPx && !o) ||\n\t\t\t\t\t(s === r.deviceWidthPx && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('height' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\tr.heightPx < i ||\n\t\t\t\t\tr.heightPx > s ||\n\t\t\t\t\t(i === r.heightPx && !o) ||\n\t\t\t\t\t(s === r.heightPx && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('device-height' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\tr.deviceHeightPx < i ||\n\t\t\t\t\tr.deviceHeightPx > s ||\n\t\t\t\t\t(i === r.deviceHeightPx && !o) ||\n\t\t\t\t\t(s === r.deviceHeightPx && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('color' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (\n\t\t\t\t\tr.colorBits < i ||\n\t\t\t\t\tr.colorBits > s ||\n\t\t\t\t\t(i === r.colorBits && !o) ||\n\t\t\t\t\t(s === r.colorBits && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('monochrome' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif ('not-monochrome' === r.monochromeBits)\n\t\t\t\t\t(i > 0 || (0 === i && !o) || (0 === s && !c)) && (e = !1)\n\t\t\t\telse if (\n\t\t\t\t\tr.monochromeBits < i ||\n\t\t\t\t\tr.monochromeBits > s ||\n\t\t\t\t\t(i === r.monochromeBits && !o) ||\n\t\t\t\t\t(s === r.monochromeBits && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('resolution' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif (r.dppx < i || r.dppx > s || (i === r.dppx && !o) || (s === r.dppx && !c)) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('color-index' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t]\n\t\t\t\tif ('none' === r.colorIndex) {\n\t\t\t\t\tif (i > 0 || (0 === i && !o) || (0 === s && !c)) {\n\t\t\t\t\t\te = !1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t} else if (\n\t\t\t\t\tr.colorIndex < i ||\n\t\t\t\t\tr.colorIndex > s ||\n\t\t\t\t\t(i === r.colorIndex && !o) ||\n\t\t\t\t\t(s === r.colorIndex && !c)\n\t\t\t\t) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else if ('aspect-ratio' === t) {\n\t\t\t\tconst [o, i, s, c] = n[t],\n\t\t\t\t\ta = i[0] / i[1],\n\t\t\t\t\tl = s[0] / s[1],\n\t\t\t\t\tf = r.widthPx / r.heightPx\n\t\t\t\tif (f < a || f > l || (a === f && !o) || (l === f && !c)) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconst [o, i, s, c] = n[t],\n\t\t\t\t\ta = i[0] / i[1],\n\t\t\t\t\tl = s[0] / s[1],\n\t\t\t\t\tf = r.deviceWidthPx / r.deviceHeightPx\n\t\t\t\tif (f < a || f > l || (a === f && !o) || (l === f && !c)) {\n\t\t\t\t\te = !1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (e) return !0\n\t}\n\treturn !1\n}\n"], "mappings": "AAAA,OAAS,YAAAA,GAAU,gBAAAC,GAAc,aAAAC,OAAuB,cACxD,OAAS,oBAAAC,GAAkB,eAAAC,OAAmB,iBCGvC,IAAIC,GAAU,OACVC,GAAU,OACVC,GAAc,OCFlB,IAAIC,GAAM,KAAK,IAMXC,EAAO,OAAO,aAqBlB,SAASC,EAAMC,EAAO,CAC5B,OAAOA,EAAM,KAAK,CACnB,CAiBO,SAASC,EAASC,EAAOC,EAASC,EAAa,CACrD,OAAOF,EAAM,QAAQC,EAASC,CAAW,CAC1C,CAQO,SAASC,GAASH,EAAOI,EAAQC,EAAU,CACjD,OAAOL,EAAM,QAAQI,EAAQC,CAAQ,CACtC,CAOO,SAASC,EAAQN,EAAOO,EAAO,CACrC,OAAOP,EAAM,WAAWO,CAAK,EAAI,CAClC,CAQO,SAASC,EAAQR,EAAOS,EAAOC,EAAK,CAC1C,OAAOV,EAAM,MAAMS,EAAOC,CAAG,CAC9B,CAMO,SAASC,EAAQX,EAAO,CAC9B,OAAOA,EAAM,MACd,CAMO,SAASY,GAAQZ,EAAO,CAC9B,OAAOA,EAAM,MACd,CAOO,SAASa,EAAQb,EAAOc,EAAO,CACrC,OAAOA,EAAM,KAAKd,CAAK,EAAGA,CAC3B,CCxGO,IAAIe,GAAO,EACPC,EAAS,EACTC,GAAS,EACTC,EAAW,EACXC,EAAY,EACZC,EAAa,GAYjB,SAASC,GAAMC,EAAOC,EAAMC,EAAQC,EAAMC,EAAOC,EAAUV,EAAQW,EAAU,CACnF,MAAO,CAAC,MAAON,EAAO,KAAMC,EAAM,OAAQC,EAAQ,KAAMC,EAAM,MAAOC,EAAO,SAAUC,EAAU,KAAMZ,GAAM,OAAQC,EAAQ,OAAQC,EAAQ,OAAQ,GAAI,SAAUW,CAAQ,CAC3K,CAwBO,SAASC,IAAQ,CACvB,OAAOC,CACR,CAKO,SAASC,IAAQ,CACvB,OAAAD,EAAYE,EAAW,EAAIC,EAAOC,EAAY,EAAEF,CAAQ,EAAI,EAExDG,IAAUL,IAAc,KAC3BK,EAAS,EAAGC,MAENN,CACR,CAKO,SAASO,GAAQ,CACvB,OAAAP,EAAYE,EAAWM,GAASL,EAAOC,EAAYF,GAAU,EAAI,EAE7DG,IAAUL,IAAc,KAC3BK,EAAS,EAAGC,MAENN,CACR,CAKO,SAASS,GAAQ,CACvB,OAAON,EAAOC,EAAYF,CAAQ,CACnC,CAKO,SAASQ,GAAS,CACxB,OAAOR,CACR,CAOO,SAASS,GAAOC,EAAOC,EAAK,CAClC,OAAOC,EAAOV,EAAYQ,EAAOC,CAAG,CACrC,CAMO,SAASE,EAAOC,EAAM,CAC5B,OAAQA,EAAM,CAEb,IAAK,GAAG,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IACtC,MAAO,GAER,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,KAE3D,IAAK,IAAI,IAAK,KAAK,IAAK,KACvB,MAAO,GAER,IAAK,IACJ,MAAO,GAER,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAC/B,MAAO,GAER,IAAK,IAAI,IAAK,IACb,MAAO,EACT,CAEA,MAAO,EACR,CAMO,SAASC,GAAOC,EAAO,CAC7B,OAAOZ,GAAOD,EAAS,EAAGG,GAASW,EAAOf,EAAac,CAAK,EAAGhB,EAAW,EAAG,CAAC,CAC/E,CAMO,SAASkB,GAASF,EAAO,CAC/B,OAAOd,EAAa,GAAIc,CACzB,CAMO,SAASG,GAASL,EAAM,CAC9B,OAAOM,EAAKX,GAAMT,EAAW,EAAGqB,GAAUP,IAAS,GAAKA,EAAO,EAAIA,IAAS,GAAKA,EAAO,EAAIA,CAAI,CAAC,CAAC,CACnG,CAcO,SAASQ,GAAYC,EAAM,CACjC,MAAOC,EAAYC,EAAK,IACnBD,EAAY,IACfE,EAAK,EAIP,OAAOC,EAAMJ,CAAI,EAAI,GAAKI,EAAMH,CAAS,EAAI,EAAI,GAAK,GACvD,CAwBO,SAASI,GAAUC,EAAOC,EAAO,CACvC,KAAO,EAAEA,GAASC,EAAK,GAElB,EAAAC,EAAY,IAAMA,EAAY,KAAQA,EAAY,IAAMA,EAAY,IAAQA,EAAY,IAAMA,EAAY,KAA9G,CAGD,OAAOC,GAAMJ,EAAOK,EAAM,GAAKJ,EAAQ,GAAKK,EAAK,GAAK,IAAMJ,EAAK,GAAK,GAAG,CAC1E,CAMO,SAASK,GAAWC,EAAM,CAChC,KAAON,EAAK,GACX,OAAQC,EAAW,CAElB,KAAKK,EACJ,OAAOC,EAER,IAAK,IAAI,IAAK,IACTD,IAAS,IAAMA,IAAS,IAC3BD,GAAUJ,CAAS,EACpB,MAED,IAAK,IACAK,IAAS,IACZD,GAAUC,CAAI,EACf,MAED,IAAK,IACJN,EAAK,EACL,KACF,CAED,OAAOO,CACR,CAOO,SAASC,GAAWF,EAAMR,EAAO,CACvC,KAAOE,EAAK,GAEPM,EAAOL,IAAc,IAGpB,GAAIK,EAAOL,IAAc,IAAWG,EAAK,IAAM,GACnD,MAEF,MAAO,KAAOF,GAAMJ,EAAOS,EAAW,CAAC,EAAI,IAAME,EAAKH,IAAS,GAAKA,EAAON,EAAK,CAAC,CAClF,CAMO,SAASU,GAAYZ,EAAO,CAClC,KAAO,CAACa,EAAMP,EAAK,CAAC,GACnBJ,EAAK,EAEN,OAAOE,GAAMJ,EAAOS,CAAQ,CAC7B,CCxPO,SAASK,GAASC,EAAO,CAC/B,OAAOC,GAAQC,GAAM,GAAI,KAAM,KAAM,KAAM,CAAC,EAAE,EAAGF,EAAQG,GAAMH,CAAK,EAAG,EAAG,CAAC,CAAC,EAAGA,CAAK,CAAC,CACtF,CAcO,SAASE,GAAOF,EAAOI,EAAMC,EAAQC,EAAMC,EAAOC,EAAUC,EAAQC,EAAQC,EAAc,CAiBhG,QAhBIC,EAAQ,EACRC,EAAS,EACTC,EAASL,EACTM,EAAS,EACTC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAY,EACZC,EAAY,EACZC,EAAO,GACPC,EAAQhB,EACRiB,EAAWhB,EACXiB,EAAYnB,EACZoB,EAAaJ,EAEVH,GACN,OAAQF,EAAWI,EAAWA,EAAYM,EAAK,EAAG,CAEjD,IAAK,IACJ,GAAIV,GAAY,KAAOW,EAAOF,EAAYZ,EAAS,CAAC,GAAK,GAAI,CACxDe,GAAQH,GAAcI,EAAQC,GAAQV,CAAS,EAAG,IAAK,KAAK,EAAG,MAAOW,GAAIpB,EAAQF,EAAOE,EAAQ,CAAC,EAAI,CAAC,CAAC,GAAK,KAChHQ,EAAY,IACb,KACD,CAED,IAAK,IAAI,IAAK,IAAI,IAAK,IACtBM,GAAcK,GAAQV,CAAS,EAC/B,MAED,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IAC9BK,GAAcO,GAAWhB,CAAQ,EACjC,MAED,IAAK,IACJS,GAAcQ,GAASC,EAAM,EAAI,EAAG,CAAC,EACrC,SAED,IAAK,IACJ,OAAQC,EAAK,EAAG,CACf,IAAK,IAAI,IAAK,IACbC,EAAOC,GAAQC,GAAUZ,EAAK,EAAGQ,EAAM,CAAC,EAAG/B,EAAMC,EAAQM,CAAY,EAAGA,CAAY,GAC/E6B,EAAMvB,GAAY,CAAC,GAAK,GAAKuB,EAAMJ,EAAK,GAAK,CAAC,GAAK,IAAMK,EAAOf,CAAU,GAAKgB,EAAOhB,EAAY,GAAI,MAAM,IAAM,MAAKA,GAAc,KAC1I,MACD,QACCA,GAAc,GAChB,CACA,MAED,IAAK,KAAMR,EACVR,EAAOE,GAAO,EAAI6B,EAAOf,CAAU,EAAIN,EAExC,IAAK,KAAMF,EAAU,IAAK,IAAI,IAAK,GAClC,OAAQG,EAAW,CAElB,IAAK,GAAG,IAAK,KAAKF,EAAW,EAE7B,IAAK,IAAKN,EAAYO,GAAa,KAAIM,EAAaI,EAAQJ,EAAY,MAAO,EAAE,GAC5EV,EAAW,IAAMyB,EAAOf,CAAU,EAAIZ,GAAWI,IAAa,GAAKD,IAAa,KACnFoB,EAAOrB,EAAW,GAAK2B,GAAYjB,EAAa,IAAKpB,EAAMD,EAAQS,EAAS,EAAGH,CAAY,EAAIgC,GAAYb,EAAQJ,EAAY,IAAK,EAAE,EAAI,IAAKpB,EAAMD,EAAQS,EAAS,EAAGH,CAAY,EAAGA,CAAY,EACrM,MAED,IAAK,IAAIe,GAAc,IAEvB,QAGC,GAFAW,EAAOZ,EAAYmB,GAAQlB,EAAYtB,EAAMC,EAAQO,EAAOC,EAAQN,EAAOG,EAAQY,EAAMC,EAAQ,CAAC,EAAGC,EAAW,CAAC,EAAGV,EAAQN,CAAQ,EAAGA,CAAQ,EAE3Ia,IAAc,IACjB,GAAIR,IAAW,EACdX,GAAMwB,EAAYtB,EAAMqB,EAAWA,EAAWF,EAAOf,EAAUM,EAAQJ,EAAQc,CAAQ,MAEvF,QAAQT,IAAW,IAAMa,EAAOF,EAAY,CAAC,IAAM,IAAM,IAAMX,EAAQ,CAEtE,IAAK,KAAK,IAAK,KAAK,IAAK,KAAK,IAAK,KAClCb,GAAMF,EAAOyB,EAAWA,EAAWnB,GAAQ+B,EAAOO,GAAQ5C,EAAOyB,EAAWA,EAAW,EAAG,EAAGlB,EAAOG,EAAQY,EAAMf,EAAOgB,EAAQ,CAAC,EAAGT,EAAQU,CAAQ,EAAGA,CAAQ,EAAGjB,EAAOiB,EAAUV,EAAQJ,EAAQJ,EAAOiB,EAAQC,CAAQ,EAC3N,MACD,QACCtB,GAAMwB,EAAYD,EAAWA,EAAWA,EAAW,CAAC,EAAE,EAAGD,EAAU,EAAGd,EAAQc,CAAQ,CACxF,CACJ,CAEAZ,EAAQC,EAASG,EAAW,EAAGE,EAAWE,EAAY,EAAGE,EAAOI,EAAa,GAAIZ,EAASL,EAC1F,MAED,IAAK,IACJK,EAAS,EAAI2B,EAAOf,CAAU,EAAGV,EAAWC,EAC7C,QACC,GAAIC,EAAW,GACd,GAAIG,GAAa,IAChB,EAAEH,UACMG,GAAa,KAAOH,KAAc,GAAK2B,GAAK,GAAK,IACzD,SAEF,OAAQnB,GAAcoB,EAAKzB,CAAS,EAAGA,EAAYH,EAAU,CAE5D,IAAK,IACJE,EAAYP,EAAS,EAAI,GAAKa,GAAc,KAAM,IAClD,MAED,IAAK,IACJhB,EAAOE,GAAO,GAAK6B,EAAOf,CAAU,EAAI,GAAKN,EAAWA,EAAY,EACpE,MAED,IAAK,IAEAgB,EAAK,IAAM,KACdV,GAAcK,GAAQJ,EAAK,CAAC,GAE7BZ,EAASqB,EAAK,EAAGvB,EAASC,EAAS2B,EAAOnB,EAAOI,GAAcqB,GAAWZ,EAAM,CAAC,CAAC,EAAGd,IACrF,MAED,IAAK,IACAJ,IAAa,IAAMwB,EAAOf,CAAU,GAAK,IAC5CR,EAAW,EACd,CACF,CAED,OAAOV,CACR,CAiBO,SAASoC,GAAS5C,EAAOI,EAAMC,EAAQO,EAAOC,EAAQN,EAAOG,EAAQY,EAAMC,EAAOC,EAAUV,EAAQkC,EAAU,CAKpH,QAJIC,EAAOpC,EAAS,EAChBP,EAAOO,IAAW,EAAIN,EAAQ,CAAC,EAAE,EACjC2C,EAAOC,GAAO7C,CAAI,EAEb8C,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGF,EAAIxC,EAAO,EAAEwC,EAC1C,QAASG,EAAI,EAAGC,EAAId,EAAO1C,EAAOiD,EAAO,EAAGA,EAAOjB,GAAIqB,EAAI3C,EAAO0C,CAAC,CAAC,CAAC,EAAGK,EAAIzD,EAAOuD,EAAIL,EAAM,EAAEK,GAC1FE,EAAIC,EAAKL,EAAI,EAAI/C,EAAKiD,CAAC,EAAI,IAAMC,EAAI1B,EAAQ0B,EAAG,OAAQlD,EAAKiD,CAAC,CAAC,CAAC,KACnEhC,EAAM+B,GAAG,EAAIG,GAEhB,OAAOE,GAAK3D,EAAOI,EAAMC,EAAQQ,IAAW,EAAI+C,GAAUtC,EAAMC,EAAOC,EAAUV,EAAQkC,CAAQ,CAClG,CASO,SAASV,GAAStC,EAAOI,EAAMC,EAAQ2C,EAAU,CACvD,OAAOW,GAAK3D,EAAOI,EAAMC,EAAQwD,GAASf,EAAKgB,GAAK,CAAC,EAAGpB,EAAO1C,EAAO,EAAG,EAAE,EAAG,EAAGgD,CAAQ,CAC1F,CAUO,SAASL,GAAa3C,EAAOI,EAAMC,EAAQS,EAAQkC,EAAU,CACnE,OAAOW,GAAK3D,EAAOI,EAAMC,EAAQ0D,GAAarB,EAAO1C,EAAO,EAAGc,CAAM,EAAG4B,EAAO1C,EAAOc,EAAS,EAAG,EAAE,EAAGA,EAAQkC,CAAQ,CACxH,CClMO,IAAMgB,EAAgB,GAAiB,OAAO,GAAnB,UAAiC,IAAT,MAAc,UAAW,EACtEC,GAAsB,GAAK,CACvC,IAAMC,EAAI,CAAC,CAAC,CAAC,EACZC,EAAI,CAAC,EACN,QAAWC,KAAK,EACf,GAAgBA,EAAE,OAAd,SAA4BD,EAAE,SAAR,EAAgBD,EAAE,KAAK,CAAC,CAAC,MAC9C,CACJ,OAAQE,EAAE,KAAM,CACf,IAAK,WACL,IAAK,IACJD,EAAE,KAAK,GAAG,EACV,MACD,IAAK,IACJA,EAAE,KAAK,GAAG,EACV,MACD,IAAK,IACJA,EAAE,KAAK,GAAG,EACV,MACD,IAAK,IACL,IAAK,IACL,IAAK,IACJA,EAAE,GAAG,EAAE,IAAMC,EAAE,MAAQD,EAAE,IAAI,EAC7B,KACF,CACAD,EAAEA,EAAE,OAAS,CAAC,EAAE,KAAKE,CAAC,CACvB,CACD,OAAOF,CACR,EACaG,GAAqB,GAAK,CACtC,IAAMH,EAAID,GAAoB,CAAC,EAC/B,GAAUC,EAAE,SAAR,GAAwBA,EAAE,CAAC,EAAE,SAAX,EACrB,MAAO,CAAC,KAAM,aAAc,aAAc,CAAC,CAAC,KAAM,OAAO,CAAC,CAAC,EAC5D,CACC,IAAMI,EAAI,CAAC,EACX,QAAWH,KAAKD,EAAG,CAClB,IAAMA,EAAIK,GAAeJ,CAAC,EAC1BH,EAAcE,CAAC,EAAII,EAAE,KAAK,CAAC,KAAM,QAAS,OAAQ,KAAK,CAAC,EAAIA,EAAE,KAAKJ,CAAC,CACrE,CACA,MAAO,CAAC,KAAM,aAAc,aAAcI,CAAC,CAC5C,CACD,EACaC,GAAiB,GAAK,CAClC,IAAIL,EAAGC,EAAGC,EACV,IAAMI,EAAI,EAAE,GAAG,CAAC,EAChB,GAAIA,EAAG,CACN,GAAYA,EAAE,OAAV,IAAgB,CACnB,IAAML,EAAIM,EAAmB,EAAG,EAAE,EAClC,GAAIT,EAAcG,CAAC,EAAG,CACrB,GAAM,CAAC,MAAOC,EAAG,IAAKM,CAAC,GAAcR,EAAI,EAAE,GAAG,CAAC,KAApB,MAAqCA,IAAX,OAAeA,EAAIM,EACxE,MAAO,CAAC,MAAO,8BAA+B,MAAOJ,EAAG,IAAKM,EAAG,MAAOP,CAAC,CACzE,CACA,MAAO,CAAC,KAAM,QAAS,eAAgBA,CAAC,CACzC,CACA,GAAgBK,EAAE,OAAd,QAAoB,CACvB,IAAIN,EAAG,EACD,CAAC,MAAOS,EAAG,IAAKC,CAAC,EAAIJ,EACdG,IAAX,QAA0BA,IAAV,QAAiBT,EAAIS,GACvC,IAAME,EAAeX,IAAX,OAAe,EAAI,EAC5BY,EAAI,EAAE,GAAGD,CAAC,EACX,GAAI,CAACC,EAAG,MAAO,CAAC,MAAO,wBAAyB,MAAOF,EAAG,IAAKA,CAAC,EAChE,GAAgBE,EAAE,OAAd,QAAoB,CACvB,GAAcZ,IAAV,OAAuBY,EAAE,OAAV,IAAgB,CAClC,IAAMZ,EAAIO,EAAmB,EAAE,MAAMI,CAAC,EAAG,EAAE,EAC3C,GAAIb,EAAcE,CAAC,EAAG,CACrB,GAAM,CAAC,MAAOE,EAAG,IAAKI,CAAC,GAAcL,EAAI,EAAE,GAAGU,EAAI,CAAC,KAAxB,MAAyCV,IAAX,OAAeA,EAAIW,EAC5E,MAAO,CAAC,MAAO,mBAAoB,MAAOV,EAAG,IAAKI,EAAG,MAAON,CAAC,CAC9D,CACA,MAAO,CACN,KAAM,QACN,eAAgB,CAAC,KAAM,YAAa,SAAU,MAAO,SAAU,CAACA,CAAC,CAAC,CACnE,CACD,CACA,CACC,GAAM,CAAC,MAAOI,EAAG,IAAKJ,CAAC,EAAIY,EAC3B,MAAO,CAAC,MAAO,cAAe,MAAOR,EAAG,IAAKJ,CAAC,CAC/C,CACD,CACA,CACC,GAAM,CAAC,MAAOI,EAAG,MAAOH,EAAG,IAAKC,CAAC,EAAIU,EACrC,GAAcR,IAAV,MAAa,EAAI,eACAA,IAAZ,SAA8BA,IAAb,SAAgB,EAAIA,MACzC,CACJ,GACWA,IAAV,OACSA,IAAT,MACiBA,IAAjB,cACeA,IAAf,YACcA,IAAd,WACeA,IAAf,YACYA,IAAZ,SACaA,IAAb,SAEA,MAAO,CAAC,MAAO,cAAe,MAAOH,EAAG,IAAKC,CAAC,EAC7CF,EAAcA,IAAV,MAAc,OAAS,MAAS,EAAI,MAC3C,CACD,CACA,GAAIW,EAAI,IAAM,EAAE,OAAQ,MAAO,CAAC,KAAM,QAAS,OAAQX,EAAG,UAAW,CAAC,EACtE,CACC,IAAMC,EAAI,EAAEU,EAAI,CAAC,EACjB,GAAgBV,EAAE,OAAd,SAAgCA,EAAE,QAAZ,MAAmB,CAC5C,IAAMA,EAAI,EAAE,GAAG,EAAE,EAChBK,EAAI,EAAE,GAAGK,EAAI,CAAC,EACXF,EACHC,EAAIT,EAAE,IAAM,EACb,IAAyBK,GAAR,KAAY,OAASA,EAAE,QAApC,SAAuDA,EAAE,QAAZ,MAAmB,CACnEI,GAAK,EACL,IAAMV,EAAIO,EAAmB,EAAE,MAAMI,EAAI,CAAC,EAAG,EAAE,EAC/CF,EAAIX,EAAcE,CAAC,EAAIA,EAAI,CAAC,KAAM,YAAa,SAAU,MAAO,SAAU,CAACA,CAAC,CAAC,CAC9E,MAAOS,EAAIF,EAAmB,EAAE,MAAMI,EAAI,CAAC,EAAG,EAAE,EAChD,GAAM,CAAC,MAAOC,EAAG,IAAKC,CAAC,GACZX,EAAI,EAAE,GAAGS,EAAI,CAAC,KAAxB,MAAyCT,IAAX,OAAeA,EAAI,CAAC,MAAOQ,EAAG,IAAKA,CAAC,EACnE,OAAOZ,EAAcW,CAAC,EACnB,CAAC,MAAO,mBAAoB,MAAOG,EAAG,IAAKC,EAAG,MAAOJ,CAAC,EACtD,CAAC,KAAM,QAAS,OAAQT,EAAG,UAAW,EAAG,eAAgBS,CAAC,CAC9D,CACA,MAAO,CAAC,MAAO,aAAc,MAAOR,EAAE,MAAO,IAAKA,EAAE,GAAG,CACxD,CACD,CACA,MAAO,CAAC,MAAO,oCAAqC,MAAOK,EAAE,MAAO,IAAKA,EAAE,GAAG,CAC/E,CACA,MAAO,CAAC,MAAO,cAAe,MAAO,EAAG,IAAK,CAAC,CAC/C,EACaC,EAAqB,CAAC,EAAGP,EAAGC,IAAM,CAC9C,IAAMC,EAAI,EAAE,GAAG,CAAC,EAChB,GAAIA,EAAG,CACN,GAAYA,EAAE,OAAV,IAAgB,MAAO,CAAC,MAAO,gBAAiB,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EAC9E,IAAII,EACHE,EAAI,EAAE,OAAS,EACfC,EAAI,EACJ,EAAI,EACL,OAAW,CAACT,EAAGC,CAAC,IAAK,EAAE,QAAQ,EAC9B,GACUA,EAAE,OAAV,KAAmB,GAAK,EAAKQ,EAAI,KAAK,IAAIA,EAAG,CAAC,GAAcR,EAAE,OAAV,MAAmB,GAAK,GAAU,IAAN,EAChF,CACDO,EAAIR,EACJ,KACD,CACD,GAAU,IAAN,EAAS,MAAO,CAAC,MAAO,kBAAmB,MAAOE,EAAE,MAAO,IAAK,EAAE,EAAE,OAAS,CAAC,EAAE,GAAG,EACvF,IAAMS,EAAI,EAAE,MAAM,EAAGH,EAAI,CAAC,EAC1B,GACGF,EACKG,IAAN,EACGK,GAAiBH,CAAC,EACNA,EAAE,CAAC,EAAE,OAAjB,SAAmCA,EAAE,CAAC,EAAE,QAAf,MACzBJ,EAAmBI,EAAE,MAAM,EAAG,EAAE,EAAG,GAAI,KAAK,EAC5CJ,EAAmBI,EAAE,MAAM,EAAG,EAAE,EAAG,EAAE,EACzCb,EAAcQ,CAAC,EAEf,MAAO,CACN,MAAO,8BACP,MAAOJ,EAAE,MACT,IAAKS,EAAEA,EAAE,OAAS,CAAC,EAAE,IACrB,MAAOL,CACR,EACD,GAAIE,IAAM,EAAE,OAAS,EAAG,MAAO,CAAC,KAAM,YAAa,SAAUP,EAAG,SAAU,CAACK,CAAC,CAAC,EAC7E,CACC,IAAMJ,EAAI,EAAEM,EAAI,CAAC,EACjB,GAAgBN,EAAE,OAAd,SAAiCA,EAAE,QAAZ,OAA8BA,EAAE,QAAX,KAC/C,MAAO,CAAC,MAAO,mBAAoB,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EAC9D,GAAeD,IAAX,QAAgBA,IAAMC,EAAE,MAC3B,MAAO,CAAC,MAAO,kBAAmB,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EAC7D,GAAaA,EAAE,QAAX,MAAoB,CAACF,EAAG,MAAO,CAAC,MAAO,kBAAmB,MAAOE,EAAE,MAAO,IAAKA,EAAE,GAAG,EACxF,IAAMO,EAAIF,EAAmB,EAAE,MAAMC,EAAI,CAAC,EAAGR,EAAGE,EAAE,KAAK,EACvD,OAAOJ,EAAcW,CAAC,EACnBA,EACA,CAAC,KAAM,YAAa,SAAUP,EAAE,MAAO,SAAU,CAACI,EAAG,GAAGG,EAAE,QAAQ,CAAC,CACvE,CACD,CACA,MAAO,CAAC,MAAO,kBAAmB,MAAO,EAAG,IAAK,CAAC,CACnD,EACaK,GAAmB,GAAK,CACpC,IAAMd,EAAI,EAAE,GAAG,CAAC,EAChB,GAAIA,EAAG,CACN,GAAYA,EAAE,OAAV,IAAgB,MAAO,CAAC,MAAO,gBAAiB,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EAC9E,IAAMC,EAAI,EAAE,EAAE,OAAS,CAAC,EACxB,GAAYA,EAAE,OAAV,IAAgB,MAAO,CAAC,MAAO,gBAAiB,MAAOA,EAAE,IAAM,EAAG,IAAKA,EAAE,IAAM,CAAC,EACpF,IAAMC,EAAI,CAAC,EAAE,CAAC,CAAC,EACf,QAASF,EAAI,EAAGA,EAAI,EAAE,OAAQA,IAAK,CAClC,GAAIA,EAAI,EAAE,OAAS,EAAG,CACrB,IAAMC,EAAI,EAAED,CAAC,EACZM,EAAI,EAAEN,EAAI,CAAC,EACXQ,EAAI,EAAER,EAAI,CAAC,EACZ,GACcC,EAAE,OAAf,UACAA,EAAE,MAAQ,GACEK,EAAE,OAAd,SACOA,EAAE,QAAT,IACaE,EAAE,OAAf,UACAA,EAAE,MAAQ,EACT,CACDN,EAAE,KAAK,CACN,KAAM,QACN,UAAWD,EAAE,MACb,YAAaO,EAAE,MACf,eAAgBP,EAAE,eAClB,cAAeO,EAAE,cACjB,MAAOP,EAAE,MACT,IAAKO,EAAE,GACR,CAAC,EACCR,GAAK,EACP,QACD,CACD,CACAE,EAAE,KAAK,EAAEF,CAAC,CAAC,CACZ,CACA,IAAMM,EAAIJ,EAAE,CAAC,EACb,GAAgBI,EAAE,OAAd,SAA4BJ,EAAE,SAAR,EACzB,MAAO,CAAC,KAAM,UAAW,QAAS,UAAW,QAASI,EAAE,KAAK,EAC9D,GAAUJ,EAAE,SAAR,GAA8BA,EAAE,CAAC,EAAE,OAAjB,SAAqCA,EAAE,CAAC,EAAE,OAAjB,QAAuB,CACrE,IAAME,EAAIF,EAAE,CAAC,EACb,GACcE,EAAE,OAAf,UACgBA,EAAE,OAAlB,aACYA,EAAE,OAAd,SACYA,EAAE,OAAd,QACC,CACD,IAAIJ,EACHC,EAAIC,EAAE,CAAC,EAAE,MACJI,EAAIL,EAAE,MAAM,EAAG,CAAC,EACXK,IAAX,QACKN,EAAI,MAASC,EAAIA,EAAE,MAAM,CAAC,GACjBK,IAAX,SAAkBN,EAAI,MAASC,EAAIA,EAAE,MAAM,CAAC,GAC/C,GAAM,CAAC,eAAgBO,EAAG,cAAeC,EAAG,MAAOC,EAAG,IAAKC,EAAG,GAAGC,CAAC,EAAIR,EACtE,MAAO,CAAC,KAAM,UAAW,QAAS,QAAS,OAAQJ,EAAG,QAASC,EAAG,MAAOW,CAAC,CAC3E,CACA,MAAO,CAAC,MAAO,eAAgB,MAAOR,EAAE,MAAO,IAAKA,EAAE,GAAG,CAC1D,CACA,GAAIF,EAAE,QAAU,EAAG,CAClB,IAAME,EAAIW,GAAUb,CAAC,EACrB,GAAIJ,EAAcM,CAAC,EAClB,MAAO,CAAC,MAAO,eAAgB,MAAOJ,EAAE,MAAO,IAAKE,EAAEA,EAAE,OAAS,CAAC,EAAE,IAAK,MAAOE,CAAC,EAClF,CACC,GAAM,CAAC,QAASJ,EAAG,GAAGC,CAAC,EAAIG,EAC3B,MAAO,CAAC,KAAM,UAAW,QAAS,QAAS,QAASJ,EAAG,MAAOC,CAAC,CAChE,CACD,CACA,MAAO,CAAC,MAAO,kBAAmB,MAAOD,EAAE,MAAO,IAAK,EAAE,EAAE,OAAS,CAAC,EAAE,GAAG,CAC3E,CACA,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAG,IAAK,CAAC,CACjD,EACae,GAAY,GAAK,CAC7B,IAAIf,EAAGC,EAAGC,EAAGI,EAAGE,EAAGC,EAAG,EAAGE,EACzB,GAAI,EAAE,OAAS,EACd,MAAO,CACN,MAAO,gBACP,OACWV,GAAcD,EAAI,EAAE,GAAG,CAAC,KAApB,MAAqCA,IAAX,OAAe,OAASA,EAAE,SAAlE,MAAuFC,IAAX,OACzEA,EACA,EACJ,KACWK,GAAcJ,EAAI,EAAE,GAAG,EAAE,KAArB,MAAsCA,IAAX,OAAe,OAASA,EAAE,OAAnE,MAAsFI,IAAX,OACxEA,EACA,CACL,EACD,GAAY,EAAE,CAAC,EAAE,OAAb,IAAmB,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAK,EAAE,CAAC,EAAE,GAAG,EACvF,IAAMM,EAAI,EAAE,EAAE,OAAS,CAAC,EACxB,GAAYA,EAAE,OAAV,IAAgB,MAAO,CAAC,MAAO,gBAAiB,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EAC9E,IAAM,EAAI,CAAC,QAAS,EAAE,EACrBI,EACc,EAAE,CAAC,EAAE,OAAlB,UACgB,EAAE,CAAC,EAAE,OAArB,aACY,EAAE,CAAC,EAAE,OAAjB,SACa,EAAE,CAAC,EAAE,OAAjB,SAAwC,EAAE,CAAC,EAAE,QAApB,WAC5B,GAAgB,EAAE,CAAC,EAAE,OAAjB,QAAuB,CAC1B,GAAW,EAAE,CAAC,EAAE,QAAZ,GACS,EAAE,CAAC,EAAE,OAAjB,SAAgC,EAAE,CAAC,EAAE,QAAZ,IAAqB,EAAE,CAAC,EAAE,eAC/C,EAAEA,EAAI,SAAW,SAAS,EAAI,IAC9B,EAAEA,EAAI,SAAW,SAAS,EAAI,aACnB,EAAE,CAAC,EAAE,QAAZ,GACI,EAAE,CAAC,EAAE,OAAjB,SAAgC,EAAE,CAAC,EAAE,QAAZ,IAAqB,EAAE,CAAC,EAAE,eAC/C,EAAEA,EAAI,SAAW,SAAS,EAAI,IAC9B,EAAEA,EAAI,SAAW,SAAS,EAAI,SAC9B,CACJ,GAAW,EAAE,CAAC,EAAE,QAAZ,GAAmB,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKJ,EAAE,GAAG,EACpF,EAAEI,EAAI,SAAW,SAAS,EAAI,GAC/B,CACA,GAAIA,EAAG,EAAE,UAAY,EAAE,CAAC,MACnB,CACJ,GAAgB,EAAE,CAAC,EAAE,OAAjB,QAAuB,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKJ,EAAE,GAAG,EACxF,EAAE,QAAU,EAAE,CAAC,EAAE,KAClB,CACA,IAAMZ,EACJ,IAEES,GAAcD,EAAI,EAAEQ,EAAI,SAAW,SAAS,KAAxC,MAAyDR,IAAX,OAAe,OAASA,EAAE,UAD7E,MAEUC,IAAX,OACGA,EACA,GACJR,EAAI,EAAED,CAAC,EACR,GAAIgB,EAAG,CACN,GAAgBf,EAAE,OAAd,QAAoB,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKW,EAAE,GAAG,EACrF,GAAM,EAAE,QAAUX,EAAE,MAAQ,EAAE,QAAU,EAAI,CAC3C,IAAMA,EAAI,EAAED,EAAI,CAAC,EAChBE,EAAI,EAAEF,EAAI,CAAC,EACZ,GAAgBC,EAAE,OAAd,QAAoB,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKW,EAAE,GAAG,EACrF,CACC,IAAMN,EAAIL,EAAE,MACZ,GAAWK,IAAP,GACSJ,EAAE,OAAd,SAA6BA,EAAE,QAAT,IAAkBA,EAAE,eACtC,EAAE,QAAU,IACZ,EAAE,QAAU,SACZ,CACJ,GAAWI,IAAP,GAAU,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKM,EAAE,GAAG,EAC/DV,EAAE,OAAd,SAA6BA,EAAE,QAAT,IAAkBA,EAAE,eACtC,EAAE,QAAU,IACZ,EAAE,QAAU,IACjB,CACA,IAAMM,EACJR,EACA,IACWW,GAAc,EAAI,EAAE,WAAhB,MAAuC,IAAX,OAAe,OAAS,EAAE,UAApE,MACUA,IAAX,OACGA,EACA,GACJF,EAAI,EAAE,GAAGD,CAAC,EACX,GAAIA,EAAI,IAAM,EAAE,OAAQ,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKI,EAAE,GAAG,EACrF,EAAE,WAAaH,CAChB,CACD,SAAWT,EAAI,IAAM,EAAE,OAAQ,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKY,EAAE,GAAG,CAC7F,MAAO,EAAE,WAAaX,EACtB,IAAIC,EACE,CAAC,UAAWI,EAAG,OAAQW,EAAG,QAASC,EAAG,QAAS,EAAG,WAAYC,CAAC,EAAI,EACrE,EAAGC,EACP,GAAed,IAAX,QACH,GAAgBA,EAAE,OAAd,QAAoB,CACvB,GAAM,CAAC,KAAMF,EAAG,MAAOJ,CAAC,EAAIM,EACbN,IAAf,aAAqB,EAAI,CAAC,KAAMI,EAAG,MAAOJ,CAAC,EAC5C,SAAwBM,EAAE,OAAf,UAAuCA,EAAE,OAAlB,aAAsCA,EAAE,OAAd,QAAoB,CAC/E,GAAM,CAAC,eAAgBF,EAAG,cAAeJ,EAAG,MAAOC,EAAG,IAAKC,EAAG,GAAGM,CAAC,EAAIF,EACtE,EAAIE,CACL,EACD,GAAeW,IAAX,QACH,GAAgBA,EAAE,OAAd,QAAoB,CACvB,GAAM,CAAC,KAAMf,EAAG,MAAOJ,CAAC,EAAImB,EACbnB,IAAf,aAAqBoB,EAAI,CAAC,KAAMhB,EAAG,MAAOJ,CAAC,EAC5C,SAAwBmB,EAAE,OAAf,UAAuCA,EAAE,OAAlB,aAAsCA,EAAE,OAAd,QAAoB,CAC/E,GAAM,CAAC,eAAgBf,EAAG,cAAeJ,EAAG,MAAOC,EAAG,IAAKC,EAAG,GAAGI,CAAC,EAAIa,EACtEC,EAAId,CACL,EACD,GAAe,IAAX,QAA2Bc,IAAX,OACnB,GAAaH,IAAR,KAAsBA,IAAT,MAAwB,IAAR,KAAsB,IAAT,KAAa,CAC3D,GAAaA,IAAR,KAAsBA,IAAT,MAAwB,IAAR,KAAsB,IAAT,KAC9C,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKL,EAAE,GAAG,EAC9DV,EAAI,CAAC,UAAW,EAAG,OAAQe,EAAG,QAASC,EAAG,QAAS,EAAG,WAAYE,CAAC,CACpE,MAAOlB,EAAI,CAAC,UAAW,EAAG,OAAQe,EAAG,QAASC,EAAG,QAAS,EAAG,WAAYE,CAAC,OAE7D,IAAX,QAA2BH,IAAX,QAA2B,IAAX,QAA2BG,IAAX,QACrC,IAAX,QAA2BH,IAAX,QAA2B,IAAX,QAA2BG,IAAX,UAChDlB,EAAI,CAAC,UAAW,EAAG,OAAQe,EAAG,QAASC,EAAG,QAAS,EAAG,WAAYE,CAAC,GACtE,OAAelB,GAAQ,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKU,EAAE,GAAG,CAC9E,CACA,MAAO,CAAC,MAAO,gBAAiB,MAAO,EAAE,CAAC,EAAE,MAAO,IAAKA,EAAE,GAAG,CAC9D,EChWO,IAAMS,GAAwB,IAAM,CAC1C,KAAM,aACN,aAAc,EAAE,aAAa,IAAIC,GAAKC,GAAkBD,CAAC,CAAC,CAC3D,GACaC,GAAoB,GAChC,EAAE,eACC,CACA,KAAM,QACN,OAAQ,EAAE,OACV,UAAW,EAAE,UACb,eAAgBC,GAAsB,EAAE,cAAc,CACtD,EACA,EACSA,GAAwB,GAAK,CACzC,IAAMC,EAAI,CAAC,EACX,QAAW,KAAK,EAAE,SACjB,GAAoB,EAAE,OAAlB,YAAwB,CAC3B,IAAMC,EAAIF,GAAsB,CAAC,EACtBE,EAAE,WAAb,QAA+BA,EAAE,SAAS,SAAjB,EACtBD,EAAE,KAAKC,EAAE,SAAS,CAAC,CAAC,EACpBA,EAAE,WAAa,EAAE,UAAuBA,EAAE,WAAZ,OAAiCA,EAAE,WAAX,KACtDD,EAAE,KAAKC,CAAC,EACRD,EAAE,KAAK,GAAGC,EAAE,QAAQ,CACxB,MAAOD,EAAE,KAAK,CAAC,EAChB,GAAUA,EAAE,SAAR,EAAgB,CACnB,IAAM,EAAIA,EAAE,CAAC,EACb,GAAoB,EAAE,OAAlB,YAAwB,CAC3B,GAAe,EAAE,WAAb,OAAuB,OAAO,EAClC,GAAc,EAAE,WAAZ,OAAkC,EAAE,WAAZ,MAC3B,MAAO,CAAC,KAAM,YAAa,SAAU,EAAE,QAAQ,CACjD,CACD,CACA,MAAO,CAAC,KAAM,YAAa,SAAU,EAAE,SAAU,SAAUA,CAAC,CAC7D,ECjCO,IAAME,GAAoB,GAAK,CACrC,IAAMC,EAAI,CAAC,CAAC,EACZ,QAASC,EAAI,EAAE,MAAkBA,IAAX,OAAcA,EAAIA,EAAE,MAAOD,EAAE,KAAKC,CAAC,EACzD,QAASC,EAAIF,EAAE,OAAS,EAAGE,GAAK,EAAGA,IAAKF,EAAEE,EAAI,CAAC,EAAE,MAAQF,EAAE,GAAGE,CAAC,EAC/D,OAAO,OAAOF,EAAE,CAAC,EAAE,MAAOA,EAAE,GAAG,EAAE,CAClC,EACaG,EAAwB,GAAK,CACzC,OAAQ,EAAE,KAAM,CACf,IAAK,aACJ,QAAWH,KAAK,EAAE,aAAcG,EAAsBH,CAAC,EACvD,OAAO,EACR,IAAK,QACJ,OACY,EAAE,SAAb,QAAuB,OAAO,EAAE,OACrB,EAAE,YAAb,QAA0B,OAAO,EAAE,UACxB,EAAE,iBAAb,OACG,OAAO,EAAE,eACTG,EAAsB,EAAE,cAAc,EACzC,EAEF,IAAK,YACO,EAAE,WAAb,QAAyB,OAAO,EAAE,SAClC,QAAWH,KAAK,EAAE,SAAUG,EAAsBH,CAAC,EACnD,OAAO,EACR,IAAK,UACJ,OACa,EAAE,UAAd,SACe,EAAE,SAAb,QAAuB,OAAO,EAAE,OAAQG,EAAsB,EAAE,KAAK,GAC1D,EAAE,UAAd,UACY,EAAE,MAAM,SAAnB,QAA6B,OAAO,EAAE,MAAM,OAClC,EAAE,MAAM,UAAnB,QAA8B,OAAO,EAAE,MAAM,QAClC,EAAE,MAAM,YAAnB,OACE,OAAO,EAAE,MAAM,UACfA,EAAsB,EAAE,MAAM,SAAS,EAC9B,EAAE,MAAM,aAAnB,OACE,OAAO,EAAE,MAAM,WACfA,EAAsB,EAAE,MAAM,UAAU,GAC7C,EAEF,QACC,OAAO,CACT,CACD,EC1CA,IAAIC,GACSC,GAAiBC,GAAK,CAClC,IAAMC,GAAK,IAAM,CACf,IAAID,EACJ,OAAOF,GAAKE,EAAIF,IAAOE,EAAI,IAAI,YAAiBF,GAAIE,GAAKA,CAC1D,GAAG,EAAE,OAAOA,CAAC,EACbE,EAAI,CAAC,EACLC,EAAIF,EAAE,OACP,QAASH,EAAI,EAAGA,EAAIK,EAAGL,GAAK,EAAG,CAC9B,IAAME,EAAIC,EAAE,GAAGH,CAAC,EAChB,GAAIE,EAAI,IACP,OAAQA,EAAG,CACV,IAAK,GACJE,EAAE,KAAK,KAAK,EACZ,MACD,IAAK,IACJA,EAAE,KAAK,EAAE,EACT,MACD,IAAK,IACJA,EAAE,KAAK,EAAE,EAAUD,EAAE,GAAGH,EAAI,CAAC,IAAjB,KAAuBA,GAAK,GACxC,MACD,QACCI,EAAE,KAAKF,CAAC,CACV,MAEAA,EAAI,IACDE,EAAE,KAAOF,GAAK,KAAQ,GAAQC,EAAE,EAAEH,CAAC,GAAK,KAAQ,EAAG,EACnDE,EAAI,IACJE,EAAE,KAAOF,GAAK,KAAQ,GAAQC,EAAE,EAAEH,CAAC,GAAK,KAAQ,GAAQG,EAAE,EAAEH,CAAC,GAAK,KAAQ,EAAG,EAC7EI,EAAE,KACAF,GAAK,KAAQ,GACZC,EAAE,EAAEH,CAAC,GAAK,KAAQ,GAClBG,EAAE,EAAEH,CAAC,GAAK,KAAQ,GAClBG,EAAE,EAAEH,CAAC,GAAK,KAAQ,EACrB,CACL,CACA,OAAOI,CACR,ECrCO,IAAME,GAAwB,GAAK,CACzC,IAAMC,EAAI,CAAC,EACPC,EAAI,GACR,QAAWC,KAAK,EACf,OAAQA,EAAE,KAAM,CACf,IAAK,IACJ,MAAO,CAAC,MAAO,YAAa,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EACvD,IAAK,YACJ,MAAO,CAAC,MAAO,eAAgB,MAAOA,EAAE,MAAO,IAAKA,EAAE,GAAG,EAC1D,IAAK,aACFD,EAAI,GAAKD,EAAE,OAAS,IAAMA,EAAEA,EAAE,OAAS,CAAC,EAAE,cAAgB,IAC5D,MACD,IAAK,MACJ,MACD,QACCA,EAAE,KAAK,CAAC,GAAGE,EAAG,eAAgBD,EAAG,cAAe,EAAE,CAAC,EAAIA,EAAI,EAC7D,CACD,OAAOD,CACR,ECRO,IAAMG,GAAqB,CAACC,EAAGC,EAAI,IAAM,CAC/C,IAAMC,EAAI,CAAC,EACX,KAAOD,EAAID,EAAE,OAAQC,GAAK,EAAG,CAC5B,IAAME,EAAIH,EAAE,GAAGC,CAAC,EACfG,EAAIH,EACL,GAAWE,IAAP,IAAmBH,EAAE,GAAGC,EAAI,CAAC,IAAjB,GAAoB,CACnCA,GAAK,EACL,QAASI,EAAIL,EAAE,GAAGC,CAAC,EAAcI,IAAX,OAAcA,EAAIL,EAAE,GAAG,EAAEC,CAAC,EAC/C,GAAWI,IAAP,IAAmBL,EAAE,GAAGC,EAAI,CAAC,IAAjB,GAAoB,CACnCA,GAAK,EACL,KACD,CACF,SAAiBE,IAAN,GAAWA,IAAM,IAAKA,IAAM,GAAG,CACzC,IAAIG,EAAIN,EAAE,GAAG,EAAEC,CAAC,EAChB,KAAaK,IAAN,GAAWA,IAAM,IAAKA,IAAM,IAAKA,EAAIN,EAAE,GAAG,EAAEC,CAAC,EACpDA,GAAK,EACL,IAAMM,EAAIL,EAAE,GAAG,EAAE,GACSK,GAAR,KAAY,OAASA,EAAE,QAAzC,cACIL,EAAE,IAAI,EAAGA,EAAE,KAAK,CAAC,KAAM,aAAc,MAAOK,EAAE,MAAO,IAAKN,CAAC,CAAC,GAC7DC,EAAE,KAAK,CAAC,KAAM,aAAc,MAAOE,EAAG,IAAKH,CAAC,CAAC,CACjD,SAAkBE,IAAP,GAAU,CACpB,IAAME,EAAIG,GAAcR,EAAGC,CAAC,EAC5B,GAAaI,IAAT,KAAY,MAAO,CAAC,MAAO,iBAAkB,MAAOJ,EAAG,IAAKA,CAAC,EACjE,GAAM,CAACQ,EAAGH,CAAC,EAAID,EACbJ,EAAIQ,EAAIP,EAAE,KAAK,CAAC,KAAM,SAAU,MAAOI,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,CAC9D,SAAkBE,IAAP,GAAU,CACpB,GAAIF,EAAI,EAAID,EAAE,OAAQ,CACrB,IAAMS,EAAIT,EAAE,GAAGC,EAAI,CAAC,EACpB,GACQQ,IAAP,IACCA,GAAK,IAAKA,GAAK,IACfA,GAAK,IAAKA,GAAK,KAChBA,GAAK,KACJA,GAAK,IAAKA,GAAK,IACfA,IAAM,IAAKR,EAAI,EAAID,EAAE,QAAUA,EAAE,GAAGC,EAAI,CAAC,IAAM,GAC/C,CACD,IAAMI,EAAIK,GAAqBV,EAAGC,EAAI,CAAC,EAAI,KAAO,eACjDQ,EAAIE,GAAmBX,EAAGC,EAAI,CAAC,EAChC,GAAaQ,IAAT,KAAY,CACf,GAAM,CAACH,EAAGC,CAAC,EAAIE,EACbR,EAAIK,EAAIJ,EAAE,KAAK,CAAC,KAAM,OAAQ,MAAOK,EAAE,YAAY,EAAG,KAAMF,EAAG,MAAOD,EAAG,IAAKH,CAAC,CAAC,EAClF,QACD,CACD,CACD,CACAC,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,CACnD,SAAkBE,IAAP,GAAU,CACpB,IAAME,EAAIG,GAAcR,EAAGC,CAAC,EAC5B,GAAaI,IAAT,KAAY,MAAO,CAAC,MAAO,iBAAkB,MAAOJ,EAAG,IAAKA,CAAC,EACjE,GAAM,CAACQ,EAAGH,CAAC,EAAID,EACbJ,EAAIQ,EAAIP,EAAE,KAAK,CAAC,KAAM,SAAU,MAAOI,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,CAC9D,SAAkBE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,IAAK,MAAOE,EAAG,IAAKH,CAAC,CAAC,UACzCE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,IAAK,MAAOE,EAAG,IAAKH,CAAC,CAAC,UACvCE,IAAP,GAAU,CAClB,IAAME,EAAIO,GAAeZ,EAAGC,CAAC,EAC7B,GAAaI,IAAT,KAAYH,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,MAC7D,CACJ,GAAM,CAACQ,EAAGH,CAAC,EAAID,EACbJ,EAAIQ,EACWH,EAAE,CAAC,IAAnB,YACGJ,EAAE,KAAK,CACP,KAAM,YACN,MAAOI,EAAE,CAAC,EACV,KAAMA,EAAE,CAAC,EAAE,YAAY,EACvB,KAAM,SACN,MAAOF,EACP,IAAKH,CACL,CAAC,EACYK,EAAE,CAAC,IAAhB,SACAJ,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAMA,EAAE,CAAC,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,EAC9DC,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAM,SAAU,MAAOF,EAAG,IAAKH,CAAC,CAAC,CACvE,CACD,SAAkBE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOE,EAAG,IAAKH,CAAC,CAAC,UACpDE,IAAM,GAAG,CACjB,IAAME,EAAIO,GAAeZ,EAAGC,CAAC,EAC7B,GAAaI,IAAT,KAAY,CACf,GAAM,CAACI,EAAGH,CAAC,EAAID,EACbJ,EAAIQ,EACWH,EAAE,CAAC,IAAnB,YACGJ,EAAE,KAAK,CACP,KAAM,YACN,MAAOI,EAAE,CAAC,EACV,KAAMA,EAAE,CAAC,EAAE,YAAY,EACvB,KAAM,SACN,MAAOF,EACP,IAAKH,CACL,CAAC,EACYK,EAAE,CAAC,IAAhB,SACAJ,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAMA,EAAE,CAAC,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,EAC9DC,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAM,SAAU,MAAOF,EAAG,IAAKH,CAAC,CAAC,EACtE,QACD,CACA,GAAIA,EAAI,EAAID,EAAE,OAAQ,CACrB,IAAMK,EAAIL,EAAE,GAAGC,EAAI,CAAC,EACnBQ,EAAIT,EAAE,GAAGC,EAAI,CAAC,EACf,GAAII,IAAM,IAAYI,IAAP,GAAU,CACtBR,GAAK,EAAIC,EAAE,KAAK,CAAC,KAAM,MAAO,MAAOE,EAAG,IAAKH,CAAC,CAAC,EACjD,QACD,CACD,CACA,IAAMQ,EAAII,GAAiBb,EAAGC,CAAC,EAC/B,GAAaQ,IAAT,KAAY,CACf,GAAM,CAACJ,EAAGC,EAAGC,CAAC,EAAIE,EAChBR,EAAII,EAAIH,EAAE,KAAK,CAAC,KAAMK,EAAG,MAAOD,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,EACtD,QACD,CACAC,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,CACnD,SAAkBE,IAAP,GAAU,CACpB,IAAME,EAAIO,GAAeZ,EAAGC,CAAC,EAC7B,GAAaI,IAAT,KAAY,CACf,GAAM,CAACI,EAAGH,CAAC,EAAID,EACbJ,EAAIQ,EACWH,EAAE,CAAC,IAAnB,YACGJ,EAAE,KAAK,CACP,KAAM,YACN,MAAOI,EAAE,CAAC,EACV,KAAMA,EAAE,CAAC,EAAE,YAAY,EACvB,KAAM,SACN,MAAOF,EACP,IAAKH,CACL,CAAC,EACYK,EAAE,CAAC,IAAhB,SACAJ,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAMA,EAAE,CAAC,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,EAC9DC,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAM,SAAU,MAAOF,EAAG,IAAKH,CAAC,CAAC,EACtE,QACD,CACAC,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,CACnD,SAAkBE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOE,EAAG,IAAKH,CAAC,CAAC,UAC7CE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,YAAa,MAAOE,EAAG,IAAKH,CAAC,CAAC,UAC/CE,IAAP,GAAU,CAClB,GAAIF,EAAI,EAAID,EAAE,OAAQ,CACrB,IAAMK,EAAIL,EAAE,GAAGC,EAAI,CAAC,EACnBQ,EAAIT,EAAE,GAAGC,EAAI,CAAC,EACd,EAAID,EAAE,GAAGC,EAAI,CAAC,EACf,GAAWI,IAAP,IAAYI,IAAM,IAAK,IAAM,GAAG,CACjCR,GAAK,EAAIC,EAAE,KAAK,CAAC,KAAM,MAAO,MAAOE,EAAG,IAAKH,CAAC,CAAC,EACjD,QACD,CACD,CACAC,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,CACnD,SAAkBE,IAAP,GAAU,CACpB,IAAME,EAAIS,GAAad,EAAGC,EAAI,CAAC,EAC/B,GAAaI,IAAT,KAAY,CACf,GAAM,CAACI,EAAGH,CAAC,EAAID,EACbJ,EAAIQ,EAAIP,EAAE,KAAK,CAAC,KAAM,aAAc,MAAOI,EAAE,YAAY,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,EAC/E,QACD,CACAC,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,CACnD,SAAkBE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,IAAK,MAAOE,EAAG,IAAKH,CAAC,CAAC,UACzCE,IAAP,GAAUD,EAAE,KAAK,CAAC,KAAM,IAAK,MAAOE,EAAG,IAAKH,CAAC,CAAC,UACtCE,IAAR,IAAWD,EAAE,KAAK,CAAC,KAAM,IAAK,MAAOE,EAAG,IAAKH,CAAC,CAAC,UACvCE,IAAR,IAAWD,EAAE,KAAK,CAAC,KAAM,IAAK,MAAOE,EAAG,IAAKH,CAAC,CAAC,UAC/CE,GAAK,IAAKA,GAAK,GAAG,CAC1B,IAAME,EAAIO,GAAeZ,EAAGC,CAAC,EAC5B,CAACQ,EAAGH,CAAC,EAAID,EACRJ,EAAIQ,EACWH,EAAE,CAAC,IAAnB,YACGJ,EAAE,KAAK,CACP,KAAM,YACN,MAAOI,EAAE,CAAC,EACV,KAAMA,EAAE,CAAC,EAAE,YAAY,EACvB,KAAM,SACN,MAAOF,EACP,IAAKH,CACL,CAAC,EACYK,EAAE,CAAC,IAAhB,SACAJ,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAMA,EAAE,CAAC,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,EAC9DC,EAAE,KAAK,CAAC,KAAMI,EAAE,CAAC,EAAG,MAAOA,EAAE,CAAC,EAAG,KAAM,SAAU,MAAOF,EAAG,IAAKH,CAAC,CAAC,CACvE,SAAkBE,IAAP,IAAaA,GAAK,IAAKA,GAAK,IAAQA,GAAK,IAAKA,GAAK,KAAMA,GAAK,KAAKA,IAAM,GAAG,CACtF,IAAME,EAAIQ,GAAiBb,EAAGC,CAAC,EAC/B,GAAaI,IAAT,KAAYH,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,MAC7D,CACJ,GAAM,CAACQ,EAAGH,EAAGC,CAAC,EAAIF,EAChBJ,EAAIQ,EAAIP,EAAE,KAAK,CAAC,KAAMK,EAAG,MAAOD,EAAG,MAAOF,EAAG,IAAKH,CAAC,CAAC,CACvD,CACD,MAAOC,EAAE,KAAK,CAAC,KAAM,QAAS,MAAOC,EAAG,MAAOC,EAAG,IAAKH,CAAC,CAAC,CAC1D,CACA,OAAOC,EAAE,KAAK,CAAC,KAAM,MAAO,MAAOD,EAAG,IAAKA,CAAC,CAAC,EAAGC,CACjD,EACaM,GAAgB,CAACC,EAAGH,IAAM,CACtC,GAAIG,EAAE,QAAUH,EAAI,EAAG,OAAO,KAC9B,IAAMC,EAAIE,EAAE,GAAGH,CAAC,EACfS,EAAI,CAAC,EACN,QAASC,EAAIV,EAAI,EAAGU,EAAIP,EAAE,OAAQO,GAAK,EAAG,CACzC,IAAMV,EAAIG,EAAE,GAAGO,CAAC,EAChB,GAAIV,IAAMC,EAAG,MAAO,CAACS,EAAG,OAAO,cAAc,GAAGD,CAAC,CAAC,EAClD,GAAIT,IAAM,GAAG,CACZ,IAAMD,EAAIY,GAAcR,EAAGO,CAAC,EAC5B,GAAaX,IAAT,KAAY,OAAO,KACvB,GAAM,CAACC,EAAGC,CAAC,EAAIF,EACfU,EAAE,KAAKR,CAAC,EAAIS,EAAIV,CACjB,KAAO,CACN,GAAIA,IAAM,GAAG,OAAO,KACpBS,EAAE,KAAKT,CAAC,CACT,CACD,CACA,OAAO,IACR,EACaI,GAAuB,CAACD,EAAGF,IAAM,CAC7C,IAAMQ,EAAIN,EAAE,GAAGF,CAAC,EAChB,GAAeQ,IAAX,OAAc,MAAO,GACzB,GAAIA,IAAM,GAAG,CACZ,IAAMA,EAAIN,EAAE,GAAGF,EAAI,CAAC,EACpB,OAAeQ,IAAX,OAAqB,GACrBA,IAAM,IAAYA,IAAP,IAAaA,GAAK,IAAKA,GAAK,IAAQA,GAAK,IAAKA,GAAK,KAAMA,GAAK,IAAU,GACnFA,IAAM,GACLN,EAAE,QAAUF,EAAI,EAAU,GACvBE,EAAE,GAAGF,EAAI,CAAC,IAAM,GAEjB,EACR,CACA,OAAWQ,IAAP,IAAaA,GAAK,IAAKA,GAAK,IAAQA,GAAK,IAAKA,GAAK,KAAMA,GAAK,IAAU,GACxEA,IAAM,GACLN,EAAE,QAAUF,EAAI,EAAU,GACvBE,EAAE,GAAGF,EAAI,CAAC,IAAM,GAEjB,EACR,EACaU,GAAgB,CAACX,EAAGY,IAAM,CAEtC,GADIZ,EAAE,QAAUY,EAAI,GAChBZ,EAAE,GAAGY,CAAC,IAAM,GAAG,OAAO,KAC1B,IAAMC,EAAIb,EAAE,GAAGY,EAAI,CAAC,EACpB,GAAIC,IAAM,GAAG,OAAO,KACpB,GAAKA,GAAK,IAAKA,GAAK,IAAOA,GAAK,IAAKA,GAAK,IAAQA,GAAK,IAAKA,GAAK,IAAM,CACtE,IAAM,EAAI,CAACA,CAAC,EACXnB,EAAI,KAAK,IAAIkB,EAAI,EAAGZ,EAAE,MAAM,EACzBL,EAAIiB,EAAI,EACZ,KAAOjB,EAAID,EAAGC,GAAK,EAAG,CACrB,IAAMI,EAAIC,EAAE,GAAGL,CAAC,EAChB,GAAI,EAAGI,GAAK,IAAKA,GAAK,IAAOA,GAAK,IAAKA,GAAK,IAAQA,GAAK,IAAKA,GAAK,KAAO,MAC1E,EAAE,KAAKA,CAAC,CACT,CACA,GAAIJ,EAAIK,EAAE,OAAQ,CACjB,IAAMC,EAAID,EAAE,GAAGL,CAAC,EACRM,IAAN,GAAWA,IAAM,IAAKA,IAAM,KAAON,GAAK,EAC3C,CACA,MAAO,CAACA,EAAI,EAAG,OAAO,SAAS,OAAO,cAAc,GAAG,CAAC,EAAG,EAAE,CAAC,CAC/D,CACA,MAAO,CAACiB,EAAI,EAAGC,CAAC,CACjB,EACaP,GAAiB,CAAC,EAAGH,IAAM,CACvC,IAAMH,EAAIc,GAAc,EAAGX,CAAC,EAC5B,GAAaH,IAAT,KAAY,OAAO,KACvB,GAAM,CAACC,EAAGQ,EAAGC,CAAC,EAAIV,EACjBe,EAAIP,GAAa,EAAGP,EAAI,CAAC,EAC1B,GAAac,IAAT,KAAY,CACf,GAAM,CAAChB,EAAGI,CAAC,EAAIY,EACf,MAAO,CAAChB,EAAG,CAAC,YAAaU,EAAGN,CAAC,CAAC,CAC/B,CACA,OAAOF,EAAI,EAAI,EAAE,QAAiB,EAAE,GAAGA,EAAI,CAAC,IAAjB,GAAqB,CAACA,EAAI,EAAG,CAAC,aAAcQ,CAAC,CAAC,EAAI,CAACR,EAAG,CAAC,SAAUQ,EAAGC,CAAC,CAAC,CAClG,EACaI,GAAgB,CAAC,EAAGX,IAAM,CACtC,IAAMO,EAAI,EAAE,GAAGP,CAAC,EAChB,GAAeO,IAAX,OAAc,OAAO,KACzB,IAAI,EAAI,UACFM,EAAI,CAAC,EACX,IAAaN,IAAP,IAAYA,IAAM,KAAQP,GAAK,EAAIO,IAAM,IAAKM,EAAE,KAAK,EAAC,GAAIb,EAAI,EAAE,QAAU,CAC/E,IAAMH,EAAI,EAAE,GAAGG,CAAC,EAChB,GAAI,EAAEH,GAAK,IAAKA,GAAK,IAAI,MACzBgB,EAAE,KAAKhB,CAAC,EAAIG,GAAK,CAClB,CACA,GAAIA,EAAI,EAAI,EAAE,OAAQ,CACrB,IAAMH,EAAI,EAAE,GAAGG,CAAC,EACfO,EAAI,EAAE,GAAGP,EAAI,CAAC,EACf,GAAWH,IAAP,IAAYU,GAAK,IAAKA,GAAK,GAC9B,IAAKM,EAAE,KAAKhB,EAAGU,CAAC,EAAG,EAAI,SAAUP,GAAK,EAAGA,EAAI,EAAE,QAAU,CACxD,IAAMH,EAAI,EAAE,GAAGG,CAAC,EAChB,GAAI,EAAEH,GAAK,IAAKA,GAAK,IAAI,MACzBgB,EAAE,KAAKhB,CAAC,EAAIG,GAAK,CAClB,CACF,CACA,GAAIA,EAAI,EAAI,EAAE,OAAQ,CACrB,IAAMO,EAAI,EAAE,GAAGP,CAAC,EACfS,EAAI,EAAE,GAAGT,EAAI,CAAC,EACdU,EAAI,EAAE,GAAGV,EAAI,CAAC,EACf,GAAWO,IAAP,IAAoBA,IAAR,IAAW,CAC1B,IAAIA,EAAI,GACR,GACEE,GAAK,IAAKA,GAAK,IACZI,EAAE,KAAK,GAAIJ,CAAC,EAAIT,GAAK,EAAKO,EAAI,KAC9BE,IAAM,IAAYA,IAAP,KACDC,IAAX,QACAA,GAAK,IACLA,GAAK,KACJG,EAAE,KAAK,EAAE,EAAGJ,IAAM,IAAKI,EAAE,KAAK,EAAC,EAAGA,EAAE,KAAKH,CAAC,EAAIV,GAAK,EAAKO,EAAI,IAChEA,EAEA,IAAK,EAAI,SAAUP,EAAI,EAAE,QAAU,CAClC,IAAMH,EAAI,EAAE,GAAGG,CAAC,EAChB,GAAI,EAAEH,GAAK,IAAKA,GAAK,IAAI,MACzBgB,EAAE,KAAKhB,CAAC,EAAIG,GAAK,CAClB,CACF,CACD,CACA,IAAM,EAAI,OAAO,cAAc,GAAGa,CAAC,EAC/B,EAAiB,IAAb,SAAiB,OAAO,WAAW,CAAC,EAAI,OAAO,SAAS,CAAC,EACjE,OAAa,IAAN,IAAY,EAAI,GAAI,OAAO,MAAM,CAAC,EAAI,KAAO,CAACb,EAAI,EAAG,EAAG,CAAC,CACjE,EACaE,GAAqB,CAAC,EAAGF,IAAM,CAC3C,GAAI,EAAE,QAAUA,EAAG,OAAO,KAC1B,IAAMY,EAAI,CAAC,EACX,QAASrB,EAAI,EAAE,GAAGS,CAAC,EAAGA,EAAI,EAAE,OAAQT,EAAI,EAAE,GAAG,EAAES,CAAC,EAAG,CAClD,GACC,EACCT,IAAM,IACCA,IAAP,IACCA,GAAK,IAAKA,GAAK,IACfA,GAAK,IAAKA,GAAK,KAChBA,GAAK,KACJA,GAAK,IAAKA,GAAK,IAEhB,CACD,CACC,IAAM,EAAIiB,GAAc,EAAGR,CAAC,EAC5B,GAAa,IAAT,KAAY,CACf,GAAM,CAACJ,EAAGE,CAAC,EAAI,EACfc,EAAE,KAAKd,CAAC,EAAIE,EAAIJ,EAChB,QACD,CACD,CACA,KACD,CACAgB,EAAE,KAAKrB,CAAC,CACT,CACA,OAAaS,IAAN,EAAU,KAAO,CAACA,EAAI,EAAG,OAAO,cAAc,GAAGY,CAAC,CAAC,CAC3D,EACaP,GAAe,CAAC,EAAGL,IAAOC,GAAqB,EAAGD,CAAC,EAAIE,GAAmB,EAAGF,CAAC,EAAI,KAClFc,GAAa,CAACjB,EAAGC,IAAM,CACnC,IAAIQ,EAAIT,EAAE,GAAGC,CAAC,EACd,KAAaQ,IAAN,GAAWA,IAAM,IAAKA,IAAM,IAAKA,EAAIT,EAAE,GAAG,EAAEC,CAAC,EACpD,IAAMS,EAAI,CAAC,EACPM,EAAI,GACR,KAAOf,EAAID,EAAE,QAAU,CACtB,GAAWS,IAAP,GAAU,MAAO,CAACR,EAAG,OAAO,cAAc,GAAGS,CAAC,CAAC,EACnD,GAAWD,IAAP,IAAmBA,IAAP,IAAmBA,IAAP,GAAU,OAAO,KAC7C,GAAUA,IAAN,GAAWA,IAAM,IAAKA,IAAM,GAAG,CAACO,GAAKN,EAAE,OAAS,IAAMM,EAAI,YACrDP,IAAM,GAAG,CACjB,IAAMV,EAAIY,GAAcX,EAAGC,CAAC,EAC5B,GAAaF,IAAT,MAAciB,EAAG,OAAO,KAC5B,GAAM,CAACb,EAAGM,CAAC,EAAIV,EACfW,EAAE,KAAKD,CAAC,EAAIR,EAAIE,CACjB,KAAO,CACN,GAAIa,EAAG,OAAO,KACdN,EAAE,KAAKD,CAAC,CACT,CACAA,EAAIT,EAAE,GAAG,EAAEC,CAAC,CACb,CACA,OAAO,IACR,EACaM,GAAmB,CAACP,EAAGC,IAAM,CACzC,IAAMQ,EAAID,GAAaR,EAAGC,CAAC,EAC3B,GAAaQ,IAAT,KAAY,OAAO,KACvB,GAAM,CAACC,EAAGK,CAAC,EAAIN,EACf,GAAcM,EAAE,YAAY,IAAxB,OACH,GAAIf,EAAE,OAASU,EAAI,GACPV,EAAE,GAAGU,EAAI,CAAC,IAAjB,GAAoB,CACvB,QAAST,EAAI,EAAGS,EAAIT,EAAID,EAAE,OAAQC,GAAK,EAAG,CACzC,IAAMQ,EAAIT,EAAE,GAAGU,EAAIT,CAAC,EACpB,GAAWQ,IAAP,IAAmBA,IAAP,GAAU,MAAO,CAACC,EAAI,EAAGK,EAAE,YAAY,EAAG,UAAU,EACpE,GAAUN,IAAN,GAAWA,IAAM,IAAKA,IAAM,GAAG,CAClC,IAAMV,EAAIkB,GAAWjB,EAAGU,EAAIT,CAAC,EAC7B,GAAaF,IAAT,KAAY,OAAO,KACvB,GAAM,CAACI,EAAGM,CAAC,EAAIV,EACf,MAAO,CAACI,EAAGM,EAAG,KAAK,CACpB,CACD,CACA,MAAO,CAACC,EAAI,EAAGK,EAAE,YAAY,EAAG,UAAU,CAC3C,UAESf,EAAE,OAASU,EAAI,GACdV,EAAE,GAAGU,EAAI,CAAC,IAAjB,GAAoB,MAAO,CAACA,EAAI,EAAGK,EAAE,YAAY,EAAG,UAAU,EAEnE,MAAO,CAACL,EAAGK,EAAE,YAAY,EAAG,OAAO,CACpC,EC3XO,IAAMG,GAAQC,GAAK,CACzB,IAAMC,EAAIC,GAAEC,GAAEH,CAAC,CAAC,EAChB,OAAOI,EAAEH,CAAC,EAAIA,EAAII,GAAEJ,CAAC,CACtB,ECPO,IAAMK,GAAgBC,GAAiB,OAAOA,GAAnB,UAAiCA,IAAT,MAAc,UAAWA,ECsB5E,IAAMC,GAAsBC,GAAK,CACvC,IAAMC,EAAIC,GAAEF,CAAC,EACb,OAAOG,GAAEF,CAAC,EAAIG,GAAEH,CAAC,EAAII,EAAEC,GAAEC,GAAEN,CAAC,CAAC,CAAC,CAC/B,ECzBO,IAAMO,GAAoB,CAChC,YAAa,CAAC,KAAM,EAAG,MAAO,CAAC,EAC/B,cAAe,CAAC,KAAM,EAAG,OAAQ,EAAG,KAAM,CAAC,EAC3C,cAAe,CAAC,KAAM,EAAG,GAAI,EAAG,QAAS,CAAC,EAC1C,KAAM,CAAC,EAAG,EAAG,EAAG,CAAC,EACjB,MAAO,CAAC,KAAM,EAAG,MAAO,CAAC,EACzB,iBAAkB,CAAC,KAAM,EAAG,OAAQ,EAAG,MAAO,CAAC,EAC/C,kBAAmB,CAAC,KAAM,EAAG,OAAQ,CAAC,EACtC,QAAS,CAAC,KAAM,EAAG,OAAQ,EAAG,KAAM,CAAC,EACrC,KAAM,CAAC,UAAW,EAAG,YAAa,CAAC,EACnC,OAAQ,CAAC,KAAM,EAAG,KAAM,EAAG,KAAM,CAAC,EAClC,eAAgB,CAAC,WAAY,EAAG,WAAY,EAAG,aAAc,EAAG,QAAS,CAAC,EAC1E,gBAAiB,CAAC,SAAU,EAAG,KAAM,CAAC,EACtC,uBAAwB,CAAC,OAAQ,EAAG,SAAU,EAAG,YAAa,CAAC,EAC/D,gBAAiB,CAAC,KAAM,EAAG,OAAQ,CAAC,EACpC,kBAAmB,CAAC,KAAM,EAAG,SAAU,CAAC,EACxC,eAAgB,CAAC,KAAM,EAAG,KAAM,CAAC,EACjC,uBAAwB,CAAC,MAAO,EAAG,KAAM,CAAC,EAC1C,mBAAoB,CAAC,gBAAiB,EAAG,KAAM,EAAG,KAAM,EAAG,OAAQ,CAAC,EACpE,uBAAwB,CAAC,gBAAiB,EAAG,OAAQ,CAAC,EACtD,yBAA0B,CAAC,gBAAiB,EAAG,OAAQ,CAAC,EACxD,+BAAgC,CAAC,gBAAiB,EAAG,OAAQ,CAAC,EAC9D,UAAW,CAAC,KAAM,EAAG,eAAgB,EAAG,QAAS,CAAC,EAClD,oBAAqB,CAAC,KAAM,EAAG,GAAI,EAAG,QAAS,CAAC,EAChD,sBAAuB,CAAC,SAAU,EAAG,KAAM,CAAC,CAC7C,EACaC,EAAwB,CACpC,MAAO,CAAC,QAAS,QAAS,KAAM,UAAW,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EACrE,cAAe,CAAC,QAAS,cAAe,KAAM,UAAW,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EACnF,WAAY,CAAC,QAAS,aAAc,KAAM,UAAW,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EAC/E,gBAAiB,CAAC,QAAS,gBAAiB,KAAM,SAAU,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EACtF,eAAgB,CAAC,QAAS,eAAgB,KAAM,SAAU,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EACpF,OAAQ,CAAC,QAAS,SAAU,KAAM,SAAU,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EACtE,MAAO,CAAC,QAAS,QAAS,KAAM,SAAU,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EACpE,WAAY,CAAC,QAAS,aAAc,KAAM,aAAc,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAAC,EAClF,+BAAgC,CAC/B,QAAS,+BACT,KAAM,UACN,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAC1B,EACA,6BAA8B,CAC7B,QAAS,6BACT,KAAM,UACN,OAAQ,CAAC,GAAI,EAAG,IAAO,EAAE,CAC1B,CACD,EACaC,GAAuB,CACnC,eAAgB,CAAC,QAAS,eAAgB,KAAM,QAAS,OAAQ,CAAC,GAAI,CAAC,EAAG,CAAC,EAAG,CAAC,IAAO,CAAC,EAAG,EAAE,CAAC,EAC7F,sBAAuB,CACtB,QAAS,sBACT,KAAM,QACN,OAAQ,CAAC,GAAI,CAAC,EAAG,CAAC,EAAG,CAAC,IAAO,CAAC,EAAG,EAAE,CACpC,CACD,EACaC,EAAuB,GAAK,OAAO,QAAQ,CAAC,EAAE,OAAOC,GAAgBA,EAAE,CAAC,IAAd,MAAe,EAChFA,GAAI,IAAI,IAAI,OAAO,KAAKJ,EAAiB,CAAC,EACnCK,GAAiBC,GAAKF,GAAE,IAAIE,EAAE,CAAC,CAAC,EAChCC,GAAgBD,GAAKF,GAAE,IAAIE,CAAC,EACnCA,GAAI,IAAI,IAAI,OAAO,KAAKJ,EAAoB,CAAC,EACtCM,EAAmB,GAAKF,GAAE,IAAI,EAAE,CAAC,CAAC,EAClCG,EAAkB,GAAKH,GAAE,IAAI,CAAC,EACrCI,GAAI,IAAI,IAAI,OAAO,KAAKT,CAAqB,CAAC,EACvCU,GAAoB,GAAKD,GAAE,IAAI,EAAE,CAAC,CAAC,EACnCE,GAAmB,GAAKF,GAAE,IAAI,CAAC,EAC/BG,GAAc,GAAKF,GAAkB,CAAC,GAAKH,EAAiB,CAAC,EAC7DM,GAAa,GAAKF,GAAiB,CAAC,GAAKH,EAAgB,CAAC,EA0BhE,IAAMM,GAAe,GAAKC,GAAiB,CAAC,GAAKC,EAAgB,CAAC,GAAKC,GAAc,CAAC,EAChFC,EAAa,CAAC,EAAGC,IAAM,CACnC,EAAEA,EAAE,CAAC,CAAC,EAAIA,EAAE,CAAC,CACd,EACaC,GAAY,IAAI,IAC5B,EAAE,OACD,CAACC,EAAG,IACUA,IAAb,SACG,EACa,IAAb,SACAA,EACcA,IAAd,WAAiC,IAAd,UACnB,WACC,CAACA,EAAGF,IAAM,CACX,GAAM,CAACG,EAAGC,EAAGC,EAAGC,CAAC,EAAIJ,EACpBK,EAAgB,OAAOH,GAAnB,SAAuBA,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACzCI,EAAgB,OAAOH,GAAnB,SAAuBA,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACzC,CAAC,EAAGI,EAAGC,EAAGC,CAAC,EAAIX,EACfY,EAAgB,OAAOH,GAAnB,SAAuBA,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACzCI,EAAgB,OAAOH,GAAnB,SAAuBA,EAAIA,EAAE,CAAC,EAAIA,EAAE,CAAC,EACtCI,EAAIX,IAAM,GAAK,CAACA,EACpBI,IAAMK,IAAME,EAAIP,EAAIK,GACpB,IAAIG,EAAIT,IAAMK,GAAK,CAACL,EACpBE,IAAMK,IAAME,EAAIP,EAAIK,GACpB,IAAMG,EAAIF,EAAIX,EAAI,EACjBc,EAAIH,EAAIV,EAAIK,EACZS,EAAIH,EAAIV,EAAIK,EACZS,EAAIJ,EAAIT,EAAIK,EACb,OAAOM,EAAIC,GAAMD,IAAMC,IAAM,CAACF,GAAK,CAACG,GAAM,UAAY,CAACH,EAAGC,EAAGC,EAAGC,CAAC,CACjE,GAAGjB,EAAG,CAAC,EACX,QACD,EACYkB,GAAa,GAAK,CAC9B,GAAIC,EAAiB,CAAC,EAAG,CACxB,GAAM,CAAC,OAAQrB,CAAC,EAAIsB,GAAqB,EAAE,CAAC,CAAC,EAC5CnB,EAAIF,GAAU,EAAE,CAAC,EAAGD,CAAC,EACtB,GAAgB,OAAOG,GAAnB,SAAsB,OAAOA,EACjC,GACCA,EAAE,CAAC,IAAMH,EAAE,CAAC,GACZG,EAAE,CAAC,EAAE,CAAC,IAAMH,EAAE,CAAC,EAAE,CAAC,GAClBG,EAAE,CAAC,EAAE,CAAC,IAAMH,EAAE,CAAC,EAAE,CAAC,GAClBG,EAAE,CAAC,EAAE,CAAC,IAAMH,EAAE,CAAC,EAAE,CAAC,GAClBG,EAAE,CAAC,EAAE,CAAC,IAAMH,EAAE,CAAC,EAAE,CAAC,GAClBG,EAAE,CAAC,IAAMH,EAAE,CAAC,EAEZ,MAAO,SACR,CACC,IAAME,EAAIC,EAAE,CAAC,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAE,CAAC,EACzBH,EAAIG,EAAE,CAAC,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAE,CAAC,EACrB,OAAOD,EAAIF,GAAME,IAAMF,IAAM,CAACG,EAAE,CAAC,GAAK,CAACA,EAAE,CAAC,GAAM,UAAYA,CAC7D,CACD,CACA,CACC,GAAM,CAAC,OAAQH,CAAC,EAAIuB,EAAsB,EAAE,CAAC,CAAC,EAC7CpB,EAAIF,GAAU,EAAE,CAAC,EAAGD,CAAC,EACtB,OAAmB,OAAOG,GAAnB,SACJA,EACAA,EAAE,CAAC,IAAMH,EAAE,CAAC,GAAKG,EAAE,CAAC,IAAMH,EAAE,CAAC,GAAKG,EAAE,CAAC,IAAMH,EAAE,CAAC,GAAKG,EAAE,CAAC,IAAMH,EAAE,CAAC,EAC/D,SACAG,EAAE,CAAC,EAAIA,EAAE,CAAC,GAAMA,EAAE,CAAC,IAAMA,EAAE,CAAC,IAAM,CAACA,EAAE,CAAC,GAAK,CAACA,EAAE,CAAC,GAC/C,UACAA,CACJ,CACD,EAqBO,IAAMqB,GAAgB,GAAK,CACjC,GAAgB,OAAO,EAAE,CAAC,GAAtB,SAAyB,MAAM,IAAI,MAAM,gBAAgB,EAC7D,GAAM,CAAC,OAAQC,CAAC,EAAIC,GAAqB,EAAE,CAAC,CAAC,EAC5C,CAACC,EAAGC,EAAGC,EAAGC,CAAC,EAAI,EAAE,CAAC,EAClB,EAAIF,EAAE,CAAC,EAAIA,EAAE,CAAC,EACdG,EAAIF,EAAE,CAAC,EAAIA,EAAE,CAAC,EACdG,EAAIP,EAAE,CAAC,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAE,CAAC,EACpB,EAAIA,EAAE,CAAC,EACP,EAAIA,EAAE,CAAC,EAAE,CAAC,EAAIA,EAAE,CAAC,EAAE,CAAC,EACpBQ,EAAIR,EAAE,CAAC,EACPS,EAAIH,EAAI,GAAMA,IAAM,GAAK,EAAEE,GAAK,CAACH,GAClC,OAAO,EAAIE,GAAM,IAAMA,GAAK,EAAE,GAAK,CAACL,GACjCO,EACC,UACA,CAAC,CAAC,CAACJ,EAAGD,EAAGJ,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,EACrBS,EACA,CAAC,CAACT,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGG,EAAG,CAACD,CAAC,CAAC,EACpB,CACA,CAACF,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGG,EAAG,CAACD,CAAC,EAClB,CAAC,CAACG,EAAGD,EAAGJ,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAClB,CACJ,EACO,SAASU,GAAe,EAAG,CACjC,GAAgB,OAAO,EAAE,CAAC,GAAtB,SAAyB,MAAM,IAAI,MAAM,gBAAgB,EAC7D,GAAM,CAAC,OAAQV,CAAC,EAAIW,EAAsB,EAAE,CAAC,CAAC,EAC7C,CAACT,EAAGC,EAAGC,EAAGC,CAAC,EAAI,EAAE,CAAC,EAClB,EAAIL,EAAE,CAAC,EACPM,EAAIN,EAAE,CAAC,EACPO,EAAIP,EAAE,CAAC,EACP,EAAIA,EAAE,CAAC,EACP,EAAII,EAAIG,GAAMH,IAAMG,GAAK,EAAE,GAAK,CAACF,GAClC,OAAOF,EAAI,GAAMA,IAAM,GAAK,EAAEG,GAAK,CAACJ,GACjC,EACC,UACA,CAAC,CAAC,CAACG,EAAGD,EAAGJ,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,EACrB,EACA,CAAC,CAACA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGG,EAAG,CAACD,CAAC,CAAC,EACpB,CACA,CAACF,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGG,EAAG,CAACD,CAAC,EAClB,CAAC,CAACG,EAAGD,EAAGJ,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAClB,CACJ,CCvNA,IAAMY,GAAI,CACT,QAAS,KACT,SAAU,KACV,YAAa,gBACb,KAAM,GACN,KAAM,GACN,KAAM,EACN,KAAM,EACN,MAAO,GACP,KAAM,EACP,EACaC,EAAgB,CAAC,EAAGC,IAAM,CACtC,GAAiB,EAAE,OAAf,SAAqB,MAAO,CAAC,KAAM,SAAU,MAAO,EAAE,KAAK,EAC/D,GAAoB,EAAE,OAAlB,YAAwB,CAC3B,IAAIC,EACJ,OAAQ,EAAE,KAAM,CACf,IAAK,IACL,IAAK,KACJA,EAAI,OACJ,MACD,IAAK,KACL,IAAK,MACJA,EAAI,YACJ,MACD,IAAK,MACL,IAAK,OACL,IAAK,OACL,IAAK,IACJA,EAAI,aACJ,MACD,QACCA,EAAI,QACN,CACA,GAAa,EAAE,OAAX,KAAiB,MAAO,CAAC,KAAM,YAAa,QAAS,SAAU,GAAI,EAAE,KAAK,EAC9E,GAAeA,IAAX,OACH,MAAO,CACN,KAAM,YACN,QAAS,OACT,GAAY,EAAE,OAAV,IAAiB,KAAK,MAAM,IAAM,EAAE,KAAK,EAAI,EAAE,KACpD,EACD,GAAoBA,IAAhB,YACH,MAAO,CACN,KAAM,YACN,QAAS,YACT,GAAc,EAAE,OAAZ,MAAmB,KAAK,MAAM,IAAM,EAAE,KAAK,EAAI,EAAE,KACtD,EACD,GAAqBA,IAAjB,aAAoB,CACvB,IAAID,EAAI,EAAE,MACV,OACW,EAAE,OAAZ,MACIA,EAAI,OAAO,YAAY,YAAe,EAAE,OAAO,QAAQ,CAAC,CAAC,EAC/C,EAAE,OAAb,SAAsBA,EAAI,OAAO,YAAY,YAAe,EAAE,OAAO,QAAQ,CAAC,CAAC,GAClF,CAAC,KAAM,YAAa,QAAS,aAAc,KAAMA,CAAC,CAEpD,CACA,GAAI,EAAE,QAAQA,EAAG,CAChB,IAAMC,EAAID,EAAE,EAAE,IAAI,EAClB,MAAO,CAAC,KAAM,YAAa,QAAS,SAAU,GAAI,OAAO,YAAY,EAAE,MAAQC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAC9F,CACA,MAAO,CAAC,KAAM,QAAS,MAAO,SAAS,CACxC,CACA,OAAmB,EAAE,OAAd,QACW,EAAE,QAAjB,WACC,CAAC,KAAM,UAAU,EACjB,CAAC,KAAM,QAAS,MAAO,EAAE,KAAK,EAC/B,CAAC,KAAM,QAAS,UAAW,EAAE,UAAW,YAAa,EAAE,WAAW,CACtE,EACaC,GAA+B,GAAK,CAChD,IAAIF,EAAI,CAAC,EACG,OAAO,EAAE,MAArB,WACEA,EAAI,CACJ,KAAM,KAAK,MAAM,GAAM,EAAE,IAAI,EAC7B,KAAM,KAAK,MAAM,GAAM,EAAE,IAAI,EAC7B,MAAO,KAAK,MAAM,GAAM,EAAE,IAAI,EAC9B,KAAM,KAAK,MAAM,EAAE,IAAI,CACxB,GACD,IAAMC,EAAI,CAAC,GAAGH,GAAG,GAAGE,EAAG,GAAG,CAAC,EAC1B,CACC,QAASG,EACT,SAAUC,EACV,YAAaC,EACb,KAAMC,EACN,KAAMC,EACN,KAAM,EACN,KAAMC,EACN,MAAOC,EACP,KAAMC,CACP,EAAIT,EACJU,EAAIR,EAAI,IACRS,EAAIR,EAAI,IACT,MAAO,CACN,GAAIE,EACJ,IAAKA,EACL,GAAIC,EACJ,IAAKA,EACL,GAAI,EACJ,GAAIC,EACJ,IAAKC,EACL,GAAIC,EACJ,GAAIC,EACJ,GAAIC,EACJ,KAAM,KAAK,IAAIA,EAAGD,CAAC,EACnB,KAAM,KAAK,IAAIC,EAAGD,CAAC,EACnB,GAAwBN,IAApB,gBAAwBM,EAAIC,EAChC,GAAwBP,IAApB,gBAAwBO,EAAID,EAChC,GAAI,YACJ,GAAI,aACJ,GAAI,GACJ,EAAG,cACH,GAAI,GACJ,GAAI,EACL,CACD,EACMR,GAAI,CAAC,IAAK,IAAK,KAAM,KAAM,IAAK,IAAK,KAAM,IAAI,EACxCU,GAAuB,CAACZ,EAAGH,IAAM,CAC7C,GAAgBG,EAAE,UAAd,SACH,GAAIa,GAAEb,EAAE,OAAO,EAAG,CACjB,GAAM,CAAC,MAAOc,EAAG,QAASf,CAAC,EAAIC,EAC/B,OAAkBc,EAAE,YAAb,QAAqCA,EAAE,aAAb,OACtBA,EAAE,SAAV,KAA6BA,EAAE,SAAX,KACnB,CACA,KAAM,SACN,KAAMf,EACN,MAAOe,EAAE,OACT,IAAKhB,EAAcgB,EAAE,UAAWjB,CAAC,EACjC,MAAOiB,EAAE,QACT,IAAKhB,EAAcgB,EAAE,WAAYjB,CAAC,CAClC,EACA,CACA,KAAM,SACN,KAAME,EACN,MAAee,EAAE,UAAV,IAAoB,IAAM,KACjC,IAAKhB,EAAcgB,EAAE,WAAYjB,CAAC,EAClC,MAAOiB,EAAE,OAAS,IAAM,KACxB,IAAKhB,EAAcgB,EAAE,UAAWjB,CAAC,CACjC,EACUiB,EAAE,aAAb,OACQA,EAAE,SAAV,IACC,CAAC,KAAM,SAAU,KAAMf,EAAG,MAAOD,EAAcgB,EAAE,UAAWjB,CAAC,CAAC,EAC9D,CAAC,KAAM,SAAU,KAAME,EAAG,GAAIG,GAAEY,EAAE,MAAM,EAAG,MAAOhB,EAAcgB,EAAE,UAAWjB,CAAC,CAAC,EACxEiB,EAAE,UAAV,IACA,CAAC,KAAM,SAAU,KAAMf,EAAG,MAAOD,EAAcgB,EAAE,WAAYjB,CAAC,CAAC,EAC/D,CAAC,KAAM,SAAU,KAAME,EAAG,GAAIe,EAAE,QAAS,MAAOhB,EAAcgB,EAAE,WAAYjB,CAAC,CAAC,CAClF,UACsBG,EAAE,UAAd,SACV,GAAsBA,EAAE,UAApB,eACH,GAAeA,EAAE,SAAb,QAAmCA,EAAE,MAAM,OAApB,QAA0B,CACpD,GAAmBA,EAAE,MAAM,QAAvB,WACH,MAAO,CACN,KAAM,SACN,KAAM,eACN,GAAI,KACJ,MAAO,CAAC,KAAM,QAAS,UAAW,EAAG,YAAa,CAAC,CACpD,EACD,GAAoBA,EAAE,MAAM,QAAxB,YACH,MAAO,CACN,KAAM,SACN,KAAM,eACN,GAAI,KACJ,MAAO,CAAC,KAAM,QAAS,UAAW,EAAG,YAAa,CAAC,CACpD,CACF,UACUe,GAAEf,EAAE,OAAO,EAAG,CACxB,GAAeA,EAAE,SAAb,OACH,MAAO,CAAC,KAAM,SAAU,KAAMA,EAAE,QAAS,MAAOF,EAAcE,EAAE,MAAOH,CAAC,CAAC,EAC1E,GAAIgB,GAAEb,EAAE,OAAO,EACd,OAAiBA,EAAE,SAAZ,MACJ,CAAC,KAAM,SAAU,KAAMA,EAAE,QAAS,GAAI,KAAM,MAAOF,EAAcE,EAAE,MAAOH,CAAC,CAAC,EAC5E,CAAC,KAAM,SAAU,KAAMG,EAAE,QAAS,GAAI,KAAM,MAAOF,EAAcE,EAAE,MAAOH,CAAC,CAAC,CACjF,MACM,CACN,GAAsBG,EAAE,UAApB,cACH,MAAO,CACN,KAAM,SACN,KAAM,eACN,IAAK,CAAC,KAAM,QAAS,UAAW,EAAG,YAAa,CAAC,EACjD,MAAO,IACP,MAAO,IACP,IAAK,CAAC,KAAM,QAAS,UAAW,OAAO,kBAAmB,YAAa,CAAC,CACzE,EACD,GAAIe,GAAEf,EAAE,OAAO,EAAG,MAAO,CAAC,KAAM,UAAW,KAAMA,EAAE,OAAO,CAC3D,CACA,MAAO,CAAC,KAAM,UAAW,KAAMA,EAAE,OAAO,CACzC,EACagB,EAAW,GACV,EAAE,OAAf,UAAuB,EAAE,MAAQ,EAC9B,CAAC,EAAE,MAAO,CAAC,EACC,EAAE,OAAd,QACA,CAAC,EAAE,UAAW,EAAE,WAAW,EAC3B,KACSC,EAAW,CAAC,EAAGlB,IAAM,CACjC,IAAMF,EAAIqB,EAAEnB,CAAC,EACb,GAAmB,EAAE,OAAjB,YACH,GAAqBA,IAAjB,aAAoB,OAAO,OAAO,0BACdF,EAAE,OAAhB,WACV,GAAiB,EAAE,OAAf,UAAuB,OAAO,UAAU,EAAE,KAAK,EAAG,OAAO,EAAE,cACpCA,EAAE,OAAnB,cACV,GAAoB,EAAE,OAAlB,aAA2C,EAAE,UAAnB,aAA4B,OAAO,EAAE,aAC5CA,EAAE,OAAf,SAAqB,CAC/B,GAAoB,EAAE,OAAlB,aAAuC,EAAE,UAAf,SAAwB,OAAO,EAAE,GAC/D,GAAiB,EAAE,OAAf,UAA6B,EAAE,QAAR,EAAe,MAAO,EAClD,CACA,OAAO,IACR,ECtLO,IAAMsB,GAAW,CAAC,EAAG,IAAM,CACjC,IAAM,EAAI,CAAC,EACX,QAAW,KAAK,EACf,QAAWC,KAAK,EAAG,CAClB,IAAMC,EAAIC,GAAW,EAAGF,CAAC,EACzB,OAAO,KAAKC,CAAC,EAAE,OAAS,GAAK,EAAE,KAAKA,CAAC,CACtC,CACD,OAAO,CACR,EACaC,GAAa,CAAC,EAAG,IAAM,CACnC,IAAMC,EAAI,CAAC,EACX,QAAWF,KAAKG,EAAE,CAAC,EAAcH,EAAE,CAAC,IAAd,QAAmBI,EAAEF,EAAGF,CAAC,EAC/C,QAAWD,KAAKI,EAAE,CAAC,EAClB,GAAIJ,EAAE,CAAC,IAAKG,GACX,GAAeA,EAAEH,EAAE,CAAC,CAAC,IAAjB,OAAoB,CACvB,IAAMC,EAAIE,EACV,GAAqBH,EAAE,CAAC,IAApB,cACC,GAA2BA,EAAE,CAAC,IAA1B,mBAA6BC,EAAED,EAAE,CAAC,CAAC,EAAE,KAAK,GAAGA,EAAE,CAAC,CAAC,UACnCC,EAAED,EAAE,CAAC,CAAC,IAApB,WAAuCA,EAAE,CAAC,IAAjB,UAAoBC,EAAED,EAAE,CAAC,CAAC,EAAI,kBAC1CC,EAAED,EAAE,CAAC,CAAC,IAAnB,SAAsBK,EAAEJ,EAAGD,CAAC,UACfA,EAAE,CAAC,IAAhB,SACJ,CACJ,IAAMC,EAAIE,EACVG,GAAEN,CAAC,GAAKO,EAAEP,CAAC,EACRK,EAAEJ,EAAG,CAACD,EAAE,CAAC,EAAGQ,GAAEP,EAAED,EAAE,CAAC,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,EACXA,EAAE,CAAC,IAArB,eAAkDA,EAAE,CAAC,IAA3B,oBACzBC,EAAED,EAAE,CAAC,CAAC,EAAI,CACXC,EAAED,EAAE,CAAC,CAAC,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAE,CAAC,EACpBC,EAAED,EAAE,CAAC,CAAC,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAE,CAAC,EACpBC,EAAED,EAAE,CAAC,CAAC,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAE,CAAC,EACpBC,EAAED,EAAE,CAAC,CAAC,EAAE,CAAC,GAAKA,EAAE,CAAC,EAAE,CAAC,CACpB,EACAK,EAAEJ,EAAG,CAACD,EAAE,CAAC,EAAGC,EAAED,EAAE,CAAC,CAAC,IAAMA,EAAE,CAAC,EAAIC,EAAED,EAAE,CAAC,CAAC,EAAI,SAAS,CAAC,CACvD,EACD,OACMK,EAAEF,EAAGH,CAAC,EACd,OAAOG,CACR,EACaM,GAAW,GAAK,EAAE,IAAIT,GAAKU,GAAWV,CAAC,CAAC,EAAE,OAAO,CAACA,EAAGC,IAAMF,GAASC,EAAGC,CAAC,CAAC,EACzES,GAAa,GAAK,CAC9B,IAAM,EAAIN,EAAE,CAAC,EACZO,EAAI,CAAC,EACN,QAAWX,KAAK,EACf,GAAeA,EAAE,CAAC,IAAd,OAAiB,CACpB,IAAIC,EAAGW,EACP,GAA2BZ,EAAE,CAAC,IAA1B,mBAA6B,MAAO,CAAC,CAAC,CAACA,EAAE,CAAC,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,EACvD,GAAqBA,EAAE,CAAC,IAApB,aAAuB,SAC3B,GAAMC,EAAID,EAAkBA,EAAE,CAAC,IAAjB,UAAqBY,EAAI,CAAC,CAACZ,EAAE,CAAC,EAAG,QAAQ,CAAC,UAClCA,EAAE,CAAC,IAAhB,SAAmBY,EAAI,CAAC,CAACZ,EAAE,CAAC,EAAG,SAAS,CAAC,UACzCa,GAAEb,CAAC,EACX,GAAsBA,EAAE,CAAC,IAArB,cAAwB,CAC3B,IAAMC,EAAID,EAAE,CAAC,EACbY,EAAI,CAAC,CAAC,cAAe,CAAC,CAACX,EAAE,CAAC,EAAG,CAACA,EAAE,CAAC,EAAG,CAACA,EAAE,CAAC,EAAG,CAACA,EAAE,CAAC,CAAC,CAAC,CAAC,CACnD,MACCW,EACYZ,EAAE,CAAC,IAAd,OACG,CAAC,CAAC,OAAcA,EAAE,CAAC,IAAT,EAAa,EAAI,CAAC,CAAC,EAC7B,OAAO,KAAKc,GAAEd,EAAE,CAAC,CAAC,CAAC,EAClB,OAAOC,GAAKA,IAAMD,EAAE,CAAC,CAAC,EACtB,IAAIC,GAAK,CAACD,EAAE,CAAC,EAAGC,CAAC,CAAC,UAChBM,EAAEP,CAAC,EAAG,CACd,IAAMC,EAAIc,GAAEf,CAAC,EACbY,GAAmBX,IAAd,UAAkB,CAAC,SAAS,EAAIA,GAAG,IAAIA,GAAK,CAACD,EAAE,CAAC,EAAGC,CAAC,CAAC,CAC3D,KAAO,CACN,IAAMA,EAAIe,GAAEhB,CAAC,EACbY,GAAmBX,IAAd,UAAkB,CAAC,SAAS,EAAIA,GAAG,IAAIA,GAAK,CAACD,EAAE,CAAC,EAAGC,CAAC,CAAC,CAC3D,CACAU,EAAE,KAAK,CAACV,EAAGW,CAAC,CAAC,CACd,CACD,IAAMK,EAAI,CAAC,EACX,OAAW,CAAC,CAAEjB,CAAC,IAAKW,EAAG,QAAWV,KAAKD,EAAGiB,EAAE,KAAK,CAAC,CAAChB,EAAE,CAAC,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,EAC/D,OAAOgB,CACR,EACaC,GAAsB,CAAC,EAAG,IAAM,CAC5C,IAAM,EAAIC,GAAE,EAAG,CAAC,EACf,EAAI,CAAC,CAAC,mBAAoB,CAAC,EAAE,OAAO,CAAC,CAAC,EACvC,GAAkB,EAAE,OAAhB,UAAsB,OAAO,EACjC,GAAkB,EAAE,OAAhB,UACH,OAAyB,EAAE,OAApB,cACJ,CAAC,CAAC,cAAe,CAAC,GAAI,GAAI,GAAI,EAAE,CAAC,CAAC,EACvB,EAAE,OAAb,OACA,CAAC,CAAC,KAAM,CAAC,CAAC,EACVC,GAAE,EAAE,IAAI,EACRV,GAAW,CAAC,CAAC,EAAE,IAAI,EAAG,MAAM,CAAC,EAC7BW,EAAE,EAAE,IAAI,EACR,CAAC,CAAC,CAAC,EAAE,IAAI,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,EAAG,CAAC,OAAO,kBAAmB,CAAC,EAAG,EAAE,CAAC,CAAC,EAC5D,CAAC,CAAC,CAAC,EAAE,IAAI,EAAG,CAAC,GAAI,EAAG,OAAO,kBAAmB,EAAE,CAAC,CAAC,EACtD,GAAID,GAAE,EAAE,IAAI,EAAG,CACd,GAAiB,EAAE,OAAf,SAAqB,CACxB,IAAMpB,EAAI,EAAE,MACZ,GAAe,EAAE,OAAb,QACH,GAAiBA,EAAE,OAAf,WAA8BA,EAAE,QAAR,GAAuBA,EAAE,QAAR,GAAgB,MAAO,CAAC,CAAC,KAAMA,EAAE,KAAK,CAAC,UAC9DA,EAAE,OAAd,SAAsBA,EAAE,SAASc,GAAE,EAAE,IAAI,EAAG,CACtD,GAAsB,EAAE,OAApB,cAA0B,MAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAGd,EAAE,KAAK,CAAC,EACzD,CACC,IAAMC,EAAI,CAAC,OAAQ,KAAM,SAAS,EAAE,QAAQD,EAAE,KAAK,EACnD,GAAWC,IAAP,GAAU,MAAO,CAAC,CAAC,cAAe,CAAC,GAAIA,GAAK,EAAGA,GAAK,EAAGA,GAAK,CAAC,CAAC,CAAC,CACpE,CACD,CACD,CACA,OAAO,CACR,CACA,GAAIoB,EAAE,EAAE,IAAI,EAAG,CACd,IAAIrB,EAAI,KACR,GAAiB,EAAE,OAAf,SAAqB,CACxB,IAAMC,EAAIqB,EAAE,EAAE,KAAK,EACVrB,IAAT,OAAeD,EAAI,CAAC,GAAIC,EAAGA,EAAG,EAAE,EACjC,SAAwB,EAAE,OAAf,SAAqB,CAC/B,IAAMA,EAAIqB,EAAE,EAAE,KAAK,EACVrB,IAAT,OACED,EACQ,EAAE,KAAV,IACG,CAAC,GAAI,CAAC,OAAO,kBAAmB,CAAC,EAAGC,EAAG,EAAE,EAChC,EAAE,KAAX,KACA,CAAC,GAAI,CAAC,OAAO,kBAAmB,CAAC,EAAGA,EAAG,EAAE,EACjC,EAAE,KAAV,IACA,CAAC,GAAIA,EAAG,CAAC,OAAO,kBAAmB,CAAC,EAAG,EAAE,EACzC,CAAC,GAAIA,EAAG,CAAC,OAAO,kBAAmB,CAAC,EAAG,EAAE,EAC/C,SAAwB,EAAE,OAAf,SAAqB,CAC/B,IAAMA,EAAIqB,EAAE,EAAE,GAAG,EAChBX,EAAIW,EAAE,EAAE,GAAG,EACHrB,IAAT,MAAuBU,IAAT,OAAeX,EAAI,CAAU,EAAE,QAAX,KAAkBC,EAAGU,EAAY,EAAE,QAAX,IAAgB,EAC3E,CACA,OAAgBX,IAAT,KAAa,EAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAGuB,GAAE,CAAC,EAAE,KAAMvB,CAAC,CAAC,CAAC,CAAC,CACpD,CACA,CACC,IAAIA,EAAI,KACR,GAAiB,EAAE,OAAf,SAAqB,CACxB,IAAMC,EAAIuB,EAAE,EAAE,MAAO,EAAE,IAAI,EAClBvB,IAAT,OAAeD,EAAI,CAAC,GAAIC,EAAGA,EAAG,EAAE,EACjC,SAAwB,EAAE,OAAf,SAAqB,CAC/B,IAAMA,EAAIuB,EAAE,EAAE,MAAO,EAAE,IAAI,EAClBvB,IAAT,OACED,EACQ,EAAE,KAAV,IACG,CAAC,GAAI,OAAO,kBAAmBC,EAAG,EAAE,EAC3B,EAAE,KAAX,KACA,CAAC,GAAI,OAAO,kBAAmBA,EAAG,EAAE,EAC5B,EAAE,KAAV,IACA,CAAC,GAAIA,EAAG,OAAO,kBAAmB,EAAE,EACpC,CAAC,GAAIA,EAAG,OAAO,kBAAmB,EAAE,EAC1C,SAAwB,EAAE,OAAf,SAAqB,CAC/B,IAAMA,EAAIuB,EAAE,EAAE,IAAK,EAAE,IAAI,EACxBb,EAAIa,EAAE,EAAE,IAAK,EAAE,IAAI,EACXvB,IAAT,MAAuBU,IAAT,OAAeX,EAAI,CAAU,EAAE,QAAX,KAAkBC,EAAGU,EAAY,EAAE,QAAX,IAAgB,EAC3E,CACA,OAAgBX,IAAT,KAAa,EAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAGuB,GAAE,CAAC,EAAE,KAAMvB,CAAC,CAAC,CAAC,CAAC,CACpD,CACD,EACayB,GAAwB,CAAC,EAAG,IAAM,CAC9C,IAAM,EAAI,CAAC,EACX,QAAW,KAAK,EAAE,SACjB,YAAa,EAAI,EAAE,KAAKP,GAAoB,EAAG,CAAC,CAAC,EAAI,EAAE,KAAKO,GAAsB,EAAG,CAAC,CAAC,EACxF,OAAgB,EAAE,WAAX,MAAkC,EAAE,WAAb,OAC3B,EAAE,KAAK,EACG,EAAE,WAAZ,MACA,EAAE,OAAO,CAACzB,EAAGC,IAAMF,GAASC,EAAGC,CAAC,CAAC,EACjCQ,GAAS,EAAE,CAAC,CAAC,CACjB,EACaiB,GAAgB,GAAK,CACjC,IAAM,EAAI,CAAC,EACVf,EAAI,IAAI,IACRgB,EAAI,IAAI,IACT,QAAWC,KAAK,EAAG,CAClB,IAAI5B,EAAI,GACR,GAAI,MAAM,QAAQ4B,EAAE,kBAAkB,CAAC,GAAKA,EAAE,kBAAkB,EAAE,OAAS,EAAG,CAC7E,QAAW5B,KAAK4B,EAAE,kBAAkB,EAAGjB,EAAE,IAAIX,CAAC,EAC9CA,EAAI,EACL,CACA,IAAM,EAAI,CAAC,EACX,QAAWC,KAAKG,EAAEwB,CAAC,EAClB,GAA2B3B,EAAE,CAAC,IAA1B,mBAA6B,CAChC,GAAsBA,EAAE,CAAC,IAArB,cAAwB,CAC3B,IAAMD,EAAIC,EAAE,CAAC,EAAE,SAAS,EACMD,IAA9B,0BACIC,EAAE,CAAC,EAAI,UACkBD,IAA1B,wBAAgCC,EAAE,CAAC,EAAI,SAC3C,MAAO4B,GAAE5B,CAAC,IAAMA,EAAE,CAAC,EAAIsB,GAAEtB,CAAC,GACZA,EAAE,CAAC,IAAjB,WACI0B,EAAE,IAAI1B,EAAE,CAAC,CAAC,EAAID,EAAI,IACNC,EAAE,CAAC,IAAhB,UAAuCA,EAAE,CAAC,IAApB,cAAmCA,EAAE,CAAC,IAAb,OAAmBI,EAAE,EAAGJ,CAAC,CAC5E,CACDD,GAAK,EAAE,KAAK,CAAC,CACd,CACA,MAAO,CAAC,YAAa,EAAG,gBAAiB,CAAC,GAAGW,CAAC,EAAE,KAAK,EAAG,cAAe,CAAC,GAAGgB,CAAC,EAAE,KAAK,CAAC,CACrF,EACaG,GAAa,CAAC,EAAG,EAAI,CAAC,IAAM,CACxC,IAAM,EAAIC,GAAE,CAAC,EACZ,EAAI,CAAC,EACN,QAAW9B,KAAK,EAAE,aAAc,CAC/B,IAAMD,EAAI,CAAC,EACDC,EAAE,SAAZ,OACgBA,EAAE,YAAd,QACCD,EAAE,KAAK,CAAC,aAAc,WAAW,CAAC,EACrBC,EAAE,YAAf,UAA4BD,EAAE,KAAK,CAAC,aAAc,YAAY,CAAC,EACtDC,EAAE,iBAAb,QACAD,EAAE,KAAK,GAAGS,GAASgB,GAAsBxB,EAAE,eAAgB,CAAC,CAAC,CAAC,GACnDA,EAAE,iBAAb,OACAD,EAAE,KAAK,CAAC,aAAcC,EAAE,SAAS,CAAC,EAClCD,EAAE,KACF,GAAGyB,GAAsBxB,EAAE,eAAgB,CAAC,EAAE,IAAID,IAAM,CACvD,GAAGA,EACH,aAAcC,EAAE,SACjB,EAAE,CACF,EACF,EAAE,KAAK,GAAGD,CAAC,CACb,CACA,OAAO0B,GAAc,CAAC,CACvB,EACaM,GAAe,CAACpB,EAAGD,EAAI,CAAC,IAAM,CAC1C,IAAMgB,EAAIM,GAAErB,CAAC,EACb,GAAIsB,GAAEP,CAAC,EACN,MAAM,IAAI,MAAM,mCAAmCA,EAAE,KAAK,aAAaA,EAAE,KAAK,IAAIA,EAAE,GAAG,EAAE,EAC1F,OAAOG,GAAWH,EAAGhB,CAAC,CACvB,EC5OO,IAAMwB,GAAsB,CAClC,UAAW,SACX,SAAU,QACV,WAAY,OACZ,WAAY,kBACZ,KAAM,SACN,MAAO,QACP,cAAe,SACf,eAAgB,SAChB,QAAS,OACT,KAAM,cACN,OAAQ,OACR,WAAY,OACZ,UAAW,EACX,eAAgB,iBAChB,YAAa,UACb,aAAc,UACd,oBAAqB,SACrB,aAAc,OACd,eAAgB,OAChB,YAAa,OACb,mBAAoB,gBACpB,gBAAiB,gBACjB,mBAAoB,gBACpB,qBAAsB,gBACtB,2BAA4B,gBAC5B,UAAW,UACX,gBAAiB,kBACjB,kBAAmB,UACnB,2BAA4B,EAC5B,yBAA0B,CAC3B,EACMC,EAAI,GAAK,IAAI,MAAM,mBAAmB,CAAC,EAAE,EAClCC,GAAcC,GAAK,CAC/B,GAAiBA,EAAE,YAAf,UAAwCA,EAAE,YAAd,SAAqDA,EAAE,YAA5B,sBAC1D,MAAMF,EAAE,WAAW,EACpB,GAAeE,EAAE,WAAb,QAAqCA,EAAE,WAAd,QAAwB,MAAMF,EAAE,UAAU,EACvE,GAAeE,EAAE,aAAb,QAAwCA,EAAE,aAAf,UAAwCA,EAAE,aAAb,OAC3D,MAAMF,EAAE,YAAY,EACrB,GACgBE,EAAE,aAAjB,YACsBA,EAAE,aAAxB,mBACyBA,EAAE,aAA3B,sBACcA,EAAE,aAAhB,UAEA,MAAMF,EAAE,YAAY,EACrB,GAAiBE,EAAE,OAAf,UAAkCA,EAAE,OAAb,OAAmB,MAAMF,EAAE,MAAM,EAC5D,GAAeE,EAAE,QAAb,QAAkCA,EAAE,QAAd,QAAqB,MAAMF,EAAE,OAAO,EAC9D,GAAeE,EAAE,gBAAb,QAA2CA,EAAE,gBAAf,UAA4CA,EAAE,gBAAd,QACjE,MAAMF,EAAE,eAAe,EACxB,GAAeE,EAAE,iBAAb,QAA4CA,EAAE,iBAAf,SAA+B,MAAMF,EAAE,gBAAgB,EAC1F,GAAeE,EAAE,UAAb,QAAqCA,EAAE,UAAf,UAAqCA,EAAE,UAAb,OAAsB,MAAMF,EAAE,SAAS,EAC7F,GAAoBE,EAAE,OAAlB,aAA4CA,EAAE,OAApB,cAA0B,MAAMF,EAAE,MAAM,EACtE,GAAeE,EAAE,SAAb,QAAkCA,EAAE,SAAb,QAAkCA,EAAE,SAAb,OAAqB,MAAMF,EAAE,QAAQ,EACvF,GAAI,EAAE,OAAO,UAAUE,EAAE,OAAO,GAAKA,EAAE,SAAW,GAAI,MAAMF,EAAE,SAAS,EACvE,GAAI,EAAE,OAAO,UAAUE,EAAE,QAAQ,GAAKA,EAAE,UAAY,GAAI,MAAMF,EAAE,UAAU,EAC1E,GAAI,EAAE,OAAO,UAAUE,EAAE,aAAa,GAAKA,EAAE,eAAiB,GAAI,MAAMF,EAAE,eAAe,EACzF,GAAI,EAAE,OAAO,UAAUE,EAAE,cAAc,GAAKA,EAAE,gBAAkB,GAAI,MAAMF,EAAE,gBAAgB,EAC5F,GAAI,EAAE,OAAO,UAAUE,EAAE,SAAS,GAAKA,EAAE,WAAa,GAAI,MAAMF,EAAE,WAAW,EAC7E,GAAIE,EAAE,MAAQ,EAAG,MAAMF,EAAE,MAAM,EAC/B,GACsBE,EAAE,iBAAvB,kBACA,EAAE,OAAO,UAAUA,EAAE,cAAc,GAAKA,EAAE,gBAAkB,GAE5D,MAAMF,EAAE,gBAAgB,EACzB,GAAeE,EAAE,aAAb,QAA2B,EAAE,OAAO,UAAUA,EAAE,UAAU,GAAKA,EAAE,YAAc,GAClF,MAAMF,EAAE,YAAY,CACtB,EACaG,GAAU,CAAC,EAAGD,IAAM,CAChC,IAAME,EAAI,CAAC,GAAGL,GAAqB,GAAGG,CAAC,EACvCD,GAAYG,CAAC,EACb,QAAW,KAAK,EAAE,YAAa,CAC9B,IAAIJ,EAAI,GACR,QAAW,KAAK,EAAG,CAClB,IAAMK,EAAI,EACTC,EAAI,EACL,GAAqBD,IAAjB,aAAoB,CACvB,IAAMH,EAAII,EAAED,CAAC,EACb,GAAgBH,IAAZ,SACH,GAAiBE,EAAE,YAAf,UAAsDA,EAAE,YAA5B,sBAAuC,CACtEJ,EAAI,GACJ,KACD,UACuBE,IAAb,UACV,GAAgBE,EAAE,YAAd,SAAqDA,EAAE,YAA5B,sBAAuC,CACrEJ,EAAI,GACJ,KACD,UAC2BE,IAAjB,cACV,GAAiBE,EAAE,YAAf,SAA0B,CAC7BJ,EAAI,GACJ,KACD,UACsBI,EAAE,YAAd,QAAyB,CACnCJ,EAAI,GACJ,KACD,CACD,SAA2BK,IAAhB,aACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,SAAU,CACxBJ,EAAI,GACJ,KACD,UACsBK,IAAZ,SACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,MAAO,CACrBJ,EAAI,GACJ,KACD,UAC4BK,IAAlB,eACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,WAAY,CAC1BJ,EAAI,GACJ,KACD,UACwBK,IAAd,WACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,QAAS,CACvBJ,EAAI,GACJ,KACD,UACqBK,IAAX,OAAc,CACxB,IAAMH,EAAII,EAAED,CAAC,EACb,GAAWH,IAAN,GAAsBE,EAAE,OAAb,QAA6BF,IAAN,GAAwBE,EAAE,OAAf,SAAsB,CACvEJ,EAAI,GACJ,KACD,CACD,SAA6BK,IAAlB,cAAqB,CAC/B,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACiBD,EAAE,aAAjB,YAA+B,CAACF,GACVE,EAAE,aAAxB,mBAAsC,CAACG,GACdH,EAAE,aAA3B,sBAAyC,CAACI,GAC5BJ,EAAE,aAAhB,WAA8B,CAAC,EAC/B,CACDJ,EAAI,GACJ,KACD,CACD,SAAmCK,IAAxB,oBAA2B,CACrC,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACiBD,EAAE,kBAAjB,YAAoC,CAACF,GACfE,EAAE,kBAAxB,mBAA2C,CAACG,GACnBH,EAAE,kBAA3B,sBAA8C,CAACI,GACjCJ,EAAE,kBAAhB,WAAmC,CAAC,EACpC,CACDJ,EAAI,GACJ,KACD,CACD,SAAgCK,IAArB,kBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,cAAe,CAC7BJ,EAAI,GACJ,KACD,UACgCK,IAAtB,mBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,eAAgB,CAC9BJ,EAAI,GACJ,KACD,UACqBK,IAAX,QACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,KAAM,CACpBJ,EAAI,GACJ,KACD,UACuBK,IAAb,UACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,OAAQ,CACtBJ,EAAI,GACJ,KACD,UAC0BK,IAAhB,aACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,UAAW,CACzBJ,EAAI,GACJ,KACD,UAC6BK,IAAnB,gBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,YAAa,CAC3BJ,EAAI,GACJ,KACD,UACqCK,IAA3B,wBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,oBAAqB,CACnCJ,EAAI,GACJ,KACD,UAC8BK,IAApB,iBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,aAAc,CAC5BJ,EAAI,GACJ,KACD,UACgCK,IAAtB,mBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,eAAgB,CAC9BJ,EAAI,GACJ,KACD,UAC6BK,IAAnB,gBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,YAAa,CAC3BJ,EAAI,GACJ,KACD,UACqCK,IAA3B,wBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,mBAAoB,CAClCJ,EAAI,GACJ,KACD,UACiCK,IAAvB,oBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,gBAAiB,CAC/BJ,EAAI,GACJ,KACD,UACqCK,IAA3B,wBACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,mBAAoB,CAClCJ,EAAI,GACJ,KACD,UACuCK,IAA7B,0BACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,qBAAsB,CACpCJ,EAAI,GACJ,KACD,UAC6CK,IAAnC,gCACV,GAAIC,EAAED,CAAC,IAAMD,EAAE,2BAA4B,CAC1CJ,EAAI,GACJ,KACD,UAC8BK,IAApB,iBACV,GAAeC,EAAED,CAAC,IAAd,QAAiCD,EAAE,eAAhB,UAA8B,CACpDJ,EAAI,GACJ,KACD,UACoCK,IAA1B,uBACV,GAAeC,EAAED,CAAC,IAAd,QAAiCD,EAAE,oBAAhB,UAAmC,CACzDJ,EAAI,GACJ,KACD,UAC2CK,IAAjC,6BAAoC,CAC9C,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACCD,EAAE,yBAA2BG,GAC7BH,EAAE,yBAA2BI,GAC5BD,IAAMH,EAAE,0BAA4B,CAACF,GACrCM,IAAMJ,EAAE,0BAA4B,CAAC,EACrC,CACDJ,EAAI,GACJ,KACD,CACD,SAA8CK,IAAnC,+BAAsC,CAChD,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACCD,EAAE,2BAA6BG,GAC/BH,EAAE,2BAA6BI,GAC9BD,IAAMH,EAAE,4BAA8B,CAACF,GACvCM,IAAMJ,EAAE,4BAA8B,CAAC,EACvC,CACDJ,EAAI,GACJ,KACD,CACD,SAAuBK,IAAZ,QAAe,CACzB,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GAAID,EAAE,QAAUG,GAAKH,EAAE,QAAUI,GAAMD,IAAMH,EAAE,SAAW,CAACF,GAAOM,IAAMJ,EAAE,SAAW,CAAC,EAAI,CACzFJ,EAAI,GACJ,KACD,CACD,SAA8BK,IAAnB,eAAsB,CAChC,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACCD,EAAE,cAAgBG,GAClBH,EAAE,cAAgBI,GACjBD,IAAMH,EAAE,eAAiB,CAACF,GAC1BM,IAAMJ,EAAE,eAAiB,CAAC,EAC1B,CACDJ,EAAI,GACJ,KACD,CACD,SAAwBK,IAAb,SAAgB,CAC1B,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACCD,EAAE,SAAWG,GACbH,EAAE,SAAWI,GACZD,IAAMH,EAAE,UAAY,CAACF,GACrBM,IAAMJ,EAAE,UAAY,CAAC,EACrB,CACDJ,EAAI,GACJ,KACD,CACD,SAA+BK,IAApB,gBAAuB,CACjC,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACCD,EAAE,eAAiBG,GACnBH,EAAE,eAAiBI,GAClBD,IAAMH,EAAE,gBAAkB,CAACF,GAC3BM,IAAMJ,EAAE,gBAAkB,CAAC,EAC3B,CACDJ,EAAI,GACJ,KACD,CACD,SAAuBK,IAAZ,QAAe,CACzB,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GACCD,EAAE,UAAYG,GACdH,EAAE,UAAYI,GACbD,IAAMH,EAAE,WAAa,CAACF,GACtBM,IAAMJ,EAAE,WAAa,CAAC,EACtB,CACDJ,EAAI,GACJ,KACD,CACD,SAA4BK,IAAjB,aAAoB,CAC9B,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GAAyBD,EAAE,iBAAvB,kBACFG,EAAI,GAAYA,IAAN,GAAW,CAACL,GAAaM,IAAN,GAAW,CAAC,KAAQR,EAAI,YAEtDI,EAAE,eAAiBG,GACnBH,EAAE,eAAiBI,GAClBD,IAAMH,EAAE,gBAAkB,CAACF,GAC3BM,IAAMJ,EAAE,gBAAkB,CAAC,EAC3B,CACDJ,EAAI,GACJ,KACD,CACD,SAA4BK,IAAjB,aAAoB,CAC9B,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GAAID,EAAE,KAAOG,GAAKH,EAAE,KAAOI,GAAMD,IAAMH,EAAE,MAAQ,CAACF,GAAOM,IAAMJ,EAAE,MAAQ,CAAC,EAAI,CAC7EJ,EAAI,GACJ,KACD,CACD,SAA6BK,IAAlB,cAAqB,CAC/B,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACxB,GAAeD,EAAE,aAAb,QACH,GAAIG,EAAI,GAAYA,IAAN,GAAW,CAACL,GAAaM,IAAN,GAAW,CAAC,EAAI,CAChDR,EAAI,GACJ,KACD,UAEAI,EAAE,WAAaG,GACfH,EAAE,WAAaI,GACdD,IAAMH,EAAE,YAAc,CAACF,GACvBM,IAAMJ,EAAE,YAAc,CAAC,EACvB,CACDJ,EAAI,GACJ,KACD,CACD,SAA8BK,IAAnB,eAAsB,CAChC,GAAM,CAACH,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACvBI,EAAIF,EAAE,CAAC,EAAIA,EAAE,CAAC,EACdG,EAAIF,EAAE,CAAC,EAAIA,EAAE,CAAC,EACdG,EAAIP,EAAE,QAAUA,EAAE,SACnB,GAAIO,EAAIF,GAAKE,EAAID,GAAMD,IAAME,GAAK,CAACT,GAAOQ,IAAMC,GAAK,CAAC,EAAI,CACzDX,EAAI,GACJ,KACD,CACD,KAAO,CACN,GAAM,CAACE,EAAGK,EAAGC,EAAG,CAAC,EAAIF,EAAED,CAAC,EACvBI,EAAIF,EAAE,CAAC,EAAIA,EAAE,CAAC,EACdG,EAAIF,EAAE,CAAC,EAAIA,EAAE,CAAC,EACdG,EAAIP,EAAE,cAAgBA,EAAE,eACzB,GAAIO,EAAIF,GAAKE,EAAID,GAAMD,IAAME,GAAK,CAACT,GAAOQ,IAAMC,GAAK,CAAC,EAAI,CACzDX,EAAI,GACJ,KACD,CACD,CACD,CACA,GAAIA,EAAG,MAAO,EACf,CACA,MAAO,EACR,EjB9Ve,SAARY,GAAwBC,EAA+B,CAC7D,GAAM,CAAE,gBAAAC,EAAkB,EAAM,EAAID,GAAQ,CAAC,EAC7C,OAAQE,GAAoB,CAC3B,IAAMC,EAAkBF,EAAkB,CAAC,oBAAoB,EAAI,CAAC,EAC9DG,EAA0B,CAAC,EACjCC,GAASH,EAAK,CAACI,EAAYC,IAAkB,CACxCD,EAAK,OAASE,IACbF,EAAK,OAAS,UACjBH,EAAM,KACLG,EAAK,SACH,IAAKG,GAAaA,EAAE,OAASC,GAAYD,EAAE,MAAQ,EAAG,EACtD,KAAK,EAAE,CACV,EACAL,EAAQ,KAAK,IAAM,CAClBG,EAAQ,SAAWA,EAAQ,SAAS,OAAQE,GAAYA,IAAMH,CAAI,CACnE,CAAC,EAGJ,CAAC,EACD,QAAWK,KAAUP,EACpBO,EAAO,EAER,IAAMC,EAAST,EAAM,KAAK;AAAA,CAAI,EACxBU,EAAMC,GAAQF,CAAM,EACpBG,EAAY,IAAI,IAEtB,SAASC,EAAUC,EAAgB,CAClC,GAAIA,EAAK,OAAS,OAAQ,CACzB,IAAMC,EAAQ,OAAO,YACnBD,EAAK,SAAkC,IAAKE,GAAe,CAC3DA,EAAM,MACNA,EAAM,QACP,CAAC,CACF,EACA,QAAWC,KAAYH,EAAK,MAAO,CAClC,IAAMI,EAAQ,OAAO,OAAON,EAAU,IAAIK,CAAQ,GAAK,CAAC,EAAGF,CAAK,EAChEH,EAAU,IAAIK,EAAUC,CAAK,CAC9B,CACD,SAAWJ,EAAK,OAAS,WAAYjB,GAAA,MAAAA,EAAM,KAAK,CAC/C,IAAMsB,EAAMC,GAAevB,EAAK,GAAG,EAE7BwB,GADO,MAAM,QAAQP,EAAK,KAAK,EAAIA,EAAK,MAAQ,CAACA,EAAK,KAAK,GAC5C,IAAKQ,GAAQC,GAAaD,CAAG,CAAC,EACnD,QAAWE,KAASH,EACnB,GAAII,GAAQD,EAAOL,CAAG,EAAG,CACxB,QAAWH,KAASF,EAAK,SACxBD,EAAUG,CAAiB,EAE5B,MACD,CAEF,CACD,CACA,QAAWF,KAAQJ,EAClBG,EAAUC,CAAI,EAEf,IAAMC,EAAQ,IAAI,IAClB,OAAW,CAACE,EAAUR,CAAM,IAAK,MAAM,KAAKG,CAAS,EAAE,KAAK,CAAC,CAACc,CAAC,EAAG,CAACC,CAAC,IAAM,CACzE,IAAMC,EAAKC,GAAYH,CAAC,EAClBI,EAAKD,GAAYF,CAAC,EACxB,OAAIC,EAAKE,EAAW,EAChBA,EAAKF,EAAW,GACb,CACR,CAAC,EAAG,CACH,IAAMG,EAAQC,GAAiBjC,EAAKkB,CAAQ,EAC5C,QAAWd,KAAQ4B,EAAO,CACzB,IAAME,EAAOlB,EAAM,IAAIZ,CAAI,GAAK,CAAC,EACjCY,EAAM,IAAIZ,EAAM,OAAO,OAAO8B,EAAMxB,CAAM,CAAC,CAC5C,CACD,CAEA,OAAW,CAACN,EAAMW,CAAI,IAAKC,EAAO,CACjC,IAAIf,EAAQG,EAAK,WAAW,OAAS,GACjC+B,EAAmC,CAAC,EACxC,QAAWC,KAAQxB,GAAQX,CAAK,EAC3BmC,EAAK,OAAS,QAEhB,OAAOA,EAAK,OAAU,UACtB,OAAOA,EAAK,UAAa,WAEzBD,EAASC,EAAK,KAAK,EAAIA,EAAK,UAI/BD,EAAW,OAAO,OAAO,CAAC,EAAGpB,EAAMoB,CAAQ,EACvCpC,EACHK,EAAK,WAAW,MAAQ+B,EAExB/B,EAAK,WAAW,MAAQ,GAAG,OAAO,QAAQ+B,CAAQ,EAChD,IAAI,CAAC,CAACC,EAAMjB,CAAK,IAAM,GAAGiB,CAAI,IAAIjB,EAAM,QAAQ,aAAc,EAAE,CAAC,GAAG,EACpE,KAAK,EAAE,CAAC,EAEZ,CACA,OAAOnB,CACR,CACD,CAUA,SAASqB,GAAegB,EAAoD,CAC3E,GAAM,CACL,MAAAC,EACA,OAAAC,EACA,KAAAC,EAAO,EACP,QAAAC,EAAUH,EACV,SAAAI,EAAWH,EACX,cAAAI,EAAgBL,EAAQE,EACxB,eAAAI,EAAiBL,EAASC,EAC1B,GAAGpB,CACJ,EAAIiB,EACJ,MAAO,CACN,QAAAI,EACA,SAAAC,EACA,cAAAC,EACA,eAAAC,EACA,KAAAJ,EACA,GAAGpB,CACJ,CACD", "names": ["walkSync", "ELEMENT_NODE", "TEXT_NODE", "querySelectorAll", "specificity", "COMMENT", "RULESET", "DECLARATION", "abs", "from", "trim", "value", "replace", "value", "pattern", "replacement", "indexof", "search", "position", "charat", "index", "substr", "begin", "end", "strlen", "sizeof", "append", "array", "line", "column", "length", "position", "character", "characters", "node", "value", "root", "parent", "type", "props", "children", "siblings", "char", "character", "prev", "position", "charat", "characters", "column", "line", "next", "length", "peek", "caret", "slice", "begin", "end", "substr", "token", "type", "alloc", "value", "strlen", "dealloc", "delimit", "trim", "delimiter", "whitespace", "type", "character", "peek", "next", "token", "escaping", "index", "count", "next", "character", "slice", "caret", "peek", "delimiter", "type", "position", "commenter", "from", "identifier", "token", "compile", "value", "dealloc", "parse", "alloc", "root", "parent", "rule", "rules", "rulesets", "pseudo", "points", "declarations", "index", "offset", "length", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "character", "type", "props", "children", "reference", "characters", "next", "charat", "indexof", "replace", "delimit", "abs", "whitespace", "escaping", "caret", "peek", "append", "comment", "commenter", "token", "strlen", "substr", "declaration", "ruleset", "prev", "from", "identifier", "siblings", "post", "size", "sizeof", "i", "j", "k", "x", "y", "z", "trim", "node", "RULESET", "COMMENT", "char", "DECLARATION", "isParserError", "splitMediaQueryList", "t", "r", "n", "readMediaQueryList", "e", "readMediaQuery", "i", "readMediaCondition", "a", "d", "s", "o", "l", "u", "readMediaFeature", "readRange", "p", "f", "c", "h", "v", "flattenMediaQueryList", "e", "flattenMediaQuery", "flattenMediaCondition", "o", "i", "invertParserError", "t", "d", "e", "deleteUndefinedValues", "e", "readCodepoints", "s", "t", "r", "a", "convertToParserTokens", "t", "r", "s", "codepointsToTokens", "f", "c", "p", "d", "h", "e", "n", "s", "consumeString", "t", "wouldStartIdentifier", "consumeIdentUnsafe", "consumeNumeric", "consumeIdentLike", "consumeIdent", "u", "r", "consumeEscape", "i", "a", "consumeNumber", "o", "l", "consumeUrl", "lexer", "m", "e", "codepointsToTokens", "readCodepoints", "isParserError", "convertToParserTokens", "isParserError", "r", "parseMediaQueryList", "t", "e", "lexer", "isParserError", "invertParserError", "deleteUndefinedValues", "flattenMediaQueryList", "readMediaQueryList", "DISCRETE_FEATURES", "RANGE_NUMBER_FEATURES", "RANGE_RATIO_FEATURES", "permToConditionPairs", "e", "hasDiscreteKey", "t", "isDiscreteKey", "hasRangeRatioKey", "isRangeRatioKey", "o", "hasRangeNumberKey", "isRangeNumberKey", "hasRange<PERSON>ey", "isRangeKey", "isFeatureKey", "isRangeNumberKey", "isRangeRatioKey", "isDiscreteKey", "attachPair", "t", "and<PERSON><PERSON><PERSON>", "e", "o", "n", "r", "s", "a", "i", "p", "u", "d", "l", "g", "f", "h", "y", "R", "b", "m", "boundRange", "hasRangeRatioKey", "RANGE_RATIO_FEATURES", "RANGE_NUMBER_FEATURES", "notRatioRange", "t", "RANGE_RATIO_FEATURES", "o", "n", "r", "s", "i", "c", "d", "l", "notNumberRange", "RANGE_NUMBER_FEATURES", "i", "convertToUnit", "t", "n", "compileStaticUnitConversions", "r", "o", "a", "u", "p", "m", "s", "f", "v", "c", "simplifyMediaFeature", "isRangeKey", "e", "isFeatureKey", "getRatio", "getValue", "RANGE_NUMBER_FEATURES", "andPerms", "e", "r", "mergePerms", "a", "permToConditionPairs", "attachPair", "hasRangeNumberKey", "hasRangeRatioKey", "and<PERSON><PERSON><PERSON>", "notPerms", "invertPerm", "o", "t", "hasDiscreteKey", "DISCRETE_FEATURES", "notRatioRange", "notNumberRange", "s", "mediaFeatureToPerms", "simplifyMediaFeature", "isDiscreteKey", "isRangeRatioKey", "getRatio", "boundRange", "getValue", "mediaConditionToPerms", "simplifyPerms", "n", "i", "hasRange<PERSON>ey", "compileAST", "compileStaticUnitConversions", "compileQuery", "parseMediaQueryList", "isParserError", "DESKTOP_ENVIRONMENT", "e", "validateEnv", "o", "matches", "r", "t", "n", "i", "s", "a", "l", "f", "inline", "opts", "useObjectSyntax", "doc", "style", "actions", "walkSync", "node", "parent", "ELEMENT_NODE", "c", "TEXT_NODE", "action", "styles", "css", "compile", "selectors", "applyRule", "rule", "rules", "child", "selector", "value", "env", "getEnvironment", "queries", "arg", "compileQuery", "query", "matches", "a", "b", "$a", "specificity", "$b", "nodes", "querySelectorAll", "curr", "styleObj", "decl", "baseEnv", "width", "height", "dppx", "widthPx", "heightPx", "deviceWidthPx", "deviceHeightPx"]}