<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 简单控制器
 */

declare(strict_types=1);

namespace app\controller;

use think\Response;

/**
 * 简单控制器 - 不依赖任何外部服务
 */
class SimpleController
{
    /**
     * 简单测试
     * @return Response
     */
    public function test(): Response
    {
        return json([
            'code' => 200,
            'message' => 'Simple API Test Success',
            'data' => [
                'time' => date('Y-m-d H:i:s'),
                'server' => 'PHP ' . PHP_VERSION,
                'framework' => 'ThinkPHP 6.1.4'
            ],
            'timestamp' => time()
        ]);
    }

    /**
     * 环境信息
     * @return Response
     */
    public function env(): Response
    {
        return json([
            'code' => 200,
            'message' => 'Environment Info',
            'data' => [
                'php_version' => PHP_VERSION,
                'cache_driver' => env('CACHE_DRIVER', 'not_set'),
                'database_host' => env('DATABASE_HOSTNAME', 'not_set'),
                'database_name' => env('DATABASE_DATABASE', 'not_set'),
                'app_debug' => env('APP_DEBUG', 'not_set'),
                'loaded_extensions' => [
                    'pdo' => extension_loaded('pdo'),
                    'pdo_mysql' => extension_loaded('pdo_mysql'),
                    'redis' => extension_loaded('redis'),
                    'curl' => extension_loaded('curl'),
                    'json' => extension_loaded('json')
                ]
            ],
            'timestamp' => time()
        ]);
    }

    /**
     * 配置测试
     * @return Response
     */
    public function config(): Response
    {
        try {
            // 尝试获取配置
            $config = config();
            
            return json([
                'code' => 200,
                'message' => 'Config Test Success',
                'data' => [
                    'app_name' => $config->get('app.name', 'not_set'),
                    'app_debug' => $config->get('app.debug', 'not_set'),
                    'cache_default' => $config->get('cache.default', 'not_set'),
                    'database_default' => $config->get('database.default', 'not_set'),
                ],
                'timestamp' => time()
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => 'Config Test Failed: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time()
            ], 500);
        }
    }
}
