import{ELEMENT_NODE as f,TEXT_NODE as _,walkSync as j}from"./index.js";var w={attribute:/\[\s*(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)\s*(?:(?<operator>\W?=)\s*(?<value>.+?)\s*(\s(?<caseSensitive>[iIsS]))?\s*)?\]/gu,id:/#(?<name>[-\w\P{ASCII}]+)/gu,class:/\.(?<name>[-\w\P{ASCII}]+)/gu,comma:/\s*,\s*/g,combinator:/\s*[\s>+~]\s*/g,"pseudo-element":/::(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,"pseudo-class":/:(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,universal:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?\*/gu,type:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)/gu},v=new Set(["combinator","comma"]),A=new Set(["not","is","where","has","matches","-moz-any","-webkit-any","nth-child","nth-last-child"]),E=/(?<index>[\dn+-]+)\s+of\s+(?<subtree>.+)/,k={"nth-child":E,"nth-last-child":E},T=t=>{switch(t){case"pseudo-element":case"pseudo-class":return new RegExp(w[t].source.replace("(?<argument>\xB6*)","(?<argument>.*)"),"gu");default:return w[t]}};function M(t,n){let e=0,r="";for(;n<t.length;n++){let s=t[n];switch(s){case"(":++e;break;case")":--e}if(r+=s,e===0)return r}return r}function O(t,n=w){if(!t)return[];let e=[t];for(let[s,o]of Object.entries(n))for(let i=0;i<e.length;i++){let c=e[i];if(typeof c!="string")continue;o.lastIndex=0;let a=o.exec(c);if(!a)continue;let u=a.index-1,l=[],h=a[0],m=c.slice(0,u+1);m&&l.push(m),l.push({...a.groups,type:s,content:h});let y=c.slice(u+h.length+1);y&&l.push(y),e.splice(i,1,...l)}let r=0;for(let s of e)switch(typeof s){case"string":throw new Error(`Unexpected sequence ${s} found at index ${r}`);case"object":r+=s.content.length,s.pos=[r-s.content.length,r],v.has(s.type)&&(s.content=s.content.trim()||" ")}return e}var $=/(['"])([^\\\n]+?)\1/g,P=/\\./g;function U(t,n=w){if((t=t.trim())==="")return[];let e=[];t=(t=t.replace(P,(o,i)=>(e.push({value:o,offset:i}),"\uE000".repeat(o.length)))).replace($,(o,i,c,a)=>(e.push({value:o,offset:a}),`${i}${"\uE001".repeat(c.length)}${i}`));{let o,i=0;for(;(o=t.indexOf("(",i))>-1;){let c=M(t,o);e.push({value:c,offset:o}),t=`${t.substring(0,o)}(${"\xB6".repeat(c.length-2)})${t.substring(o+c.length)}`,i=o+c.length}}let r=O(t,n),s=new Set;for(let o of e.reverse())for(let i of r){let{offset:c,value:a}=o;if(!(i.pos[0]<=c&&c+a.length<=i.pos[1]))continue;let{content:u}=i,l=c-i.pos[0];i.content=u.slice(0,l)+a+u.slice(l+a.length),i.content!==u&&s.add(i)}for(let o of s){let i=T(o.type);if(!i)throw new Error(`Unknown token type: ${o.type}`);i.lastIndex=0;let c=i.exec(o.content);if(!c)throw new Error(`Unable to parse content for ${o.type}: ${o.content}`);Object.assign(o,c.groups)}return r}function g(t,{list:n=!0}={}){if(n&&t.find(e=>e.type==="comma")){let e=[],r=[];for(let s=0;s<t.length;s++)if(t[s].type==="comma"){if(r.length===0)throw new Error("Incorrect comma at "+s);e.push(g(r,{list:!1})),r.length=0}else r.push(t[s]);if(r.length===0)throw new Error("Trailing comma");return e.push(g(r,{list:!1})),{type:"list",list:e}}for(let e=t.length-1;e>=0;e--){let r=t[e];if(r.type==="combinator"){let s=t.slice(0,e),o=t.slice(e+1);return{type:"complex",combinator:r.content,left:g(s),right:g(o)}}}switch(t.length){case 0:throw new Error("Could not build AST.");case 1:return t[0];default:return{type:"compound",list:[...t]}}}function*p(t,n){switch(t.type){case"list":for(let e of t.list)yield*p(e,t);break;case"complex":yield*p(t.left,t),yield*p(t.right,t);break;case"compound":yield*t.list.map(e=>[e,t]);break;default:yield[t,n]}}function b(t,{recursive:n=!0,list:e=!0}={}){let r=U(t);if(!r)return;let s=g(r,{list:e});if(!n)return s;for(let[o]of p(s)){if(o.type!=="pseudo-class"||!o.argument||!A.has(o.name))continue;let i=o.argument,c=k[o.name];if(c){let a=c.exec(i);if(!a)continue;Object.assign(o,a.groups),i=a.groups.subtree}i&&Object.assign(o,{subtree:b(i,{recursive:!0,list:!0})})}return s}function x(t,n){return n=n||Math.max(...t)+1,t[0]*(n<<1)+t[1]*n+t[2]}function N(t){let n=t;if(typeof n=="string"&&(n=b(n,{recursive:!0})),!n)return[];if(n.type==="list"&&"list"in n){let r=10,s=n.list.map(i=>{let c=N(i);return r=Math.max(r,...N(i)),c}),o=s.map(i=>x(i,r));return s[o.indexOf(Math.max(...o))]}let e=[0,0,0];for(let[r]of p(n))switch(r.type){case"id":e[0]++;break;case"class":case"attribute":e[1]++;break;case"pseudo-element":case"type":e[2]++;break;case"pseudo-class":if(r.name==="where")break;if(!A.has(r.name)||!r.subtree){e[1]++;break}N(r.subtree).forEach((s,o)=>e[o]+=s),r.name!=="nth-child"&&r.name!=="nth-last-child"||e[1]++}return e}function G(t){return x(N(t),10)}function J(t,n){return d(n)(t,t.parent,I(t,t.parent))}function X(t,n){let e=d(n);try{return C(t,(r,s,o)=>{let i=e(r,s,o);return i||!1},{single:!0})[0]}catch(r){if(r instanceof Error)throw r;return r}}function R(t,n){let e=d(n);return C(t,(r,s,o)=>{let i=e(r,s,o);return i||!1})}var F=R;function C(t,n,e={single:!1}){let r=[];return j(t,(s,o,i)=>{if(!(s&&s.type!==f)&&n(s,o,i)){if(e.single)throw s;r.push(s)}}),r}var L=t=>{let{operator:n="="}=t;switch(n){case"=":return(e,r)=>e===r;case"~=":return(e,r)=>e.split(/\s+/g).includes(r);case"|=":return(e,r)=>e.startsWith(r+"-");case"*=":return(e,r)=>e.indexOf(r)>-1;case"$=":return(e,r)=>e.endsWith(r);case"^=":return(e,r)=>e.startsWith(r)}return(e,r)=>!1},I=(t,n)=>n==null?void 0:n.children.filter(e=>e.type===f).findIndex(e=>e===t),W=t=>{let[n,e="1",r="0"]=/^\s*(?:(-?(?:\d+)?)n)?\s*\+?\s*(\d+)?\s*$/gm.exec(t)??[];e.length===0&&(e="1");let s=Number.parseInt(e==="-"?"-1":e),o=Number.parseInt(r);return i=>s*i+o},D=(t,n)=>(n==null?void 0:n.children.filter(e=>e.type===f).pop())===t,q=(t,n)=>(n==null?void 0:n.children.filter(e=>e.type===f).shift())===t,z=(t,n)=>(n==null?void 0:n.children.filter(e=>e.type===f).length)===1,S=t=>{switch(t.type){case"type":return n=>t.content==="*"?!0:n.name===t.name;case"class":return n=>{var e,r;return(r=(e=n.attributes)==null?void 0:e.class)==null?void 0:r.split(/\s+/g).includes(t.name)};case"id":return n=>{var e;return((e=n.attributes)==null?void 0:e.id)===t.name};case"pseudo-class":switch(t.name){case"global":return(...n)=>d(b(t.argument))(...n);case"not":return(...n)=>!S(t.subtree)(...n);case"is":return(...n)=>d(t.subtree)(...n);case"where":return(...n)=>d(t.subtree)(...n);case"root":return(n,e)=>n.type===f&&n.name==="html";case"empty":return n=>n.type===f&&(n.children.length===0||n.children.every(e=>e.type===_&&e.value.trim()===""));case"first-child":return(n,e)=>q(n,e);case"last-child":return(n,e)=>D(n,e);case"only-child":return(n,e)=>z(n,e);case"nth-child":return(n,e)=>{let r=I(n,e)+1;if(Number.isNaN(Number(t.argument)))switch(t.argument){case"odd":return Math.abs(r%2)==1;case"even":return r%2===0;default:{if(!t.argument)throw new Error("Unsupported empty nth-child selector!");let s=W(t.argument),o=e==null?void 0:e.children.filter(c=>c.type===f),i=I(n,e)+1;for(let c=0;c<o.length;c++){let a=s(c);if(a>o.length)return!1;if(a===i)return!0}return!1}}return r===Number(t.argument)};default:throw new Error(`Unhandled pseudo-class: ${t.name}!`)}case"attribute":return n=>{let{caseSensitive:e,name:r,value:s}=t;if(!n.attributes)return!1;let o=Object.entries(n.attributes);for(let[i,c]of o)if(e==="i"&&(s=r.toLowerCase(),c=i.toLowerCase()),i===r){if(!s)return!0;if((s[0]==='"'||s[0]==="'")&&s[0]===s[s.length-1]&&(s=JSON.parse(s)),s)return L(t)(c,s)}return!1};case"universal":return n=>!0;default:throw new Error(`Unhandled selector: ${t.type}`)}},d=t=>{let n=typeof t=="string"?b(t):t;switch(n==null?void 0:n.type){case"list":{let e=n.list.map(r=>S(r));return(r,s,o)=>{for(let i of e)if(i(r,s))return!0;return!1}}case"compound":{let e=n.list.map(r=>S(r));return(r,s,o)=>{for(let i of e)if(!i(r,s))return!1;return!0}}case"complex":{let{left:e,right:r,combinator:s}=n,o=d(e),i=d(r),c=new WeakSet;return(a,u,l=0)=>{if((o(a)||u&&c.has(u)&&s===" ")&&c.add(a),!i(a))return!1;switch(s){case" ":case">":return u?c.has(u):!1;case"~":{if(!u)return!1;for(let h of u.children.slice(0,l))if(c.has(h))return!0;return!1}case"+":{if(!u)return!1;let h=u.children.slice(0,l).filter(y=>y.type===f);if(h.length===0)return!1;let m=h[h.length-1];if(!m)return!1;if(c.has(m))return!0}default:return!1}}}default:return S(n)}};export{F as default,J as matches,X as querySelector,R as querySelectorAll,G as specificity};
