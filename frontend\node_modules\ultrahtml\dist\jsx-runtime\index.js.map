{"version": 3, "sources": ["../../src/jsx-runtime/index.ts"], "sourcesContent": ["import { ElementNode, Node, TEXT_NODE } from '../index.js';\nimport { ELEMENT_NODE, Fragment, __unsafeRenderFn } from '../index.js';\n\nfunction createVNode(\n\ttype: any,\n\t{ children, ...attributes }: Record<string, any>,\n\tkey: string,\n\t__self: string,\n\t__source: string,\n) {\n\tconst vnode: ElementNode = {\n\t\ttype: ELEMENT_NODE,\n\t\tname: typeof type === 'function' ? type.name : type,\n\t\tattributes,\n\t\tchildren: (Array.isArray(children) ? children : [children]).map((child) => {\n\t\t\tif (typeof child === 'string') {\n\t\t\t\treturn {\n\t\t\t\t\ttype: TEXT_NODE,\n\t\t\t\t\tvalue: child,\n\t\t\t\t};\n\t\t\t}\n\t\t\treturn child;\n\t\t}),\n\t\tparent: undefined as any,\n\t\tloc: [] as any,\n\t};\n\n\tif (typeof type === 'function') {\n\t\t__unsafeRenderFn(vnode, type);\n\t}\n\n\treturn vnode;\n}\n\nexport {\n\tcreateVNode as jsx,\n\tcreateVNode as jsxs,\n\tcreateVNode as jsxDEV,\n\tFragment,\n};\n"], "mappings": "AAAA,OAA4B,aAAAA,MAAiB,cAC7C,OAAS,gBAAAC,EAAc,YAAAC,EAAU,oBAAAC,MAAwB,cAEzD,SAASC,EACRC,EACA,CAAE,SAAAC,EAAU,GAAGC,CAAW,EAC1BC,EACAC,EACAC,EACC,CACD,IAAMC,EAAqB,CAC1B,KAAMV,EACN,KAAM,OAAOI,GAAS,WAAaA,EAAK,KAAOA,EAC/C,WAAAE,EACA,UAAW,MAAM,QAAQD,CAAQ,EAAIA,EAAW,CAACA,CAAQ,GAAG,IAAKM,GAC5D,OAAOA,GAAU,SACb,CACN,KAAMZ,EACN,MAAOY,CACR,EAEMA,CACP,EACD,OAAQ,OACR,IAAK,CAAC,CACP,EAEA,OAAI,OAAOP,GAAS,YACnBF,EAAiBQ,EAAON,CAAI,EAGtBM,CACR", "names": ["TEXT_NODE", "ELEMENT_NODE", "Fragment", "__unsafeRenderFn", "createVNode", "type", "children", "attributes", "key", "__self", "__source", "vnode", "child"]}