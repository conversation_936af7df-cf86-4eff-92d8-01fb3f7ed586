<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - DIY验证器
 */

declare(strict_types=1);

namespace app\validate;

use think\Validate;

/**
 * DIY验证器
 */
class DiyValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        // 页面验证规则
        'title' => 'require|length:1,200',
        'slug' => 'require|length:1,200|alphaNum',
        'description' => 'length:0,500',
        'seo_title' => 'length:0,200',
        'seo_description' => 'length:0,500',
        'seo_keywords' => 'length:0,500',
        'template_id' => 'integer|egt:0',
        'layout' => 'length:0,50',
        'theme' => 'length:0,50',
        'custom_css' => 'length:0,10000',
        'custom_js' => 'length:0,10000',
        'status' => 'integer|in:0,1,2',
        'is_home' => 'integer|in:0,1',
        'sort_order' => 'integer|egt:0',
        
        // 组件验证规则
        'name' => 'require|length:1,100',
        'component_slug' => 'require|length:1,100|alphaNum',
        'type' => 'require|length:1,50',
        'category' => 'require|length:1,50',
        'component_description' => 'length:0,500',
        'icon' => 'length:0,100',
        'preview' => 'length:0,255',
        'is_system' => 'integer|in:0,1',
        'is_active' => 'integer|in:0,1',
        'component_sort_order' => 'integer|egt:0',
        
        // 模板验证规则
        'template_name' => 'require|length:1,100',
        'template_slug' => 'require|length:1,100|alphaNum',
        'template_description' => 'length:0,500',
        'template_preview' => 'length:0,255',
        'thumbnail' => 'length:0,255',
        'template_category' => 'require|length:1,50',
        'version' => 'length:0,20',
        'author' => 'length:0,50',
        'is_free' => 'integer|in:0,1',
        'price' => 'float|egt:0',
        'template_sort_order' => 'integer|egt:0',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        // 页面验证消息
        'title.require' => '页面标题不能为空',
        'title.length' => '页面标题长度不能超过200个字符',
        'slug.require' => '页面别名不能为空',
        'slug.length' => '页面别名长度不能超过200个字符',
        'slug.alphaNum' => '页面别名只能包含字母和数字',
        'description.length' => '页面描述长度不能超过500个字符',
        'seo_title.length' => 'SEO标题长度不能超过200个字符',
        'seo_description.length' => 'SEO描述长度不能超过500个字符',
        'seo_keywords.length' => 'SEO关键词长度不能超过500个字符',
        'template_id.integer' => '模板ID必须是整数',
        'template_id.egt' => '模板ID必须大于等于0',
        'layout.length' => '布局类型长度不能超过50个字符',
        'theme.length' => '主题样式长度不能超过50个字符',
        'custom_css.length' => '自定义CSS长度不能超过10000个字符',
        'custom_js.length' => '自定义JS长度不能超过10000个字符',
        'status.integer' => '状态必须是整数',
        'status.in' => '状态值无效',
        'is_home.integer' => '是否首页必须是整数',
        'is_home.in' => '是否首页值无效',
        'sort_order.integer' => '排序必须是整数',
        'sort_order.egt' => '排序必须大于等于0',
        
        // 组件验证消息
        'name.require' => '组件名称不能为空',
        'name.length' => '组件名称长度不能超过100个字符',
        'component_slug.require' => '组件标识不能为空',
        'component_slug.length' => '组件标识长度不能超过100个字符',
        'component_slug.alphaNum' => '组件标识只能包含字母和数字',
        'type.require' => '组件类型不能为空',
        'type.length' => '组件类型长度不能超过50个字符',
        'category.require' => '组件分类不能为空',
        'category.length' => '组件分类长度不能超过50个字符',
        'component_description.length' => '组件描述长度不能超过500个字符',
        'icon.length' => '组件图标长度不能超过100个字符',
        'preview.length' => '预览图长度不能超过255个字符',
        'is_system.integer' => '是否系统组件必须是整数',
        'is_system.in' => '是否系统组件值无效',
        'is_active.integer' => '是否启用必须是整数',
        'is_active.in' => '是否启用值无效',
        'component_sort_order.integer' => '排序必须是整数',
        'component_sort_order.egt' => '排序必须大于等于0',
        
        // 模板验证消息
        'template_name.require' => '模板名称不能为空',
        'template_name.length' => '模板名称长度不能超过100个字符',
        'template_slug.require' => '模板标识不能为空',
        'template_slug.length' => '模板标识长度不能超过100个字符',
        'template_slug.alphaNum' => '模板标识只能包含字母和数字',
        'template_description.length' => '模板描述长度不能超过500个字符',
        'template_preview.length' => '预览图长度不能超过255个字符',
        'thumbnail.length' => '缩略图长度不能超过255个字符',
        'template_category.require' => '模板分类不能为空',
        'template_category.length' => '模板分类长度不能超过50个字符',
        'version.length' => '版本号长度不能超过20个字符',
        'author.length' => '作者长度不能超过50个字符',
        'is_free.integer' => '是否免费必须是整数',
        'is_free.in' => '是否免费值无效',
        'price.float' => '价格必须是数字',
        'price.egt' => '价格必须大于等于0',
        'template_sort_order.integer' => '排序必须是整数',
        'template_sort_order.egt' => '排序必须大于等于0',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        // 页面场景
        'page' => ['title', 'slug', 'description', 'seo_title', 'seo_description', 'seo_keywords', 'template_id', 'layout', 'theme', 'custom_css', 'custom_js', 'status', 'is_home', 'sort_order'],
        'page_create' => ['title', 'slug'],
        'page_update' => ['title', 'slug'],
        'page_content' => ['custom_css', 'custom_js'],
        
        // 组件场景
        'component' => ['name', 'component_slug', 'type', 'category', 'component_description', 'icon', 'preview', 'is_system', 'is_active', 'component_sort_order'],
        'component_create' => ['name', 'component_slug', 'type', 'category'],
        'component_update' => ['name', 'component_slug', 'type', 'category'],
        
        // 模板场景
        'template' => ['template_name', 'template_slug', 'template_description', 'template_preview', 'thumbnail', 'template_category', 'version', 'author', 'is_free', 'price', 'template_sort_order'],
        'template_create' => ['template_name', 'template_slug', 'template_category'],
        'template_update' => ['template_name', 'template_slug', 'template_category'],
    ];

    /**
     * 自定义验证规则：检查slug格式
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkSlug($value, $rule, array $data = [])
    {
        // slug只能包含字母、数字、连字符和下划线
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $value)) {
            return 'slug只能包含字母、数字、连字符和下划线';
        }
        
        // slug不能以连字符或下划线开头或结尾
        if (preg_match('/^[-_]|[-_]$/', $value)) {
            return 'slug不能以连字符或下划线开头或结尾';
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查JSON格式
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkJson($value, $rule, array $data = [])
    {
        if (empty($value)) {
            return true;
        }
        
        if (is_array($value)) {
            return true;
        }
        
        if (is_string($value)) {
            json_decode($value);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return 'JSON格式不正确';
            }
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查CSS代码
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkCss($value, $rule, array $data = [])
    {
        if (empty($value)) {
            return true;
        }
        
        // 简单的CSS安全检查，禁止危险的CSS属性
        $dangerousPatterns = [
            '/javascript:/i',
            '/expression\s*\(/i',
            '/behavior\s*:/i',
            '/@import/i',
            '/url\s*\(\s*["\']?\s*javascript:/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return 'CSS代码包含不安全的内容';
            }
        }
        
        return true;
    }

    /**
     * 自定义验证规则：检查JS代码
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkJs($value, $rule, array $data = [])
    {
        if (empty($value)) {
            return true;
        }
        
        // 简单的JS安全检查，禁止危险的JS代码
        $dangerousPatterns = [
            '/eval\s*\(/i',
            '/document\.write/i',
            '/innerHTML\s*=/i',
            '/outerHTML\s*=/i',
            '/location\s*=/i',
            '/window\.location/i'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return 'JS代码包含不安全的内容';
            }
        }
        
        return true;
    }
}
