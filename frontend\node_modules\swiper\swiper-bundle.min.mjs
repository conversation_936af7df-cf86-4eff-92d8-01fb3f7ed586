/**
 * Swiper 11.2.8
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: May 23, 2025
 */

import{S as Swiper}from"./shared/swiper-core.min.mjs";import Virtual from"./modules/virtual.min.mjs";import Keyboard from"./modules/keyboard.min.mjs";import Mousewheel from"./modules/mousewheel.min.mjs";import Navigation from"./modules/navigation.min.mjs";import Pagination from"./modules/pagination.min.mjs";import Scrollbar from"./modules/scrollbar.min.mjs";import Parallax from"./modules/parallax.min.mjs";import Zoom from"./modules/zoom.min.mjs";import Controller from"./modules/controller.min.mjs";import A11y from"./modules/a11y.min.mjs";import History from"./modules/history.min.mjs";import HashNavigation from"./modules/hash-navigation.min.mjs";import Autoplay from"./modules/autoplay.min.mjs";import Thumb from"./modules/thumbs.min.mjs";import freeMode from"./modules/free-mode.min.mjs";import Grid from"./modules/grid.min.mjs";import Manipulation from"./modules/manipulation.min.mjs";import EffectFade from"./modules/effect-fade.min.mjs";import EffectCube from"./modules/effect-cube.min.mjs";import EffectFlip from"./modules/effect-flip.min.mjs";import EffectCoverflow from"./modules/effect-coverflow.min.mjs";import EffectCreative from"./modules/effect-creative.min.mjs";import EffectCards from"./modules/effect-cards.min.mjs";const modules=[Virtual,Keyboard,Mousewheel,Navigation,Pagination,Scrollbar,Parallax,Zoom,Controller,A11y,History,HashNavigation,Autoplay,Thumb,freeMode,Grid,Manipulation,EffectFade,EffectCube,EffectFlip,EffectCoverflow,EffectCreative,EffectCards];Swiper.use(modules);export{Swiper,Swiper as default};
//# sourceMappingURL=swiper-bundle.min.mjs.map