<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 初始数据插入脚本
 */

echo "=== QiyeDIY初始数据插入 ===\n\n";

// 数据库配置
$config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'username' => 'root',
    'password' => '123456',
    'database' => 'qiyediy',
    'charset' => 'utf8mb4'
];

try {
    echo "1. 连接数据库...\n";
    
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]
    );
    
    echo "✅ 数据库连接成功\n\n";
    
    echo "2. 插入默认角色...\n";
    
    // 插入角色
    $roles = [
        [1, '超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', '["*"]', 0, 1],
        [2, '管理员', 'admin', '系统管理员，拥有大部分管理权限', '["user.*", "role.*", "diy.*", "content.*", "system.view"]', 0, 2],
        [3, '编辑员', 'editor', '内容编辑员，负责内容管理', '["content.*", "diy.view", "diy.create", "diy.update"]', 0, 3],
        [4, '普通用户', 'user', '普通注册用户', '["diy.view", "diy.create", "content.view"]', 1, 4]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO qd_roles (id, name, slug, description, permissions, is_default, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($roles as $role) {
        try {
            $stmt->execute($role);
            echo "   ✅ 角色: {$role[1]}\n";
        } catch (PDOException $e) {
            echo "   ⚠️  角色 {$role[1]} 可能已存在\n";
        }
    }
    
    echo "\n3. 插入默认管理员账号...\n";
    
    // 插入管理员账号 (密码: admin123)
    $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
    
    try {
        $stmt = $pdo->prepare("INSERT INTO qd_users (id, username, email, password, real_name, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([1, 'admin', '<EMAIL>', $passwordHash, '系统管理员', 1]);
        echo "   ✅ 管理员账号创建成功\n";
        
        // 分配超级管理员角色
        $stmt = $pdo->prepare("INSERT INTO qd_user_roles (user_id, role_id) VALUES (?, ?)");
        $stmt->execute([1, 1]);
        echo "   ✅ 超级管理员角色分配成功\n";
        
    } catch (PDOException $e) {
        echo "   ⚠️  管理员账号可能已存在\n";
    }
    
    echo "\n4. 插入系统权限...\n";
    
    // 插入权限
    $permissions = [
        ['查看用户', 'user.view', '查看用户列表和详情', 'user', 'view'],
        ['创建用户', 'user.create', '创建新用户', 'user', 'create'],
        ['更新用户', 'user.update', '更新用户信息', 'user', 'update'],
        ['删除用户', 'user.delete', '删除用户', 'user', 'delete'],
        ['重置密码', 'user.reset_password', '重置用户密码', 'user', 'reset_password'],
        ['分配角色', 'user.assign_roles', '为用户分配角色', 'user', 'assign_roles'],
        ['导出用户', 'user.export', '导出用户数据', 'user', 'export'],
        ['导入用户', 'user.import', '导入用户数据', 'user', 'import'],
        
        ['查看角色', 'role.view', '查看角色列表和详情', 'role', 'view'],
        ['创建角色', 'role.create', '创建新角色', 'role', 'create'],
        ['更新角色', 'role.update', '更新角色信息', 'role', 'update'],
        ['删除角色', 'role.delete', '删除角色', 'role', 'delete'],
        
        ['查看DIY页面', 'diy.view', '查看DIY页面', 'diy', 'view'],
        ['创建DIY页面', 'diy.create', '创建DIY页面', 'diy', 'create'],
        ['更新DIY页面', 'diy.update', '更新DIY页面', 'diy', 'update'],
        ['删除DIY页面', 'diy.delete', '删除DIY页面', 'diy', 'delete'],
        ['发布DIY页面', 'diy.publish', '发布DIY页面', 'diy', 'publish'],
        
        ['查看内容', 'content.view', '查看内容列表', 'content', 'view'],
        ['创建内容', 'content.create', '创建新内容', 'content', 'create'],
        ['更新内容', 'content.update', '更新内容', 'content', 'update'],
        ['删除内容', 'content.delete', '删除内容', 'content', 'delete'],
        ['发布内容', 'content.publish', '发布内容', 'content', 'publish'],
        
        ['查看系统', 'system.view', '查看系统信息', 'system', 'view'],
        ['系统设置', 'system.settings', '修改系统设置', 'system', 'settings'],
        ['查看日志', 'system.logs', '查看系统日志', 'system', 'logs']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO qd_permissions (name, slug, description, module, action) VALUES (?, ?, ?, ?, ?)");
    
    $permissionCount = 0;
    foreach ($permissions as $permission) {
        try {
            $stmt->execute($permission);
            $permissionCount++;
        } catch (PDOException $e) {
            // 权限可能已存在，跳过
        }
    }
    
    echo "   ✅ 插入 {$permissionCount} 个权限\n";
    
    echo "\n5. 插入默认DIY组件...\n";
    
    // 插入DIY组件
    $components = [
        ['文本组件', 'text', 'basic', 'basic', '基础文本显示组件', 'text', '{"editable": true, "styles": ["fontSize", "color", "textAlign"]}', '{"text": "请输入文本内容", "fontSize": "16px", "color": "#333333"}', 1, 1, 1],
        ['图片组件', 'image', 'basic', 'basic', '图片显示组件', 'image', '{"editable": true, "styles": ["width", "height", "borderRadius"]}', '{"src": "", "alt": "图片", "width": "100%"}', 1, 1, 2],
        ['按钮组件', 'button', 'basic', 'basic', '按钮交互组件', 'button', '{"editable": true, "styles": ["backgroundColor", "color", "borderRadius"]}', '{"text": "按钮", "link": "", "backgroundColor": "#007bff", "color": "#ffffff"}', 1, 1, 3],
        ['容器组件', 'container', 'layout', 'layout', '布局容器组件', 'container', '{"editable": true, "styles": ["padding", "margin", "backgroundColor"]}', '{"padding": "20px", "backgroundColor": "#ffffff"}', 1, 1, 4]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO qd_diy_components (name, slug, type, category, description, icon, config, default_props, is_system, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $componentCount = 0;
    foreach ($components as $component) {
        try {
            $stmt->execute($component);
            echo "   ✅ 组件: {$component[0]}\n";
            $componentCount++;
        } catch (PDOException $e) {
            echo "   ⚠️  组件 {$component[0]} 可能已存在\n";
        }
    }
    
    echo "\n6. 插入默认模板...\n";
    
    // 插入模板
    $templates = [
        ['企业官网模板', 'corporate-website', '适合企业官网的专业模板', '{"components": [], "layout": "default"}', 'corporate', 1, 1, 1],
        ['产品展示模板', 'product-showcase', '适合产品展示的现代模板', '{"components": [], "layout": "product"}', 'product', 1, 1, 2],
        ['个人博客模板', 'personal-blog', '适合个人博客的简洁模板', '{"components": [], "layout": "blog"}', 'blog', 1, 1, 3]
    ];
    
    $stmt = $pdo->prepare("INSERT INTO qd_diy_templates (name, slug, description, content, category, is_system, is_free, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    
    $templateCount = 0;
    foreach ($templates as $template) {
        try {
            $stmt->execute($template);
            echo "   ✅ 模板: {$template[0]}\n";
            $templateCount++;
        } catch (PDOException $e) {
            echo "   ⚠️  模板 {$template[0]} 可能已存在\n";
        }
    }
    
    echo "\n7. 验证数据插入结果...\n";
    
    // 验证数据
    $tables = [
        'qd_users' => '用户',
        'qd_roles' => '角色',
        'qd_permissions' => '权限',
        'qd_diy_components' => 'DIY组件',
        'qd_diy_templates' => 'DIY模板'
    ];
    
    foreach ($tables as $table => $name) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
        $count = $stmt->fetch()['count'];
        echo "   ✅ {$name}: {$count} 条记录\n";
    }
    
    echo "\n=== 初始数据插入完成 ===\n";
    echo "🎉 QiyeDIY系统初始数据已成功插入！\n\n";
    
    echo "📋 管理员登录信息:\n";
    echo "   用户名: admin\n";
    echo "   邮箱: <EMAIL>\n";
    echo "   密码: admin123\n\n";
    
    echo "🚀 系统已就绪，可以开始使用！\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
