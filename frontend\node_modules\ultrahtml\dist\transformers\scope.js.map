{"version": 3, "sources": ["../../node_modules/.pnpm/parsel-js@1.1.2/node_modules/parsel-js/dist/parsel.min.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Enum.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Utility.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Tokenizer.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Parser.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Serializer.js", "../../node_modules/.pnpm/stylis@4.3.4/node_modules/stylis/src/Middleware.js", "../../src/transformers/scope.ts"], "sourcesContent": ["const e={attribute:/\\[\\s*(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?(?<name>[-\\w\\P{ASCII}]+)\\s*(?:(?<operator>\\W?=)\\s*(?<value>.+?)\\s*(\\s(?<caseSensitive>[iIsS]))?\\s*)?\\]/gu,id:/#(?<name>[-\\w\\P{ASCII}]+)/gu,class:/\\.(?<name>[-\\w\\P{ASCII}]+)/gu,comma:/\\s*,\\s*/g,combinator:/\\s*[\\s>+~]\\s*/g,\"pseudo-element\":/::(?<name>[-\\w\\P{ASCII}]+)(?:\\((?<argument>¶*)\\))?/gu,\"pseudo-class\":/:(?<name>[-\\w\\P{ASCII}]+)(?:\\((?<argument>¶*)\\))?/gu,universal:/(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?\\*/gu,type:/(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?(?<name>[-\\w\\P{ASCII}]+)/gu},t=new Set([\"combinator\",\"comma\"]),n=new Set([\"not\",\"is\",\"where\",\"has\",\"matches\",\"-moz-any\",\"-webkit-any\",\"nth-child\",\"nth-last-child\"]),s=/(?<index>[\\dn+-]+)\\s+of\\s+(?<subtree>.+)/,o={\"nth-child\":s,\"nth-last-child\":s},r=t=>{switch(t){case\"pseudo-element\":case\"pseudo-class\":return new RegExp(e[t].source.replace(\"(?<argument>¶*)\",\"(?<argument>.*)\"),\"gu\");default:return e[t]}};function c(e,t){let n=0,s=\"\";for(;t<e.length;t++){const o=e[t];switch(o){case\"(\":++n;break;case\")\":--n}if(s+=o,0===n)return s}return s}function i(n,s=e){if(!n)return[];const o=[n];for(const[e,t]of Object.entries(s))for(let n=0;n<o.length;n++){const s=o[n];if(\"string\"!=typeof s)continue;t.lastIndex=0;const r=t.exec(s);if(!r)continue;const c=r.index-1,i=[],a=r[0],l=s.slice(0,c+1);l&&i.push(l),i.push({...r.groups,type:e,content:a});const u=s.slice(c+a.length+1);u&&i.push(u),o.splice(n,1,...i)}let r=0;for(const e of o)switch(typeof e){case\"string\":throw new Error(`Unexpected sequence ${e} found at index ${r}`);case\"object\":r+=e.content.length,e.pos=[r-e.content.length,r],t.has(e.type)&&(e.content=e.content.trim()||\" \")}return o}const a=/(['\"])([^\\\\\\n]+?)\\1/g,l=/\\\\./g;function u(t,n=e){if(\"\"===(t=t.trim()))return[];const s=[];t=(t=t.replace(l,((e,t)=>(s.push({value:e,offset:t}),\"\".repeat(e.length))))).replace(a,((e,t,n,o)=>(s.push({value:e,offset:o}),`${t}${\"\".repeat(n.length)}${t}`)));{let e,n=0;for(;(e=t.indexOf(\"(\",n))>-1;){const o=c(t,e);s.push({value:o,offset:e}),t=`${t.substring(0,e)}(${\"¶\".repeat(o.length-2)})${t.substring(e+o.length)}`,n=e+o.length}}const o=i(t,n),u=new Set;for(const e of s.reverse())for(const t of o){const{offset:n,value:s}=e;if(!(t.pos[0]<=n&&n+s.length<=t.pos[1]))continue;const{content:o}=t,r=n-t.pos[0];t.content=o.slice(0,r)+s+o.slice(r+s.length),t.content!==o&&u.add(t)}for(const e of u){const t=r(e.type);if(!t)throw new Error(`Unknown token type: ${e.type}`);t.lastIndex=0;const n=t.exec(e.content);if(!n)throw new Error(`Unable to parse content for ${e.type}: ${e.content}`);Object.assign(e,n.groups)}return o}function f(e,{list:t=!0}={}){if(t&&e.find((e=>\"comma\"===e.type))){const t=[],n=[];for(let s=0;s<e.length;s++)if(\"comma\"===e[s].type){if(0===n.length)throw new Error(\"Incorrect comma at \"+s);t.push(f(n,{list:!1})),n.length=0}else n.push(e[s]);if(0===n.length)throw new Error(\"Trailing comma\");return t.push(f(n,{list:!1})),{type:\"list\",list:t}}for(let t=e.length-1;t>=0;t--){let n=e[t];if(\"combinator\"===n.type){let s=e.slice(0,t),o=e.slice(t+1);return{type:\"complex\",combinator:n.content,left:f(s),right:f(o)}}}switch(e.length){case 0:throw new Error(\"Could not build AST.\");case 1:return e[0];default:return{type:\"compound\",list:[...e]}}}function*p(e,t){switch(e.type){case\"list\":for(let t of e.list)yield*p(t,e);break;case\"complex\":yield*p(e.left,e),yield*p(e.right,e);break;case\"compound\":yield*e.list.map((t=>[t,e]));break;default:yield[e,t]}}function h(e,t,n){if(e)for(const[s,o]of p(e,n))t(s,o)}function m(e,{recursive:t=!0,list:s=!0}={}){const r=u(e);if(!r)return;const c=f(r,{list:s});if(!t)return c;for(const[e]of p(c)){if(\"pseudo-class\"!==e.type||!e.argument)continue;if(!n.has(e.name))continue;let t=e.argument;const s=o[e.name];if(s){const n=s.exec(t);if(!n)continue;Object.assign(e,n.groups),t=n.groups.subtree}t&&Object.assign(e,{subtree:m(t,{recursive:!0,list:!0})})}return c}function g(e){let t;return t=Array.isArray(e)?e:[...p(e)].map((([e])=>e)),t.map((e=>e.content)).join(\"\")}function d(e,t){return t=t||Math.max(...e)+1,e[0]*(t<<1)+e[1]*t+e[2]}function w(e){let t=e;if(\"string\"==typeof t&&(t=m(t,{recursive:!0})),!t)return[];if(\"list\"===t.type&&\"list\"in t){let e=10;const n=t.list.map((t=>{const n=w(t);return e=Math.max(e,...w(t)),n})),s=n.map((t=>d(t,e)));return n[s.indexOf(Math.max(...s))]}const s=[0,0,0];for(const[e]of p(t))switch(e.type){case\"id\":s[0]++;break;case\"class\":case\"attribute\":s[1]++;break;case\"pseudo-element\":case\"type\":s[2]++;break;case\"pseudo-class\":if(\"where\"===e.name)break;if(!n.has(e.name)||!e.subtree){s[1]++;break}w(e.subtree).forEach(((e,t)=>s[t]+=e)),\"nth-child\"!==e.name&&\"nth-last-child\"!==e.name||s[1]++}return s}export{n as RECURSIVE_PSEUDO_CLASSES,o as RECURSIVE_PSEUDO_CLASSES_ARGS,e as TOKENS,t as TRIM_TOKENS,p as flatten,c as gobbleParens,m as parse,w as specificity,d as specificityToNumber,g as stringify,u as tokenize,i as tokenizeBy,h as walk};\n", "export var MS = '-ms-'\nexport var MOZ = '-moz-'\nexport var WEBKIT = '-webkit-'\n\nexport var COMMENT = 'comm'\nexport var RULESET = 'rule'\nexport var DECLARATION = 'decl'\n\nexport var PAGE = '@page'\nexport var MEDIA = '@media'\nexport var IMPORT = '@import'\nexport var CHARSET = '@charset'\nexport var VIEWPORT = '@viewport'\nexport var SUPPORTS = '@supports'\nexport var DOCUMENT = '@document'\nexport var NAMESPACE = '@namespace'\nexport var KEYFRAMES = '@keyframes'\nexport var FONT_FACE = '@font-face'\nexport var COUNTER_STYLE = '@counter-style'\nexport var FONT_FEATURE_VALUES = '@font-feature-values'\nexport var LAYER = '@layer'\nexport var SCOPE = '@scope'\n", "/**\n * @param {number}\n * @return {number}\n */\nexport var abs = Math.abs\n\n/**\n * @param {number}\n * @return {string}\n */\nexport var from = String.fromCharCode\n\n/**\n * @param {object}\n * @return {object}\n */\nexport var assign = Object.assign\n\n/**\n * @param {string} value\n * @param {number} length\n * @return {number}\n */\nexport function hash (value, length) {\n\treturn charat(value, 0) ^ 45 ? (((((((length << 2) ^ charat(value, 0)) << 2) ^ charat(value, 1)) << 2) ^ charat(value, 2)) << 2) ^ charat(value, 3) : 0\n}\n\n/**\n * @param {string} value\n * @return {string}\n */\nexport function trim (value) {\n\treturn value.trim()\n}\n\n/**\n * @param {string} value\n * @param {RegExp} pattern\n * @return {string?}\n */\nexport function match (value, pattern) {\n\treturn (value = pattern.exec(value)) ? value[0] : value\n}\n\n/**\n * @param {string} value\n * @param {(string|RegExp)} pattern\n * @param {string} replacement\n * @return {string}\n */\nexport function replace (value, pattern, replacement) {\n\treturn value.replace(pattern, replacement)\n}\n\n/**\n * @param {string} value\n * @param {string} search\n * @param {number} position\n * @return {number}\n */\nexport function indexof (value, search, position) {\n\treturn value.indexOf(search, position)\n}\n\n/**\n * @param {string} value\n * @param {number} index\n * @return {number}\n */\nexport function charat (value, index) {\n\treturn value.charCodeAt(index) | 0\n}\n\n/**\n * @param {string} value\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function substr (value, begin, end) {\n\treturn value.slice(begin, end)\n}\n\n/**\n * @param {string} value\n * @return {number}\n */\nexport function strlen (value) {\n\treturn value.length\n}\n\n/**\n * @param {any[]} value\n * @return {number}\n */\nexport function sizeof (value) {\n\treturn value.length\n}\n\n/**\n * @param {any} value\n * @param {any[]} array\n * @return {any}\n */\nexport function append (value, array) {\n\treturn array.push(value), value\n}\n\n/**\n * @param {string[]} array\n * @param {function} callback\n * @return {string}\n */\nexport function combine (array, callback) {\n\treturn array.map(callback).join('')\n}\n\n/**\n * @param {string[]} array\n * @param {RegExp} pattern\n * @return {string[]}\n */\nexport function filter (array, pattern) {\n\treturn array.filter(function (value) { return !match(value, pattern) })\n}\n", "import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {object[]} siblings\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length, siblings) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: '', siblings: siblings}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0, root.siblings), root, {length: -root.length}, props)\n}\n\n/**\n * @param {object} root\n */\nexport function lift (root) {\n\twhile (root.root)\n\t\troot = copy(root.root, {children: [root]})\n\n\tappend(root, root.siblings)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n", "import {COMMENT, RULESET, DECLARATION} from './Enum.js'\nimport {abs, charat, trim, from, sizeof, strlen, substr, append, replace, indexof} from './Utility.js'\nimport {node, char, prev, next, peek, token, caret, alloc, dealloc, delimit, whitespace, escaping, identifier, commenter} from './Tokenizer.js'\n\n/**\n * @param {string} value\n * @return {object[]}\n */\nexport function compile (value) {\n\treturn dealloc(parse('', null, null, null, [''], value = alloc(value), 0, [0], value))\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {string[]} rule\n * @param {string[]} rules\n * @param {string[]} rulesets\n * @param {number[]} pseudo\n * @param {number[]} points\n * @param {string[]} declarations\n * @return {object}\n */\nexport function parse (value, root, parent, rule, rules, rulesets, pseudo, points, declarations) {\n\tvar index = 0\n\tvar offset = 0\n\tvar length = pseudo\n\tvar atrule = 0\n\tvar property = 0\n\tvar previous = 0\n\tvar variable = 1\n\tvar scanning = 1\n\tvar ampersand = 1\n\tvar character = 0\n\tvar type = ''\n\tvar props = rules\n\tvar children = rulesets\n\tvar reference = rule\n\tvar characters = type\n\n\twhile (scanning)\n\t\tswitch (previous = character, character = next()) {\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (previous != 108 && charat(characters, length - 1) == 58) {\n\t\t\t\t\tif (indexof(characters += replace(delimit(character), '&', '&\\f'), '&\\f', abs(index ? points[index - 1] : 0)) != -1)\n\t\t\t\t\t\tampersand = -1\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t// \" ' [\n\t\t\tcase 34: case 39: case 91:\n\t\t\t\tcharacters += delimit(character)\n\t\t\t\tbreak\n\t\t\t// \\t \\n \\r \\s\n\t\t\tcase 9: case 10: case 13: case 32:\n\t\t\t\tcharacters += whitespace(previous)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tcharacters += escaping(caret() - 1, 7)\n\t\t\t\tcontinue\n\t\t\t// /\n\t\t\tcase 47:\n\t\t\t\tswitch (peek()) {\n\t\t\t\t\tcase 42: case 47:\n\t\t\t\t\t\tappend(comment(commenter(next(), caret()), root, parent, declarations), declarations)\n\t\t\t\t\t\tif ((token(previous || 1) == 5 || token(peek() || 1) == 5) && strlen(characters) && substr(characters, -1, void 0) !== ' ') characters += ' '\n\t\t\t\t\t\tbreak\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tcharacters += '/'\n\t\t\t\t}\n\t\t\t\tbreak\n\t\t\t// {\n\t\t\tcase 123 * variable:\n\t\t\t\tpoints[index++] = strlen(characters) * ampersand\n\t\t\t// } ; \\0\n\t\t\tcase 125 * variable: case 59: case 0:\n\t\t\t\tswitch (character) {\n\t\t\t\t\t// \\0 }\n\t\t\t\t\tcase 0: case 125: scanning = 0\n\t\t\t\t\t// ;\n\t\t\t\t\tcase 59 + offset: if (ampersand == -1) characters = replace(characters, /\\f/g, '')\n\t\t\t\t\t\tif (property > 0 && (strlen(characters) - length || (variable === 0 && previous === 47)))\n\t\t\t\t\t\t\tappend(property > 32 ? declaration(characters + ';', rule, parent, length - 1, declarations) : declaration(replace(characters, ' ', '') + ';', rule, parent, length - 2, declarations), declarations)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @ ;\n\t\t\t\t\tcase 59: characters += ';'\n\t\t\t\t\t// { rule/at-rule\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tappend(reference = ruleset(characters, root, parent, index, offset, rules, points, type, props = [], children = [], length, rulesets), rulesets)\n\n\t\t\t\t\t\tif (character === 123)\n\t\t\t\t\t\t\tif (offset === 0)\n\t\t\t\t\t\t\t\tparse(characters, root, reference, reference, props, rulesets, length, points, children)\n\t\t\t\t\t\t\telse\n\t\t\t\t\t\t\t\tswitch (atrule === 99 && charat(characters, 3) === 110 ? 100 : atrule) {\n\t\t\t\t\t\t\t\t\t// d l m s\n\t\t\t\t\t\t\t\t\tcase 100: case 108: case 109: case 115:\n\t\t\t\t\t\t\t\t\t\tparse(value, reference, reference, rule && append(ruleset(value, reference, reference, 0, 0, rules, points, type, rules, props = [], length, children), children), rules, children, length, points, rule ? props : children)\n\t\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\t\tparse(characters, reference, reference, reference, [''], children, 0, points, children)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tindex = offset = property = 0, variable = ampersand = 1, type = characters = '', length = pseudo\n\t\t\t\tbreak\n\t\t\t// :\n\t\t\tcase 58:\n\t\t\t\tlength = 1 + strlen(characters), property = previous\n\t\t\tdefault:\n\t\t\t\tif (variable < 1)\n\t\t\t\t\tif (character == 123)\n\t\t\t\t\t\t--variable\n\t\t\t\t\telse if (character == 125 && variable++ == 0 && prev() == 125)\n\t\t\t\t\t\tcontinue\n\n\t\t\t\tswitch (characters += from(character), character * variable) {\n\t\t\t\t\t// &\n\t\t\t\t\tcase 38:\n\t\t\t\t\t\tampersand = offset > 0 ? 1 : (characters += '\\f', -1)\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// ,\n\t\t\t\t\tcase 44:\n\t\t\t\t\t\tpoints[index++] = (strlen(characters) - 1) * ampersand, ampersand = 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// @\n\t\t\t\t\tcase 64:\n\t\t\t\t\t\t// -\n\t\t\t\t\t\tif (peek() === 45)\n\t\t\t\t\t\t\tcharacters += delimit(next())\n\n\t\t\t\t\t\tatrule = peek(), offset = length = strlen(type = characters += identifier(caret())), character++\n\t\t\t\t\t\tbreak\n\t\t\t\t\t// -\n\t\t\t\t\tcase 45:\n\t\t\t\t\t\tif (previous === 45 && strlen(characters) == 2)\n\t\t\t\t\t\t\tvariable = 0\n\t\t\t\t}\n\t\t}\n\n\treturn rulesets\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} index\n * @param {number} offset\n * @param {string[]} rules\n * @param {number[]} points\n * @param {string} type\n * @param {string[]} props\n * @param {string[]} children\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function ruleset (value, root, parent, index, offset, rules, points, type, props, children, length, siblings) {\n\tvar post = offset - 1\n\tvar rule = offset === 0 ? rules : ['']\n\tvar size = sizeof(rule)\n\n\tfor (var i = 0, j = 0, k = 0; i < index; ++i)\n\t\tfor (var x = 0, y = substr(value, post + 1, post = abs(j = points[i])), z = value; x < size; ++x)\n\t\t\tif (z = trim(j > 0 ? rule[x] + ' ' + y : replace(y, /&\\f/g, rule[x])))\n\t\t\t\tprops[k++] = z\n\n\treturn node(value, root, parent, offset === 0 ? RULESET : type, props, children, length, siblings)\n}\n\n/**\n * @param {number} value\n * @param {object} root\n * @param {object?} parent\n * @param {object[]} siblings\n * @return {object}\n */\nexport function comment (value, root, parent, siblings) {\n\treturn node(value, root, parent, COMMENT, from(char()), substr(value, 2, -2), 0, siblings)\n}\n\n/**\n * @param {string} value\n * @param {object} root\n * @param {object?} parent\n * @param {number} length\n * @param {object[]} siblings\n * @return {object}\n */\nexport function declaration (value, root, parent, length, siblings) {\n\treturn node(value, root, parent, DECLARATION, substr(value, 0, length), substr(value, length + 1, -1), length, siblings)\n}\n", "import {IMPOR<PERSON>, LAYER, COMMENT, RULESET, DECLARATION, KEYFRAMES} from './Enum.js'\nimport {strlen} from './Utility.js'\n\n/**\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function serialize (children, callback) {\n\tvar output = ''\n\n\tfor (var i = 0; i < children.length; i++)\n\t\toutput += callback(children[i], i, children, callback) || ''\n\n\treturn output\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n * @return {string}\n */\nexport function stringify (element, index, children, callback) {\n\tswitch (element.type) {\n\t\tcase LAYER: if (element.children.length) break\n\t\tcase IMPORT: case DECLARATION: return element.return = element.return || element.value\n\t\tcase COMMENT: return ''\n\t\tcase KEYFRAMES: return element.return = element.value + '{' + serialize(element.children, callback) + '}'\n\t\tcase RULESET: if (!strlen(element.value = element.props.join(','))) return ''\n\t}\n\n\treturn strlen(children = serialize(element.children, callback)) ? element.return = element.value + '{' + children + '}' : ''\n}\n", "import {MS, MOZ, WEBKIT, RULESET, KEYFRAMES, DECLARATION} from './Enum.js'\nimport {match, charat, substr, strlen, sizeof, replace, combine, filter, assign} from './Utility.js'\nimport {copy, lift, tokenize} from './Tokenizer.js'\nimport {serialize} from './Serializer.js'\nimport {prefix} from './Prefixer.js'\n\n/**\n * @param {function[]} collection\n * @return {function}\n */\nexport function middleware (collection) {\n\tvar length = sizeof(collection)\n\n\treturn function (element, index, children, callback) {\n\t\tvar output = ''\n\n\t\tfor (var i = 0; i < length; i++)\n\t\t\toutput += collection[i](element, index, children, callback) || ''\n\n\t\treturn output\n\t}\n}\n\n/**\n * @param {function} callback\n * @return {function}\n */\nexport function rulesheet (callback) {\n\treturn function (element) {\n\t\tif (!element.root)\n\t\t\tif (element = element.return)\n\t\t\t\tcallback(element)\n\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n * @param {function} callback\n */\nexport function prefixer (element, index, children, callback) {\n\tif (element.length > -1)\n\t\tif (!element.return)\n\t\t\tswitch (element.type) {\n\t\t\t\tcase DECLARATION: element.return = prefix(element.value, element.length, children)\n\t\t\t\t\treturn\n\t\t\t\tcase KEYFRAMES:\n\t\t\t\t\treturn serialize([copy(element, {value: replace(element.value, '@', '@' + WEBKIT)})], callback)\n\t\t\t\tcase RULESET:\n\t\t\t\t\tif (element.length)\n\t\t\t\t\t\treturn combine(children = element.props, function (value) {\n\t\t\t\t\t\t\tswitch (match(value, callback = /(::plac\\w+|:read-\\w+)/)) {\n\t\t\t\t\t\t\t\t// :read-(only|write)\n\t\t\t\t\t\t\t\tcase ':read-only': case ':read-write':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(read-\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t\t// :placeholder\n\t\t\t\t\t\t\t\tcase '::placeholder':\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + WEBKIT + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, ':' + MOZ + '$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [replace(value, /:(plac\\w+)/, MS + 'input-$1')]}))\n\t\t\t\t\t\t\t\t\tlift(copy(element, {props: [value]}))\n\t\t\t\t\t\t\t\t\tassign(element, {props: filter(children, callback)})\n\t\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\treturn ''\n\t\t\t\t\t\t})\n\t\t\t}\n}\n\n/**\n * @param {object} element\n * @param {number} index\n * @param {object[]} children\n */\nexport function namespace (element) {\n\tswitch (element.type) {\n\t\tcase RULESET:\n\t\t\telement.props = element.props.map(function (value) {\n\t\t\t\treturn combine(tokenize(value), function (value, index, children) {\n\t\t\t\t\tswitch (charat(value, 0)) {\n\t\t\t\t\t\t// \\f\n\t\t\t\t\t\tcase 12:\n\t\t\t\t\t\t\treturn substr(value, 1, strlen(value))\n\t\t\t\t\t\t// \\0 ( + > ~\n\t\t\t\t\t\tcase 0: case 40: case 43: case 62: case 126:\n\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t// :\n\t\t\t\t\t\tcase 58:\n\t\t\t\t\t\t\tif (children[++index] === 'global')\n\t\t\t\t\t\t\t\tchildren[index] = '', children[++index] = '\\f' + substr(children[index], index = 1, -1)\n\t\t\t\t\t\t// \\s\n\t\t\t\t\t\tcase 32:\n\t\t\t\t\t\t\treturn index === 1 ? '' : value\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tswitch (index) {\n\t\t\t\t\t\t\t\tcase 0: element = value\n\t\t\t\t\t\t\t\t\treturn sizeof(children) > 1 ? '' : value\n\t\t\t\t\t\t\t\tcase index = sizeof(children) - 1: case 2:\n\t\t\t\t\t\t\t\t\treturn index === 2 ? value + element + element : value + element\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\treturn value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t})\n\t}\n}\n", "import type { AST } from 'parsel-js';\nimport type { ElementNode, Node } from '../index.js';\n\nimport { parse } from 'parsel-js';\nimport { compile, middleware, serialize, stringify } from 'stylis';\nimport { ELEMENT_NODE, TEXT_NODE, render, walkSync } from '../index.js';\nimport { matches } from '../selector.js';\n\nexport interface ScopeOptions {\n\thash?: string;\n\tattribute?: string;\n}\nexport default function scope(opts: ScopeOptions = {}) {\n\treturn async (doc: Node): Promise<Node> => {\n\t\tconst hash = opts.hash ?? shorthash(await render(doc));\n\t\tconst actions: (() => void)[] = [];\n\t\tlet hasStyle = false;\n\t\tconst selectors = new Set<string>();\n\t\tconst nodes = new Set<ElementNode>();\n\t\twalkSync(doc, (node: Node) => {\n\t\t\tif (node.type === ELEMENT_NODE && node.name === 'style') {\n\t\t\t\tif (!opts.attribute || hasAttribute(node, opts.attribute)) {\n\t\t\t\t\thasStyle = true;\n\t\t\t\t\tif (opts.attribute) {\n\t\t\t\t\t\tdelete node.attributes[opts.attribute];\n\t\t\t\t\t}\n\t\t\t\t\tfor (const selector of getSelectors(node.children[0].value)) {\n\t\t\t\t\t\tselectors.add(selector);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (node.type === ELEMENT_NODE) {\n\t\t\t\tnodes.add(node);\n\t\t\t}\n\t\t});\n\t\tif (hasStyle) {\n\t\t\twalkSync(doc, (node: Node) => {\n\t\t\t\tif (node.type === ELEMENT_NODE) {\n\t\t\t\t\tactions.push(() => scopeElement(node, hash, selectors));\n\t\t\t\t\tif (node.name === 'style') {\n\t\t\t\t\t\tactions.push(() => {\n\t\t\t\t\t\t\tnode.children = node.children.map((c: Node) => {\n\t\t\t\t\t\t\t\tif (c.type !== TEXT_NODE) return c;\n\t\t\t\t\t\t\t\tc.value = scopeCSS(c.value, hash);\n\t\t\t\t\t\t\t\tif (c.value === '') {\n\t\t\t\t\t\t\t\t\tnode.parent.children = node.parent.children.filter(\n\t\t\t\t\t\t\t\t\t\t(s: Node) => s !== node,\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\tfor (const action of actions) {\n\t\t\taction();\n\t\t}\n\n\t\treturn doc;\n\t};\n}\n\nconst NEVER_SCOPED = new Set([\n\t'base',\n\t'font',\n\t'frame',\n\t'frameset',\n\t'head',\n\t'link',\n\t'meta',\n\t'noframes',\n\t'noscript',\n\t'script',\n\t'style',\n\t'title',\n]);\n\nfunction hasAttribute(node: ElementNode, name: string) {\n\tif (name in node.attributes) {\n\t\treturn node.attributes[name] !== 'false';\n\t}\n\treturn false;\n}\n\nfunction scopeElement(node: ElementNode, hash: string, selectors: Set<string>) {\n\tconst { name } = node;\n\tif (!name) return;\n\tif (name.length < 1) return;\n\tif (NEVER_SCOPED.has(name)) return;\n\tif (node.attributes['data-scope']) return;\n\tfor (const selector of selectors) {\n\t\tif (matches(node, selector)) {\n\t\t\tnode.attributes['data-scope'] = hash;\n\t\t\treturn;\n\t\t}\n\t}\n}\n\nfunction scopeSelector(selector: string, hash: string): string {\n\tconst ast = parse(selector);\n\tconst scope = (node: AST): string => {\n\t\tswitch (node.type) {\n\t\t\tcase 'pseudo-class': {\n\t\t\t\tif (node.name === 'root') return node.content;\n\t\t\t\tif (node.name === 'global') return node.argument!;\n\t\t\t\treturn `${node.content}:where([data-scope=\"${hash}\"])`;\n\t\t\t}\n\t\t\tcase 'compound':\n\t\t\t\treturn `${selector}:where([data-scope=\"${hash}\"])`;\n\t\t\tcase 'complex': {\n\t\t\t\tconst { left, right, combinator } = node;\n\t\t\t\treturn `${scope(left)}${combinator}${scope(right)}`;\n\t\t\t}\n\t\t\tcase 'list':\n\t\t\t\treturn node.list.map((s) => scope(s)).join(' ');\n\t\t\tdefault:\n\t\t\t\treturn `${node.content}:where([data-scope=\"${hash}\"])`;\n\t\t}\n\t};\n\treturn scope(ast!);\n}\n\nfunction scopeCSS(css: string, hash: string) {\n\treturn serialize(\n\t\tcompile(css),\n\t\tmiddleware([\n\t\t\t(element) => {\n\t\t\t\tif (element.type === 'rule') {\n\t\t\t\t\tif (Array.isArray(element.props)) {\n\t\t\t\t\t\telement.props = element.props.map((prop) =>\n\t\t\t\t\t\t\tscopeSelector(prop, hash),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\telement.props = scopeSelector(element.props, hash);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tstringify,\n\t\t]),\n\t);\n}\n\nfunction getSelectors(css: string) {\n\tconst selectors = new Set<string>();\n\tserialize(\n\t\tcompile(css),\n\t\tmiddleware([\n\t\t\t(element) => {\n\t\t\t\tif (element.type === 'rule') {\n\t\t\t\t\tif (Array.isArray(element.props)) {\n\t\t\t\t\t\tfor (const p of element.props) {\n\t\t\t\t\t\t\tselectors.add(p);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tselectors.add(element.props);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t]),\n\t);\n\treturn Array.from(selectors);\n}\n\n/**\n * shorthash - https://github.com/bibig/node-shorthash\n *\n * @license\n *\n * (The MIT License)\n *\n * Copyright (c) 2013 Bibig <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person\n * obtaining a copy of this software and associated documentation\n * files (the \"Software\"), to deal in the Software without\n * restriction, including without limitation the rights to use,\n * copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the\n * Software is furnished to do so, subject to the following\n * conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n * OTHER DEALINGS IN THE SOFTWARE.\n */\n\nconst dictionary =\n\t'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXY';\nconst binary = dictionary.length;\n\n// refer to: http://werxltd.com/wp/2010/05/13/javascript-implementation-of-javas-string-hashcode-method/\nfunction bitwise(str: string) {\n\tlet hash = 0;\n\tif (str.length === 0) return hash;\n\tfor (let i = 0; i < str.length; i++) {\n\t\tconst ch = str.charCodeAt(i);\n\t\thash = (hash << 5) - hash + ch;\n\t\thash = hash & hash; // Convert to 32bit integer\n\t}\n\treturn hash;\n}\n\nfunction shorthash(text: string) {\n\tlet num: number;\n\tlet result = '';\n\n\tlet integer = bitwise(text);\n\tconst sign = integer < 0 ? 'Z' : ''; // It it's negative, start with Z, which isn't in the dictionary\n\n\tinteger = Math.abs(integer);\n\n\twhile (integer >= binary) {\n\t\tnum = integer % binary;\n\t\tinteger = Math.floor(integer / binary);\n\t\tresult = dictionary[num] + result;\n\t}\n\n\tif (integer > 0) {\n\t\tresult = dictionary[integer] + result;\n\t}\n\n\treturn sign + result;\n}\n"], "mappings": "AAAA,IAAMA,EAAE,CAAC,UAAU,uJAAuJ,GAAG,8BAA8B,MAAM,+BAA+B,MAAM,WAAW,WAAW,iBAAiB,iBAAiB,uDAAuD,eAAe,sDAAsD,UAAU,8CAA8C,KAAK,mEAAmE,EAAEC,GAAE,IAAI,IAAI,CAAC,aAAa,OAAO,CAAC,EAAEC,GAAE,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,MAAM,UAAU,WAAW,cAAc,YAAY,gBAAgB,CAAC,EAAEC,GAAE,2CAA2CC,GAAE,CAAC,YAAYD,GAAE,iBAAiBA,EAAC,EAAEE,GAAEJ,GAAG,CAAC,OAAOA,EAAE,CAAC,IAAI,iBAAiB,IAAI,eAAe,OAAO,IAAI,OAAOD,EAAEC,CAAC,EAAE,OAAO,QAAQ,qBAAkB,iBAAiB,EAAE,IAAI,EAAE,QAAQ,OAAOD,EAAEC,CAAC,CAAC,CAAC,EAAE,SAASK,GAAEN,EAAEC,EAAE,CAAC,IAAIC,EAAE,EAAEC,EAAE,GAAG,KAAKF,EAAED,EAAE,OAAOC,IAAI,CAAC,IAAMG,EAAEJ,EAAEC,CAAC,EAAE,OAAOG,EAAE,CAAC,IAAI,IAAI,EAAEF,EAAE,MAAM,IAAI,IAAI,EAAEA,CAAC,CAAC,GAAGC,GAAGC,EAAMF,IAAJ,EAAM,OAAOC,CAAC,CAAC,OAAOA,CAAC,CAAC,SAASI,GAAEL,EAAEC,EAAEH,EAAE,CAAC,GAAG,CAACE,EAAE,MAAM,CAAC,EAAE,IAAME,EAAE,CAACF,CAAC,EAAE,OAAS,CAACF,EAAEC,CAAC,IAAI,OAAO,QAAQE,CAAC,EAAE,QAAQD,EAAE,EAAEA,EAAEE,EAAE,OAAOF,IAAI,CAAC,IAAMC,EAAEC,EAAEF,CAAC,EAAE,GAAa,OAAOC,GAAjB,SAAmB,SAASF,EAAE,UAAU,EAAE,IAAMI,EAAEJ,EAAE,KAAKE,CAAC,EAAE,GAAG,CAACE,EAAE,SAAS,IAAMC,EAAED,EAAE,MAAM,EAAEE,EAAE,CAAC,EAAEC,EAAEH,EAAE,CAAC,EAAEI,EAAEN,EAAE,MAAM,EAAEG,EAAE,CAAC,EAAEG,GAAGF,EAAE,KAAKE,CAAC,EAAEF,EAAE,KAAK,CAAC,GAAGF,EAAE,OAAO,KAAKL,EAAE,QAAQQ,CAAC,CAAC,EAAE,IAAME,EAAEP,EAAE,MAAMG,EAAEE,EAAE,OAAO,CAAC,EAAEE,GAAGH,EAAE,KAAKG,CAAC,EAAEN,EAAE,OAAOF,EAAE,EAAE,GAAGK,CAAC,CAAC,CAAC,IAAIF,EAAE,EAAE,QAAUL,KAAKI,EAAE,OAAO,OAAOJ,EAAE,CAAC,IAAI,SAAS,MAAM,IAAI,MAAM,uBAAuBA,CAAC,mBAAmBK,CAAC,EAAE,EAAE,IAAI,SAASA,GAAGL,EAAE,QAAQ,OAAOA,EAAE,IAAI,CAACK,EAAEL,EAAE,QAAQ,OAAOK,CAAC,EAAEJ,GAAE,IAAID,EAAE,IAAI,IAAIA,EAAE,QAAQA,EAAE,QAAQ,KAAK,GAAG,IAAI,CAAC,OAAOI,CAAC,CAAC,IAAMI,GAAE,uBAAuBC,GAAE,OAAO,SAASC,GAAET,EAAEC,EAAEF,EAAE,CAAC,IAASC,EAAEA,EAAE,KAAK,KAAf,GAAkB,MAAM,CAAC,EAAE,IAAME,EAAE,CAAC,EAAEF,GAAGA,EAAEA,EAAE,QAAQQ,GAAG,CAACT,EAAEC,KAAKE,EAAE,KAAK,CAAC,MAAMH,EAAE,OAAOC,CAAC,CAAC,EAAE,SAAI,OAAOD,EAAE,MAAM,EAAG,GAAG,QAAQQ,GAAG,CAACR,EAAEC,EAAEC,EAAEE,KAAKD,EAAE,KAAK,CAAC,MAAMH,EAAE,OAAOI,CAAC,CAAC,EAAE,GAAGH,CAAC,GAAG,SAAI,OAAOC,EAAE,MAAM,CAAC,GAAGD,CAAC,GAAI,EAAE,CAAC,IAAID,EAAEE,EAAE,EAAE,MAAMF,EAAEC,EAAE,QAAQ,IAAIC,CAAC,GAAG,IAAI,CAAC,IAAM,EAAEI,GAAEL,EAAED,CAAC,EAAEG,EAAE,KAAK,CAAC,MAAM,EAAE,OAAOH,CAAC,CAAC,EAAEC,EAAE,GAAGA,EAAE,UAAU,EAAED,CAAC,CAAC,IAAI,OAAI,OAAO,EAAE,OAAO,CAAC,CAAC,IAAIC,EAAE,UAAUD,EAAE,EAAE,MAAM,CAAC,GAAGE,EAAEF,EAAE,EAAE,MAAM,CAAC,CAAC,IAAMI,EAAEG,GAAEN,EAAEC,CAAC,EAAEQ,EAAE,IAAI,IAAI,QAAUV,KAAKG,EAAE,QAAQ,EAAE,QAAUF,KAAKG,EAAE,CAAC,GAAK,CAAC,OAAOF,EAAE,MAAMC,CAAC,EAAEH,EAAE,GAAG,EAAEC,EAAE,IAAI,CAAC,GAAGC,GAAGA,EAAEC,EAAE,QAAQF,EAAE,IAAI,CAAC,GAAG,SAAS,GAAK,CAAC,QAAQG,CAAC,EAAEH,EAAEI,EAAEH,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,QAAQG,EAAE,MAAM,EAAEC,CAAC,EAAEF,EAAEC,EAAE,MAAMC,EAAEF,EAAE,MAAM,EAAEF,EAAE,UAAUG,GAAGM,EAAE,IAAIT,CAAC,CAAC,CAAC,QAAUD,KAAKU,EAAE,CAAC,IAAMT,EAAEI,GAAEL,EAAE,IAAI,EAAE,GAAG,CAACC,EAAE,MAAM,IAAI,MAAM,uBAAuBD,EAAE,IAAI,EAAE,EAAEC,EAAE,UAAU,EAAE,IAAMC,EAAED,EAAE,KAAKD,EAAE,OAAO,EAAE,GAAG,CAACE,EAAE,MAAM,IAAI,MAAM,+BAA+BF,EAAE,IAAI,KAAKA,EAAE,OAAO,EAAE,EAAE,OAAO,OAAOA,EAAEE,EAAE,MAAM,CAAC,CAAC,OAAOE,CAAC,CAAC,SAASO,EAAEX,EAAE,CAAC,KAAKC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAGA,GAAGD,EAAE,KAAMA,GAAaA,EAAE,OAAZ,OAAiB,EAAE,CAAC,IAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAEA,EAAE,OAAO,IAAI,GAAaA,EAAE,CAAC,EAAE,OAAf,QAAoB,CAAC,GAAO,EAAE,SAAN,EAAa,MAAM,IAAI,MAAM,sBAAsB,CAAC,EAAE,EAAE,KAAKW,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,KAAKX,EAAE,CAAC,CAAC,EAAE,GAAO,EAAE,SAAN,EAAa,MAAM,IAAI,MAAM,gBAAgB,EAAE,OAAO,EAAE,KAAKW,EAAE,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAEX,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,EAAEA,EAAE,CAAC,EAAE,GAAkB,EAAE,OAAjB,aAAsB,CAAC,IAAI,EAAEA,EAAE,MAAM,EAAE,CAAC,EAAEI,EAAEJ,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,UAAU,WAAW,EAAE,QAAQ,KAAKW,EAAE,CAAC,EAAE,MAAMA,EAAEP,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOJ,EAAE,OAAO,CAAC,IAAK,GAAE,MAAM,IAAI,MAAM,sBAAsB,EAAE,IAAK,GAAE,OAAOA,EAAE,CAAC,EAAE,QAAQ,MAAM,CAAC,KAAK,WAAW,KAAK,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,SAASY,EAAEZ,EAAEC,EAAE,CAAC,OAAOD,EAAE,KAAK,CAAC,IAAI,OAAO,QAAQ,KAAKA,EAAE,KAAK,MAAMY,EAAE,EAAEZ,CAAC,EAAE,MAAM,IAAI,UAAU,MAAMY,EAAEZ,EAAE,KAAKA,CAAC,EAAE,MAAMY,EAAEZ,EAAE,MAAMA,CAAC,EAAE,MAAM,IAAI,WAAW,MAAMA,EAAE,KAAK,IAAK,GAAG,CAAC,EAAEA,CAAC,CAAE,EAAE,MAAM,QAAQ,KAAK,CAACA,EAAEC,CAAC,CAAC,CAAC,CAAuD,SAASY,EAAEC,EAAE,CAAC,UAAUC,EAAE,GAAG,KAAKC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAMC,EAAEC,GAAEJ,CAAC,EAAE,GAAG,CAACG,EAAE,OAAO,IAAME,EAAEC,EAAEH,EAAE,CAAC,KAAKD,CAAC,CAAC,EAAE,GAAG,CAACD,EAAE,OAAOI,EAAE,OAAS,CAACL,CAAC,IAAIO,EAAEF,CAAC,EAAE,CAAkD,GAA7BL,EAAE,OAAnB,gBAAyB,CAACA,EAAE,UAAqB,CAACQ,GAAE,IAAIR,EAAE,IAAI,EAAE,SAAS,IAAIC,EAAED,EAAE,SAAeE,EAAEO,GAAET,EAAE,IAAI,EAAE,GAAGE,EAAE,CAAC,IAAMM,EAAEN,EAAE,KAAKD,CAAC,EAAE,GAAG,CAACO,EAAE,SAAS,OAAO,OAAOR,EAAEQ,EAAE,MAAM,EAAEP,EAAEO,EAAE,OAAO,OAAO,CAACP,GAAG,OAAO,OAAOD,EAAE,CAAC,QAAQD,EAAEE,EAAE,CAAC,UAAU,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOI,CAAC,CCItyH,IAAIK,EAAU,OACVC,EAAU,OACVC,EAAc,OAIlB,IAAIC,GAAS,UAMb,IAAIC,GAAY,aAIhB,IAAIC,GAAQ,SChBZ,IAAIC,EAAM,KAAK,IAMXC,EAAO,OAAO,aAqBlB,SAASC,EAAMC,EAAO,CAC5B,OAAOA,EAAM,KAAK,CACnB,CAiBO,SAASC,EAASC,EAAOC,EAASC,EAAa,CACrD,OAAOF,EAAM,QAAQC,EAASC,CAAW,CAC1C,CAQO,SAASC,GAASH,EAAOI,EAAQC,EAAU,CACjD,OAAOL,EAAM,QAAQI,EAAQC,CAAQ,CACtC,CAOO,SAASC,EAAQN,EAAOO,EAAO,CACrC,OAAOP,EAAM,WAAWO,CAAK,EAAI,CAClC,CAQO,SAASC,EAAQR,EAAOS,EAAOC,EAAK,CAC1C,OAAOV,EAAM,MAAMS,EAAOC,CAAG,CAC9B,CAMO,SAASC,EAAQX,EAAO,CAC9B,OAAOA,EAAM,MACd,CAMO,SAASY,EAAQZ,EAAO,CAC9B,OAAOA,EAAM,MACd,CAOO,SAASa,EAAQb,EAAOc,EAAO,CACrC,OAAOA,EAAM,KAAKd,CAAK,EAAGA,CAC3B,CCxGO,IAAIe,EAAO,EACPC,EAAS,EACTC,GAAS,EACTC,EAAW,EACXC,EAAY,EACZC,EAAa,GAYjB,SAASC,EAAMC,EAAOC,EAAMC,EAAQC,EAAMC,EAAOC,EAAUV,EAAQW,EAAU,CACnF,MAAO,CAAC,MAAON,EAAO,KAAMC,EAAM,OAAQC,EAAQ,KAAMC,EAAM,MAAOC,EAAO,SAAUC,EAAU,KAAMZ,EAAM,OAAQC,EAAQ,OAAQC,EAAQ,OAAQ,GAAI,SAAUW,CAAQ,CAC3K,CAwBO,SAASC,IAAQ,CACvB,OAAOC,CACR,CAKO,SAASC,IAAQ,CACvB,OAAAD,EAAYE,EAAW,EAAIC,EAAOC,EAAY,EAAEF,CAAQ,EAAI,EAExDG,IAAUL,IAAc,KAC3BK,EAAS,EAAGC,KAENN,CACR,CAKO,SAASO,GAAQ,CACvB,OAAAP,EAAYE,EAAWM,GAASL,EAAOC,EAAYF,GAAU,EAAI,EAE7DG,IAAUL,IAAc,KAC3BK,EAAS,EAAGC,KAENN,CACR,CAKO,SAASS,GAAQ,CACvB,OAAON,EAAOC,EAAYF,CAAQ,CACnC,CAKO,SAASQ,GAAS,CACxB,OAAOR,CACR,CAOO,SAASS,EAAOC,EAAOC,EAAK,CAClC,OAAOC,EAAOV,EAAYQ,EAAOC,CAAG,CACrC,CAMO,SAASE,EAAOC,EAAM,CAC5B,OAAQA,EAAM,CAEb,IAAK,GAAG,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IACtC,MAAO,GAER,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,KAE3D,IAAK,IAAI,IAAK,KAAK,IAAK,KACvB,MAAO,GAER,IAAK,IACJ,MAAO,GAER,IAAK,IAAI,IAAK,IAAI,IAAK,IAAI,IAAK,IAC/B,MAAO,GAER,IAAK,IAAI,IAAK,IACb,MAAO,EACT,CAEA,MAAO,EACR,CAMO,SAASC,GAAOC,EAAO,CAC7B,OAAOZ,EAAOD,EAAS,EAAGG,GAASW,EAAOf,EAAac,CAAK,EAAGhB,EAAW,EAAG,CAAC,CAC/E,CAMO,SAASkB,GAASF,EAAO,CAC/B,OAAOd,EAAa,GAAIc,CACzB,CAMO,SAASG,EAASL,EAAM,CAC9B,OAAOM,EAAKX,EAAMT,EAAW,EAAGqB,GAAUP,IAAS,GAAKA,EAAO,EAAIA,IAAS,GAAKA,EAAO,EAAIA,CAAI,CAAC,CAAC,CACnG,CAcO,SAASQ,GAAYC,EAAM,CACjC,MAAOC,EAAYC,EAAK,IACnBD,EAAY,IACfE,EAAK,EAIP,OAAOC,EAAMJ,CAAI,EAAI,GAAKI,EAAMH,CAAS,EAAI,EAAI,GAAK,GACvD,CAwBO,SAASI,GAAUC,EAAOC,EAAO,CACvC,KAAO,EAAEA,GAASC,EAAK,GAElB,EAAAC,EAAY,IAAMA,EAAY,KAAQA,EAAY,IAAMA,EAAY,IAAQA,EAAY,IAAMA,EAAY,KAA9G,CAGD,OAAOC,EAAMJ,EAAOK,EAAM,GAAKJ,EAAQ,GAAKK,EAAK,GAAK,IAAMJ,EAAK,GAAK,GAAG,CAC1E,CAMO,SAASK,GAAWC,EAAM,CAChC,KAAON,EAAK,GACX,OAAQC,EAAW,CAElB,KAAKK,EACJ,OAAOC,EAER,IAAK,IAAI,IAAK,IACTD,IAAS,IAAMA,IAAS,IAC3BD,GAAUJ,CAAS,EACpB,MAED,IAAK,IACAK,IAAS,IACZD,GAAUC,CAAI,EACf,MAED,IAAK,IACJN,EAAK,EACL,KACF,CAED,OAAOO,CACR,CAOO,SAASC,GAAWF,EAAMR,EAAO,CACvC,KAAOE,EAAK,GAEPM,EAAOL,IAAc,IAGpB,GAAIK,EAAOL,IAAc,IAAWG,EAAK,IAAM,GACnD,MAEF,MAAO,KAAOF,EAAMJ,EAAOS,EAAW,CAAC,EAAI,IAAME,EAAKH,IAAS,GAAKA,EAAON,EAAK,CAAC,CAClF,CAMO,SAASU,GAAYZ,EAAO,CAClC,KAAO,CAACa,EAAMP,EAAK,CAAC,GACnBJ,EAAK,EAEN,OAAOE,EAAMJ,EAAOS,CAAQ,CAC7B,CCxPO,SAASK,GAASC,EAAO,CAC/B,OAAOC,GAAQC,EAAM,GAAI,KAAM,KAAM,KAAM,CAAC,EAAE,EAAGF,EAAQG,GAAMH,CAAK,EAAG,EAAG,CAAC,CAAC,EAAGA,CAAK,CAAC,CACtF,CAcO,SAASE,EAAOF,EAAOI,EAAMC,EAAQC,EAAMC,EAAOC,EAAUC,EAAQC,EAAQC,EAAc,CAiBhG,QAhBIC,EAAQ,EACRC,EAAS,EACTC,EAASL,EACTM,EAAS,EACTC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAY,EACZC,EAAY,EACZC,EAAO,GACPC,EAAQhB,EACRiB,EAAWhB,EACXiB,EAAYnB,EACZoB,EAAaJ,EAEVH,GACN,OAAQF,EAAWI,EAAWA,EAAYM,EAAK,EAAG,CAEjD,IAAK,IACJ,GAAIV,GAAY,KAAOW,EAAOF,EAAYZ,EAAS,CAAC,GAAK,GAAI,CACxDe,GAAQH,GAAcI,EAAQC,EAAQV,CAAS,EAAG,IAAK,KAAK,EAAG,MAAOW,EAAIpB,EAAQF,EAAOE,EAAQ,CAAC,EAAI,CAAC,CAAC,GAAK,KAChHQ,EAAY,IACb,KACD,CAED,IAAK,IAAI,IAAK,IAAI,IAAK,IACtBM,GAAcK,EAAQV,CAAS,EAC/B,MAED,IAAK,GAAG,IAAK,IAAI,IAAK,IAAI,IAAK,IAC9BK,GAAcO,GAAWhB,CAAQ,EACjC,MAED,IAAK,IACJS,GAAcQ,GAASC,EAAM,EAAI,EAAG,CAAC,EACrC,SAED,IAAK,IACJ,OAAQC,EAAK,EAAG,CACf,IAAK,IAAI,IAAK,IACbC,EAAOC,GAAQC,GAAUZ,EAAK,EAAGQ,EAAM,CAAC,EAAG/B,EAAMC,EAAQM,CAAY,EAAGA,CAAY,GAC/E6B,EAAMvB,GAAY,CAAC,GAAK,GAAKuB,EAAMJ,EAAK,GAAK,CAAC,GAAK,IAAMK,EAAOf,CAAU,GAAKgB,EAAOhB,EAAY,GAAI,MAAM,IAAM,MAAKA,GAAc,KAC1I,MACD,QACCA,GAAc,GAChB,CACA,MAED,IAAK,KAAMR,EACVR,EAAOE,GAAO,EAAI6B,EAAOf,CAAU,EAAIN,EAExC,IAAK,KAAMF,EAAU,IAAK,IAAI,IAAK,GAClC,OAAQG,EAAW,CAElB,IAAK,GAAG,IAAK,KAAKF,EAAW,EAE7B,IAAK,IAAKN,EAAYO,GAAa,KAAIM,EAAaI,EAAQJ,EAAY,MAAO,EAAE,GAC5EV,EAAW,IAAMyB,EAAOf,CAAU,EAAIZ,GAAWI,IAAa,GAAKD,IAAa,KACnFoB,EAAOrB,EAAW,GAAK2B,GAAYjB,EAAa,IAAKpB,EAAMD,EAAQS,EAAS,EAAGH,CAAY,EAAIgC,GAAYb,EAAQJ,EAAY,IAAK,EAAE,EAAI,IAAKpB,EAAMD,EAAQS,EAAS,EAAGH,CAAY,EAAGA,CAAY,EACrM,MAED,IAAK,IAAIe,GAAc,IAEvB,QAGC,GAFAW,EAAOZ,EAAYmB,GAAQlB,EAAYtB,EAAMC,EAAQO,EAAOC,EAAQN,EAAOG,EAAQY,EAAMC,EAAQ,CAAC,EAAGC,EAAW,CAAC,EAAGV,EAAQN,CAAQ,EAAGA,CAAQ,EAE3Ia,IAAc,IACjB,GAAIR,IAAW,EACdX,EAAMwB,EAAYtB,EAAMqB,EAAWA,EAAWF,EAAOf,EAAUM,EAAQJ,EAAQc,CAAQ,MAEvF,QAAQT,IAAW,IAAMa,EAAOF,EAAY,CAAC,IAAM,IAAM,IAAMX,EAAQ,CAEtE,IAAK,KAAK,IAAK,KAAK,IAAK,KAAK,IAAK,KAClCb,EAAMF,EAAOyB,EAAWA,EAAWnB,GAAQ+B,EAAOO,GAAQ5C,EAAOyB,EAAWA,EAAW,EAAG,EAAGlB,EAAOG,EAAQY,EAAMf,EAAOgB,EAAQ,CAAC,EAAGT,EAAQU,CAAQ,EAAGA,CAAQ,EAAGjB,EAAOiB,EAAUV,EAAQJ,EAAQJ,EAAOiB,EAAQC,CAAQ,EAC3N,MACD,QACCtB,EAAMwB,EAAYD,EAAWA,EAAWA,EAAW,CAAC,EAAE,EAAGD,EAAU,EAAGd,EAAQc,CAAQ,CACxF,CACJ,CAEAZ,EAAQC,EAASG,EAAW,EAAGE,EAAWE,EAAY,EAAGE,EAAOI,EAAa,GAAIZ,EAASL,EAC1F,MAED,IAAK,IACJK,EAAS,EAAI2B,EAAOf,CAAU,EAAGV,EAAWC,EAC7C,QACC,GAAIC,EAAW,GACd,GAAIG,GAAa,IAChB,EAAEH,UACMG,GAAa,KAAOH,KAAc,GAAK2B,GAAK,GAAK,IACzD,SAEF,OAAQnB,GAAcoB,EAAKzB,CAAS,EAAGA,EAAYH,EAAU,CAE5D,IAAK,IACJE,EAAYP,EAAS,EAAI,GAAKa,GAAc,KAAM,IAClD,MAED,IAAK,IACJhB,EAAOE,GAAO,GAAK6B,EAAOf,CAAU,EAAI,GAAKN,EAAWA,EAAY,EACpE,MAED,IAAK,IAEAgB,EAAK,IAAM,KACdV,GAAcK,EAAQJ,EAAK,CAAC,GAE7BZ,EAASqB,EAAK,EAAGvB,EAASC,EAAS2B,EAAOnB,EAAOI,GAAcqB,GAAWZ,EAAM,CAAC,CAAC,EAAGd,IACrF,MAED,IAAK,IACAJ,IAAa,IAAMwB,EAAOf,CAAU,GAAK,IAC5CR,EAAW,EACd,CACF,CAED,OAAOV,CACR,CAiBO,SAASoC,GAAS5C,EAAOI,EAAMC,EAAQO,EAAOC,EAAQN,EAAOG,EAAQY,EAAMC,EAAOC,EAAUV,EAAQkC,EAAU,CAKpH,QAJIC,EAAOpC,EAAS,EAChBP,EAAOO,IAAW,EAAIN,EAAQ,CAAC,EAAE,EACjC2C,EAAOC,EAAO7C,CAAI,EAEb8C,EAAI,EAAGC,EAAI,EAAGC,EAAI,EAAGF,EAAIxC,EAAO,EAAEwC,EAC1C,QAASG,EAAI,EAAGC,EAAId,EAAO1C,EAAOiD,EAAO,EAAGA,EAAOjB,EAAIqB,EAAI3C,EAAO0C,CAAC,CAAC,CAAC,EAAGK,EAAIzD,EAAOuD,EAAIL,EAAM,EAAEK,GAC1FE,EAAIC,EAAKL,EAAI,EAAI/C,EAAKiD,CAAC,EAAI,IAAMC,EAAI1B,EAAQ0B,EAAG,OAAQlD,EAAKiD,CAAC,CAAC,CAAC,KACnEhC,EAAM+B,GAAG,EAAIG,GAEhB,OAAOE,EAAK3D,EAAOI,EAAMC,EAAQQ,IAAW,EAAI+C,EAAUtC,EAAMC,EAAOC,EAAUV,EAAQkC,CAAQ,CAClG,CASO,SAASV,GAAStC,EAAOI,EAAMC,EAAQ2C,EAAU,CACvD,OAAOW,EAAK3D,EAAOI,EAAMC,EAAQwD,EAASf,EAAKgB,GAAK,CAAC,EAAGpB,EAAO1C,EAAO,EAAG,EAAE,EAAG,EAAGgD,CAAQ,CAC1F,CAUO,SAASL,GAAa3C,EAAOI,EAAMC,EAAQS,EAAQkC,EAAU,CACnE,OAAOW,EAAK3D,EAAOI,EAAMC,EAAQ0D,EAAarB,EAAO1C,EAAO,EAAGc,CAAM,EAAG4B,EAAO1C,EAAOc,EAAS,EAAG,EAAE,EAAGA,EAAQkC,CAAQ,CACxH,CC1LO,SAASgB,EAAWC,EAAUC,EAAU,CAG9C,QAFIC,EAAS,GAEJC,EAAI,EAAGA,EAAIH,EAAS,OAAQG,IACpCD,GAAUD,EAASD,EAASG,CAAC,EAAGA,EAAGH,EAAUC,CAAQ,GAAK,GAE3D,OAAOC,CACR,CASO,SAASE,GAAWC,EAASC,EAAON,EAAUC,EAAU,CAC9D,OAAQI,EAAQ,KAAM,CACrB,KAAKE,GAAO,GAAIF,EAAQ,SAAS,OAAQ,MACzC,KAAKG,GAAQ,KAAKC,EAAa,OAAOJ,EAAQ,OAASA,EAAQ,QAAUA,EAAQ,MACjF,KAAKK,EAAS,MAAO,GACrB,KAAKC,GAAW,OAAON,EAAQ,OAASA,EAAQ,MAAQ,IAAMN,EAAUM,EAAQ,SAAUJ,CAAQ,EAAI,IACtG,KAAKW,EAAS,GAAI,CAACC,EAAOR,EAAQ,MAAQA,EAAQ,MAAM,KAAK,GAAG,CAAC,EAAG,MAAO,EAC5E,CAEA,OAAOQ,EAAOb,EAAWD,EAAUM,EAAQ,SAAUJ,CAAQ,CAAC,EAAII,EAAQ,OAASA,EAAQ,MAAQ,IAAML,EAAW,IAAM,EAC3H,CCxBO,SAASc,GAAYC,EAAY,CACvC,IAAIC,EAASC,EAAOF,CAAU,EAE9B,OAAO,SAAUG,EAASC,EAAOC,EAAUC,EAAU,CAGpD,QAFIC,EAAS,GAEJC,EAAI,EAAGA,EAAIP,EAAQO,IAC3BD,GAAUP,EAAWQ,CAAC,EAAEL,EAASC,EAAOC,EAAUC,CAAQ,GAAK,GAEhE,OAAOC,CACR,CACD,CChBA,OAAS,gBAAAE,GAAc,aAAAC,GAAW,UAAAC,GAAQ,YAAAC,OAAgB,cAC1D,OAAS,WAAAC,OAAe,iBAMT,SAARC,GAAuBC,EAAqB,CAAC,EAAG,CACtD,MAAO,OAAOC,GAA6B,CAC1C,IAAMC,EAAOF,EAAK,MAAQG,GAAU,MAAMP,GAAOK,CAAG,CAAC,EAC/CG,EAA0B,CAAC,EAC7BC,EAAW,GACTC,EAAY,IAAI,IAChBC,EAAQ,IAAI,IAClBV,GAASI,EAAMO,GAAe,CAC7B,GAAIA,EAAK,OAASd,IAAgBc,EAAK,OAAS,UAC3C,CAACR,EAAK,WAAaS,GAAaD,EAAMR,EAAK,SAAS,GAAG,CAC1DK,EAAW,GACPL,EAAK,WACR,OAAOQ,EAAK,WAAWR,EAAK,SAAS,EAEtC,QAAWU,KAAYC,GAAaH,EAAK,SAAS,CAAC,EAAE,KAAK,EACzDF,EAAU,IAAII,CAAQ,CAExB,CAEGF,EAAK,OAASd,IACjBa,EAAM,IAAIC,CAAI,CAEhB,CAAC,EACGH,GACHR,GAASI,EAAMO,GAAe,CACzBA,EAAK,OAASd,KACjBU,EAAQ,KAAK,IAAMQ,GAAaJ,EAAMN,EAAMI,CAAS,CAAC,EAClDE,EAAK,OAAS,SACjBJ,EAAQ,KAAK,IAAM,CAClBI,EAAK,SAAWA,EAAK,SAAS,IAAK,IAC9B,EAAE,OAASb,KACf,EAAE,MAAQkB,GAAS,EAAE,MAAOX,CAAI,EAC5B,EAAE,QAAU,KACfM,EAAK,OAAO,SAAWA,EAAK,OAAO,SAAS,OAC1CM,GAAYA,IAAMN,CACpB,IAEM,EACP,CACF,CAAC,EAGJ,CAAC,EAEF,QAAWO,KAAUX,EACpBW,EAAO,EAGR,OAAOd,CACR,CACD,CAEA,IAAMe,GAAe,IAAI,IAAI,CAC5B,OACA,OACA,QACA,WACA,OACA,OACA,OACA,WACA,WACA,SACA,QACA,OACD,CAAC,EAED,SAASP,GAAaD,EAAmBS,EAAc,CACtD,OAAIA,KAAQT,EAAK,WACTA,EAAK,WAAWS,CAAI,IAAM,QAE3B,EACR,CAEA,SAASL,GAAaJ,EAAmBN,EAAcI,EAAwB,CAC9E,GAAM,CAAE,KAAAW,CAAK,EAAIT,EACjB,GAAKS,GACD,EAAAA,EAAK,OAAS,IACd,CAAAD,GAAa,IAAIC,CAAI,GACrB,CAAAT,EAAK,WAAW,YAAY,GAChC,QAAWE,KAAYJ,EACtB,GAAIR,GAAQU,EAAME,CAAQ,EAAG,CAC5BF,EAAK,WAAW,YAAY,EAAIN,EAChC,MACD,EAEF,CAEA,SAASgB,GAAcR,EAAkBR,EAAsB,CAC9D,IAAMiB,EAAMC,EAAMV,CAAQ,EACpBX,EAASS,GAAsB,CACpC,OAAQA,EAAK,KAAM,CAClB,IAAK,eACJ,OAAIA,EAAK,OAAS,OAAeA,EAAK,QAClCA,EAAK,OAAS,SAAiBA,EAAK,SACjC,GAAGA,EAAK,OAAO,uBAAuBN,CAAI,MAElD,IAAK,WACJ,MAAO,GAAGQ,CAAQ,uBAAuBR,CAAI,MAC9C,IAAK,UAAW,CACf,GAAM,CAAE,KAAAmB,EAAM,MAAAC,EAAO,WAAAC,CAAW,EAAIf,EACpC,MAAO,GAAGT,EAAMsB,CAAI,CAAC,GAAGE,CAAU,GAAGxB,EAAMuB,CAAK,CAAC,EAClD,CACA,IAAK,OACJ,OAAOd,EAAK,KAAK,IAAKM,GAAMf,EAAMe,CAAC,CAAC,EAAE,KAAK,GAAG,EAC/C,QACC,MAAO,GAAGN,EAAK,OAAO,uBAAuBN,CAAI,KACnD,CACD,EACA,OAAOH,EAAMoB,CAAI,CAClB,CAEA,SAASN,GAASW,EAAatB,EAAc,CAC5C,OAAOuB,EACNC,GAAQF,CAAG,EACXG,GAAW,CACTC,GAAY,CACRA,EAAQ,OAAS,SAChB,MAAM,QAAQA,EAAQ,KAAK,EAC9BA,EAAQ,MAAQA,EAAQ,MAAM,IAAKC,GAClCX,GAAcW,EAAM3B,CAAI,CACzB,EAEA0B,EAAQ,MAAQV,GAAcU,EAAQ,MAAO1B,CAAI,EAGpD,EACA4B,EACD,CAAC,CACF,CACD,CAEA,SAASnB,GAAaa,EAAa,CAClC,IAAMlB,EAAY,IAAI,IACtB,OAAAmB,EACCC,GAAQF,CAAG,EACXG,GAAW,CACTC,GAAY,CACZ,GAAIA,EAAQ,OAAS,OACpB,GAAI,MAAM,QAAQA,EAAQ,KAAK,EAC9B,QAAWG,KAAKH,EAAQ,MACvBtB,EAAU,IAAIyB,CAAC,OAGhBzB,EAAU,IAAIsB,EAAQ,KAAK,CAG9B,CACD,CAAC,CACF,EACO,MAAM,KAAKtB,CAAS,CAC5B,CAiCA,IAAM0B,GACL,gEACKC,GAASD,GAAW,OAG1B,SAASE,GAAQC,EAAa,CAC7B,IAAIjC,EAAO,EACX,GAAIiC,EAAI,SAAW,EAAG,OAAOjC,EAC7B,QAASkC,EAAI,EAAGA,EAAID,EAAI,OAAQC,IAAK,CACpC,IAAMC,EAAKF,EAAI,WAAWC,CAAC,EAC3BlC,GAAQA,GAAQ,GAAKA,EAAOmC,EAC5BnC,EAAOA,EAAOA,CACf,CACA,OAAOA,CACR,CAEA,SAASC,GAAUmC,EAAc,CAChC,IAAIC,EACAC,EAAS,GAETC,EAAUP,GAAQI,CAAI,EACpBI,EAAOD,EAAU,EAAI,IAAM,GAIjC,IAFAA,EAAU,KAAK,IAAIA,CAAO,EAEnBA,GAAWR,IACjBM,EAAME,EAAUR,GAChBQ,EAAU,KAAK,MAAMA,EAAUR,EAAM,EACrCO,EAASR,GAAWO,CAAG,EAAIC,EAG5B,OAAIC,EAAU,IACbD,EAASR,GAAWS,CAAO,EAAID,GAGzBE,EAAOF,CACf", "names": ["e", "t", "n", "s", "o", "r", "c", "i", "a", "l", "u", "f", "p", "m", "e", "t", "s", "r", "u", "c", "f", "p", "n", "o", "COMMENT", "RULESET", "DECLARATION", "IMPORT", "KEYFRAMES", "LAYER", "abs", "from", "trim", "value", "replace", "value", "pattern", "replacement", "indexof", "search", "position", "charat", "index", "substr", "begin", "end", "strlen", "sizeof", "append", "array", "line", "column", "length", "position", "character", "characters", "node", "value", "root", "parent", "type", "props", "children", "siblings", "char", "character", "prev", "position", "charat", "characters", "column", "line", "next", "length", "peek", "caret", "slice", "begin", "end", "substr", "token", "type", "alloc", "value", "strlen", "dealloc", "delimit", "trim", "delimiter", "whitespace", "type", "character", "peek", "next", "token", "escaping", "index", "count", "next", "character", "slice", "caret", "peek", "delimiter", "type", "position", "commenter", "from", "identifier", "token", "compile", "value", "dealloc", "parse", "alloc", "root", "parent", "rule", "rules", "rulesets", "pseudo", "points", "declarations", "index", "offset", "length", "at<PERSON>le", "property", "previous", "variable", "scanning", "ampersand", "character", "type", "props", "children", "reference", "characters", "next", "charat", "indexof", "replace", "delimit", "abs", "whitespace", "escaping", "caret", "peek", "append", "comment", "commenter", "token", "strlen", "substr", "declaration", "ruleset", "prev", "from", "identifier", "siblings", "post", "size", "sizeof", "i", "j", "k", "x", "y", "z", "trim", "node", "RULESET", "COMMENT", "char", "DECLARATION", "serialize", "children", "callback", "output", "i", "stringify", "element", "index", "LAYER", "IMPORT", "DECLARATION", "COMMENT", "KEYFRAMES", "RULESET", "strlen", "middleware", "collection", "length", "sizeof", "element", "index", "children", "callback", "output", "i", "ELEMENT_NODE", "TEXT_NODE", "render", "walkSync", "matches", "scope", "opts", "doc", "hash", "<PERSON><PERSON>h", "actions", "hasStyle", "selectors", "nodes", "node", "hasAttribute", "selector", "getSelectors", "scopeElement", "scopeCSS", "s", "action", "NEVER_SCOPED", "name", "scopeSelector", "ast", "m", "left", "right", "combinator", "css", "serialize", "compile", "middleware", "element", "prop", "stringify", "p", "dictionary", "binary", "bitwise", "str", "i", "ch", "text", "num", "result", "integer", "sign"]}