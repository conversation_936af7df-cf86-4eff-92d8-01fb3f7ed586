<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - API调试脚本
 */

echo "=== QiyeDIY API调试 ===\n\n";

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "1. 测试基础HTTP请求...\n";

// 创建HTTP上下文
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

// 测试根路径
echo "   测试 http://localhost:8000/ ...\n";
$response = file_get_contents('http://localhost:8000/', false, $context);
$httpCode = $http_response_header[0] ?? 'Unknown';
echo "   HTTP响应: {$httpCode}\n";
echo "   响应内容: " . substr($response ?: 'NULL', 0, 200) . "\n\n";

// 测试API路径
echo "   测试 http://localhost:8000/api/test/index ...\n";
$response = file_get_contents('http://localhost:8000/api/test/index', false, $context);
$httpCode = $http_response_header[0] ?? 'Unknown';
echo "   HTTP响应: {$httpCode}\n";
echo "   响应内容: " . substr($response ?: 'NULL', 0, 200) . "\n\n";

echo "2. 测试ThinkPHP直接调用...\n";

// 切换到backend目录
chdir(__DIR__ . '/backend');

try {
    // 加载ThinkPHP
    require_once 'vendor/autoload.php';
    
    echo "   ✅ ThinkPHP自动加载成功\n";
    
    // 创建应用实例
    $app = new \think\App();
    echo "   ✅ ThinkPHP应用实例创建成功\n";
    
    // 测试数据库连接
    $db = \think\facade\Db::connect();
    $userCount = $db->table('qd_users')->count();
    echo "   ✅ 数据库连接成功，用户数量: {$userCount}\n";
    
    // 测试控制器实例化
    $controller = new \app\controller\Index();
    echo "   ✅ Index控制器实例化成功\n";
    
    // 测试方法调用
    $result = $controller->index();
    echo "   ✅ Index控制器方法调用成功\n";
    echo "   返回类型: " . get_class($result) . "\n";
    
    // 获取响应内容
    if (method_exists($result, 'getContent')) {
        $content = $result->getContent();
        echo "   响应内容: " . substr($content, 0, 200) . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ ThinkPHP错误: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "   堆栈: " . substr($e->getTraceAsString(), 0, 500) . "\n";
} catch (Error $e) {
    echo "   ❌ PHP错误: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n3. 检查配置文件...\n";

$configFiles = [
    'config/app.php',
    'config/database.php', 
    'config/middleware.php',
    '.env'
];

foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file} 存在\n";
    } else {
        echo "   ❌ {$file} 不存在\n";
    }
}

echo "\n4. 检查目录权限...\n";

$dirs = [
    'runtime',
    'runtime/cache',
    'runtime/log',
    'runtime/temp',
    'public'
];

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir);
        echo "   " . ($writable ? "✅" : "❌") . " {$dir} " . ($writable ? "可写" : "不可写") . "\n";
    } else {
        echo "   ❌ {$dir} 不存在\n";
    }
}

echo "\n=== 调试完成 ===\n";
