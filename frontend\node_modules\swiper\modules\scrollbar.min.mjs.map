{"version": 3, "file": "scrollbar.mjs.mjs", "names": ["getDocument", "makeElementsArray", "classesToTokens", "createElement", "nextTick", "elementOffset", "createElementIfNotDefined", "classesToSelector", "Sc<PERSON><PERSON>", "_ref", "swiper", "extendParams", "on", "emit", "document", "dragStartPos", "dragSize", "trackSize", "divider", "isTouched", "timeout", "dragTimeout", "setTranslate", "params", "scrollbar", "el", "rtlTranslate", "rtl", "dragEl", "progress", "loop", "progressLoop", "newSize", "newPos", "isHorizontal", "style", "transform", "width", "height", "hide", "clearTimeout", "opacity", "setTimeout", "transitionDuration", "updateSize", "offsetWidth", "offsetHeight", "size", "virtualSize", "slidesOffsetBefore", "centeredSlides", "snapGrid", "parseInt", "display", "watchOverflow", "enabled", "classList", "isLocked", "lockClass", "getPointerPosition", "e", "clientX", "clientY", "setDragPosition", "positionRatio", "Math", "max", "min", "position", "minTranslate", "maxTranslate", "updateProgress", "updateActiveIndex", "updateSlidesClasses", "onDragStart", "wrapperEl", "target", "getBoundingClientRect", "preventDefault", "stopPropagation", "cssMode", "onDragMove", "cancelable", "returnValue", "onDragEnd", "snapOnRelease", "slideToClosest", "events", "method", "activeListener", "passiveListeners", "passive", "capture", "passiveListener", "eventMethod", "init", "swiperEl", "originalParams", "isElement", "querySelector", "querySelectorAll", "length", "uniqueNavElements", "add", "horizontalClass", "verticalClass", "dragClass", "append", "Object", "assign", "draggable", "destroy", "remove", "scrollbarDisabledClass", "for<PERSON>ach", "subEl", "disable", "_s", "duration", "setTransition", "enable"], "sources": ["0"], "mappings": "YAAcA,gBAAmB,+CACnBC,uBAAwBC,qBAAsBC,mBAAoBC,cAAeC,kBAAqB,sCACtGC,8BAAiC,8DACjCC,sBAAyB,wCAEvC,SAASC,UAAUC,GACjB,IAAIC,OACFA,EAAMC,aACNA,EAAYC,GACZA,EAAEC,KACFA,GACEJ,EACJ,MAAMK,EAAWd,cACjB,IAGIe,EACAC,EACAC,EACAC,EANAC,GAAY,EACZC,EAAU,KACVC,EAAc,KAuBlB,SAASC,IACP,IAAKZ,EAAOa,OAAOC,UAAUC,KAAOf,EAAOc,UAAUC,GAAI,OACzD,MAAMD,UACJA,EACAE,aAAcC,GACZjB,GACEkB,OACJA,EAAMH,GACNA,GACED,EACED,EAASb,EAAOa,OAAOC,UACvBK,EAAWnB,EAAOa,OAAOO,KAAOpB,EAAOqB,aAAerB,EAAOmB,SACnE,IAAIG,EAAUhB,EACViB,GAAUhB,EAAYD,GAAYa,EAClCF,GACFM,GAAUA,EACNA,EAAS,GACXD,EAAUhB,EAAWiB,EACrBA,EAAS,IACCA,EAASjB,EAAWC,IAC9Be,EAAUf,EAAYgB,IAEfA,EAAS,GAClBD,EAAUhB,EAAWiB,EACrBA,EAAS,GACAA,EAASjB,EAAWC,IAC7Be,EAAUf,EAAYgB,GAEpBvB,EAAOwB,gBACTN,EAAOO,MAAMC,UAAY,eAAeH,aACxCL,EAAOO,MAAME,MAAQ,GAAGL,QAExBJ,EAAOO,MAAMC,UAAY,oBAAoBH,UAC7CL,EAAOO,MAAMG,OAAS,GAAGN,OAEvBT,EAAOgB,OACTC,aAAapB,GACbK,EAAGU,MAAMM,QAAU,EACnBrB,EAAUsB,YAAW,KACnBjB,EAAGU,MAAMM,QAAU,EACnBhB,EAAGU,MAAMQ,mBAAqB,OAAO,GACpC,KAEP,CAKA,SAASC,IACP,IAAKlC,EAAOa,OAAOC,UAAUC,KAAOf,EAAOc,UAAUC,GAAI,OACzD,MAAMD,UACJA,GACEd,GACEkB,OACJA,EAAMH,GACNA,GACED,EACJI,EAAOO,MAAME,MAAQ,GACrBT,EAAOO,MAAMG,OAAS,GACtBrB,EAAYP,EAAOwB,eAAiBT,EAAGoB,YAAcpB,EAAGqB,aACxD5B,EAAUR,EAAOqC,MAAQrC,EAAOsC,YAActC,EAAOa,OAAO0B,oBAAsBvC,EAAOa,OAAO2B,eAAiBxC,EAAOyC,SAAS,GAAK,IAEpInC,EADuC,SAArCN,EAAOa,OAAOC,UAAUR,SACfC,EAAYC,EAEZkC,SAAS1C,EAAOa,OAAOC,UAAUR,SAAU,IAEpDN,EAAOwB,eACTN,EAAOO,MAAME,MAAQ,GAAGrB,MAExBY,EAAOO,MAAMG,OAAS,GAAGtB,MAGzBS,EAAGU,MAAMkB,QADPnC,GAAW,EACM,OAEA,GAEjBR,EAAOa,OAAOC,UAAUe,OAC1Bd,EAAGU,MAAMM,QAAU,GAEjB/B,EAAOa,OAAO+B,eAAiB5C,EAAO6C,SACxC/B,EAAUC,GAAG+B,UAAU9C,EAAO+C,SAAW,MAAQ,UAAU/C,EAAOa,OAAOC,UAAUkC,UAEvF,CACA,SAASC,EAAmBC,GAC1B,OAAOlD,EAAOwB,eAAiB0B,EAAEC,QAAUD,EAAEE,OAC/C,CACA,SAASC,EAAgBH,GACvB,MAAMpC,UACJA,EACAE,aAAcC,GACZjB,GACEe,GACJA,GACED,EACJ,IAAIwC,EACJA,GAAiBL,EAAmBC,GAAKvD,cAAcoB,GAAIf,EAAOwB,eAAiB,OAAS,QAA2B,OAAjBnB,EAAwBA,EAAeC,EAAW,KAAOC,EAAYD,GAC3KgD,EAAgBC,KAAKC,IAAID,KAAKE,IAAIH,EAAe,GAAI,GACjDrC,IACFqC,EAAgB,EAAIA,GAEtB,MAAMI,EAAW1D,EAAO2D,gBAAkB3D,EAAO4D,eAAiB5D,EAAO2D,gBAAkBL,EAC3FtD,EAAO6D,eAAeH,GACtB1D,EAAOY,aAAa8C,GACpB1D,EAAO8D,oBACP9D,EAAO+D,qBACT,CACA,SAASC,EAAYd,GACnB,MAAMrC,EAASb,EAAOa,OAAOC,WACvBA,UACJA,EAASmD,UACTA,GACEjE,GACEe,GACJA,EAAEG,OACFA,GACEJ,EACJL,GAAY,EACZJ,EAAe6C,EAAEgB,SAAWhD,EAAS+B,EAAmBC,GAAKA,EAAEgB,OAAOC,wBAAwBnE,EAAOwB,eAAiB,OAAS,OAAS,KACxI0B,EAAEkB,iBACFlB,EAAEmB,kBACFJ,EAAUxC,MAAMQ,mBAAqB,QACrCf,EAAOO,MAAMQ,mBAAqB,QAClCoB,EAAgBH,GAChBpB,aAAanB,GACbI,EAAGU,MAAMQ,mBAAqB,MAC1BpB,EAAOgB,OACTd,EAAGU,MAAMM,QAAU,GAEjB/B,EAAOa,OAAOyD,UAChBtE,EAAOiE,UAAUxC,MAAM,oBAAsB,QAE/CtB,EAAK,qBAAsB+C,EAC7B,CACA,SAASqB,EAAWrB,GAClB,MAAMpC,UACJA,EAASmD,UACTA,GACEjE,GACEe,GACJA,EAAEG,OACFA,GACEJ,EACCL,IACDyC,EAAEkB,gBAAkBlB,EAAEsB,WAAYtB,EAAEkB,iBAAsBlB,EAAEuB,aAAc,EAC9EpB,EAAgBH,GAChBe,EAAUxC,MAAMQ,mBAAqB,MACrClB,EAAGU,MAAMQ,mBAAqB,MAC9Bf,EAAOO,MAAMQ,mBAAqB,MAClC9B,EAAK,oBAAqB+C,GAC5B,CACA,SAASwB,EAAUxB,GACjB,MAAMrC,EAASb,EAAOa,OAAOC,WACvBA,UACJA,EAASmD,UACTA,GACEjE,GACEe,GACJA,GACED,EACCL,IACLA,GAAY,EACRT,EAAOa,OAAOyD,UAChBtE,EAAOiE,UAAUxC,MAAM,oBAAsB,GAC7CwC,EAAUxC,MAAMQ,mBAAqB,IAEnCpB,EAAOgB,OACTC,aAAanB,GACbA,EAAcjB,UAAS,KACrBqB,EAAGU,MAAMM,QAAU,EACnBhB,EAAGU,MAAMQ,mBAAqB,OAAO,GACpC,MAEL9B,EAAK,mBAAoB+C,GACrBrC,EAAO8D,eACT3E,EAAO4E,iBAEX,CACA,SAASC,EAAOC,GACd,MAAMhE,UACJA,EAASD,OACTA,GACEb,EACEe,EAAKD,EAAUC,GACrB,IAAKA,EAAI,OACT,MAAMmD,EAASnD,EACTgE,IAAiBlE,EAAOmE,kBAAmB,CAC/CC,SAAS,EACTC,SAAS,GAELC,IAAkBtE,EAAOmE,kBAAmB,CAChDC,SAAS,EACTC,SAAS,GAEX,IAAKhB,EAAQ,OACb,MAAMkB,EAAyB,OAAXN,EAAkB,mBAAqB,sBAC3DZ,EAAOkB,GAAa,cAAepB,EAAae,GAChD3E,EAASgF,GAAa,cAAeb,EAAYQ,GACjD3E,EAASgF,GAAa,YAAaV,EAAWS,EAChD,CASA,SAASE,IACP,MAAMvE,UACJA,EACAC,GAAIuE,GACFtF,EACJA,EAAOa,OAAOC,UAAYlB,0BAA0BI,EAAQA,EAAOuF,eAAezE,UAAWd,EAAOa,OAAOC,UAAW,CACpHC,GAAI,qBAEN,MAAMF,EAASb,EAAOa,OAAOC,UAC7B,IAAKD,EAAOE,GAAI,OAChB,IAAIA,EAeAG,EAXJ,GAHyB,iBAAdL,EAAOE,IAAmBf,EAAOwF,YAC1CzE,EAAKf,EAAOe,GAAG0E,cAAc5E,EAAOE,KAEjCA,GAA2B,iBAAdF,EAAOE,GAGbA,IACVA,EAAKF,EAAOE,SAFZ,GADAA,EAAKX,EAASsF,iBAAiB7E,EAAOE,KACjCA,EAAG4E,OAAQ,OAId3F,EAAOa,OAAO+E,mBAA0C,iBAAd/E,EAAOE,IAAmBA,EAAG4E,OAAS,GAAqD,IAAhDL,EAASI,iBAAiB7E,EAAOE,IAAI4E,SAC5H5E,EAAKuE,EAASG,cAAc5E,EAAOE,KAEjCA,EAAG4E,OAAS,IAAG5E,EAAKA,EAAG,IAC3BA,EAAG+B,UAAU+C,IAAI7F,EAAOwB,eAAiBX,EAAOiF,gBAAkBjF,EAAOkF,eAErEhF,IACFG,EAASH,EAAG0E,cAAc5F,kBAAkBG,EAAOa,OAAOC,UAAUkF,YAC/D9E,IACHA,EAASzB,cAAc,MAAOO,EAAOa,OAAOC,UAAUkF,WACtDjF,EAAGkF,OAAO/E,KAGdgF,OAAOC,OAAOrF,EAAW,CACvBC,KACAG,WAEEL,EAAOuF,WA5CNpG,EAAOa,OAAOC,UAAUC,IAAOf,EAAOc,UAAUC,IACrD8D,EAAO,MA8CH9D,GACFA,EAAG+B,UAAU9C,EAAO6C,QAAU,SAAW,UAAUrD,gBAAgBQ,EAAOa,OAAOC,UAAUkC,WAE/F,CACA,SAASqD,IACP,MAAMxF,EAASb,EAAOa,OAAOC,UACvBC,EAAKf,EAAOc,UAAUC,GACxBA,GACFA,EAAG+B,UAAUwD,UAAU9G,gBAAgBQ,EAAOwB,eAAiBX,EAAOiF,gBAAkBjF,EAAOkF,gBAnD5F/F,EAAOa,OAAOC,UAAUC,IAAOf,EAAOc,UAAUC,IACrD8D,EAAO,MAqDT,CApRA5E,EAAa,CACXa,UAAW,CACTC,GAAI,KACJT,SAAU,OACVuB,MAAM,EACNuE,WAAW,EACXzB,eAAe,EACf3B,UAAW,wBACXgD,UAAW,wBACXO,uBAAwB,4BACxBT,gBAAiB,8BACjBC,cAAe,+BAGnB/F,EAAOc,UAAY,CACjBC,GAAI,KACJG,OAAQ,MAqQVhB,EAAG,mBAAmB,KACpB,IAAKF,EAAOc,YAAcd,EAAOc,UAAUC,GAAI,OAC/C,MAAMF,EAASb,EAAOa,OAAOC,UAC7B,IAAIC,GACFA,GACEf,EAAOc,UACXC,EAAKxB,kBAAkBwB,GACvBA,EAAGyF,SAAQC,IACTA,EAAM3D,UAAUwD,OAAOzF,EAAOiF,gBAAiBjF,EAAOkF,eACtDU,EAAM3D,UAAU+C,IAAI7F,EAAOwB,eAAiBX,EAAOiF,gBAAkBjF,EAAOkF,cAAc,GAC1F,IAEJ7F,EAAG,QAAQ,MAC+B,IAApCF,EAAOa,OAAOC,UAAU+B,QAE1B6D,KAEArB,IACAnD,IACAtB,IACF,IAEFV,EAAG,4DAA4D,KAC7DgC,GAAY,IAEdhC,EAAG,gBAAgB,KACjBU,GAAc,IAEhBV,EAAG,iBAAiB,CAACyG,EAAIC,MAnPzB,SAAuBA,GAChB5G,EAAOa,OAAOC,UAAUC,IAAOf,EAAOc,UAAUC,KACrDf,EAAOc,UAAUI,OAAOO,MAAMQ,mBAAqB,GAAG2E,MACxD,CAiPEC,CAAcD,EAAS,IAEzB1G,EAAG,kBAAkB,KACnB,MAAMa,GACJA,GACEf,EAAOc,UACPC,GACFA,EAAG+B,UAAU9C,EAAO6C,QAAU,SAAW,UAAUrD,gBAAgBQ,EAAOa,OAAOC,UAAUkC,WAC7F,IAEF9C,EAAG,WAAW,KACZmG,GAAS,IAEX,MASMK,EAAU,KACd1G,EAAOe,GAAG+B,UAAU+C,OAAOrG,gBAAgBQ,EAAOa,OAAOC,UAAUyF,yBAC/DvG,EAAOc,UAAUC,IACnBf,EAAOc,UAAUC,GAAG+B,UAAU+C,OAAOrG,gBAAgBQ,EAAOa,OAAOC,UAAUyF,yBAE/EF,GAAS,EAEXH,OAAOC,OAAOnG,EAAOc,UAAW,CAC9BgG,OAjBa,KACb9G,EAAOe,GAAG+B,UAAUwD,UAAU9G,gBAAgBQ,EAAOa,OAAOC,UAAUyF,yBAClEvG,EAAOc,UAAUC,IACnBf,EAAOc,UAAUC,GAAG+B,UAAUwD,UAAU9G,gBAAgBQ,EAAOa,OAAOC,UAAUyF,yBAElFlB,IACAnD,IACAtB,GAAc,EAWd8F,UACAxE,aACAtB,eACAyE,OACAgB,WAEJ,QAESvG"}