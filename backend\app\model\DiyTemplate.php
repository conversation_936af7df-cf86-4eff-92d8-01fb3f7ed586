<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - DIY模板模型
 */

declare(strict_types=1);

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * DIY模板模型
 * @property int $id 模板ID
 * @property string $name 模板名称
 * @property string $slug 模板标识
 * @property string $description 模板描述
 * @property string $preview 预览图
 * @property string $thumbnail 缩略图
 * @property string $content 模板内容(JSON格式)
 * @property string $category 模板分类
 * @property array $tags 模板标签
 * @property int $is_system 是否系统模板
 * @property int $is_free 是否免费
 * @property float $price 模板价格
 * @property int $download_count 下载次数
 * @property float $rating 评分
 * @property int $rating_count 评分人数
 * @property int $sort_order 排序
 * @property int $created_by 创建者
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class DiyTemplate extends Model
{
    use SoftDelete;

    /**
     * 数据表名
     */
    protected $name = 'diy_templates';

    /**
     * 数据表前缀
     */
    protected $connection = 'mysql';

    /**
     * 主键
     */
    protected $pk = 'id';

    /**
     * 自动时间戳
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     */
    protected $createTime = 'created_at';

    /**
     * 更新时间字段
     */
    protected $updateTime = 'updated_at';

    /**
     * 软删除字段
     */
    protected $deleteTime = 'deleted_at';

    /**
     * 字段类型转换
     */
    protected $type = [
        'id' => 'integer',
        'is_system' => 'integer',
        'is_free' => 'integer',
        'price' => 'float',
        'download_count' => 'integer',
        'rating' => 'float',
        'rating_count' => 'integer',
        'sort_order' => 'integer',
        'created_by' => 'integer',
        'tags' => 'json',
        'content' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * JSON字段
     */
    protected $json = ['tags', 'content'];

    /**
     * 字段默认值
     */
    protected $field = [
        'id', 'name', 'slug', 'description', 'preview', 'thumbnail', 
        'content', 'category', 'tags', 'is_system', 'is_free', 'price',
        'download_count', 'rating', 'rating_count', 'sort_order', 
        'created_by', 'created_at', 'updated_at'
    ];

    /**
     * 允许写入的字段
     */
    protected $allowFields = [
        'name', 'slug', 'description', 'preview', 'thumbnail', 
        'content', 'category', 'tags', 'is_system', 'is_free', 'price',
        'download_count', 'rating', 'rating_count', 'sort_order', 'created_by'
    ];

    /**
     * 关联创建者
     * @return \think\model\relation\BelongsTo
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    /**
     * 关联使用该模板的页面
     * @return \think\model\relation\HasMany
     */
    public function pages()
    {
        return $this->hasMany(DiyPage::class, 'template_id', 'id');
    }

    /**
     * 获取器：格式化内容
     * @param mixed $value
     * @return array
     */
    public function getContentAttr($value): array
    {
        if (empty($value)) {
            return [];
        }
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        
        return is_array($value) ? $value : [];
    }

    /**
     * 设置器：处理内容
     * @param mixed $value
     * @return string
     */
    public function setContentAttr($value): string
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        return is_string($value) ? $value : '';
    }

    /**
     * 获取器：格式化标签
     * @param mixed $value
     * @return array
     */
    public function getTagsAttr($value): array
    {
        if (empty($value)) {
            return [];
        }
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        
        return is_array($value) ? $value : [];
    }

    /**
     * 设置器：处理标签
     * @param mixed $value
     * @return string
     */
    public function setTagsAttr($value): string
    {
        if (is_array($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        return is_string($value) ? $value : '[]';
    }

    /**
     * 获取器：格式化价格
     * @param mixed $value
     * @return float
     */
    public function getPriceAttr($value): float
    {
        return (float) $value;
    }

    /**
     * 获取器：格式化评分
     * @param mixed $value
     * @return float
     */
    public function getRatingAttr($value): float
    {
        return round((float) $value, 2);
    }

    /**
     * 获取器：是否免费文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getIsFreeTextAttr($value, array $data): string
    {
        return $data['is_free'] ? '免费' : '付费';
    }

    /**
     * 获取器：是否系统模板文本
     * @param mixed $value
     * @param array $data
     * @return string
     */
    public function getIsSystemTextAttr($value, array $data): string
    {
        return $data['is_system'] ? '系统模板' : '自定义模板';
    }

    /**
     * 获取器：预览图URL
     * @param mixed $value
     * @return string
     */
    public function getPreviewUrlAttr($value): string
    {
        if (empty($this->preview)) {
            return '';
        }
        
        // 如果已经是完整URL，直接返回
        if (strpos($this->preview, 'http') === 0) {
            return $this->preview;
        }
        
        // 拼接完整URL
        $domain = config('app.domain', '');
        return $domain . $this->preview;
    }

    /**
     * 获取器：缩略图URL
     * @param mixed $value
     * @return string
     */
    public function getThumbnailUrlAttr($value): string
    {
        if (empty($this->thumbnail)) {
            return '';
        }
        
        // 如果已经是完整URL，直接返回
        if (strpos($this->thumbnail, 'http') === 0) {
            return $this->thumbnail;
        }
        
        // 拼接完整URL
        $domain = config('app.domain', '');
        return $domain . $this->thumbnail;
    }

    /**
     * 搜索器：按名称搜索
     * @param \think\db\Query $query
     * @param mixed $value
     */
    public function searchNameAttr($query, $value): void
    {
        if (!empty($value)) {
            $query->where('name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：按分类搜索
     * @param \think\db\Query $query
     * @param mixed $value
     */
    public function searchCategoryAttr($query, $value): void
    {
        if (!empty($value)) {
            $query->where('category', $value);
        }
    }

    /**
     * 搜索器：按是否免费搜索
     * @param \think\db\Query $query
     * @param mixed $value
     */
    public function searchIsFreeAttr($query, $value): void
    {
        if ($value !== '' && $value !== null) {
            $query->where('is_free', $value);
        }
    }

    /**
     * 搜索器：按是否系统模板搜索
     * @param \think\db\Query $query
     * @param mixed $value
     */
    public function searchIsSystemAttr($query, $value): void
    {
        if ($value !== '' && $value !== null) {
            $query->where('is_system', $value);
        }
    }

    /**
     * 增加下载次数
     * @return bool
     */
    public function incrementDownloadCount(): bool
    {
        return $this->inc('download_count')->save();
    }

    /**
     * 更新评分
     * @param float $rating
     * @return bool
     */
    public function updateRating(float $rating): bool
    {
        $currentRating = $this->rating;
        $currentCount = $this->rating_count;
        
        $newCount = $currentCount + 1;
        $newRating = (($currentRating * $currentCount) + $rating) / $newCount;
        
        return $this->save([
            'rating' => round($newRating, 2),
            'rating_count' => $newCount
        ]);
    }
}
