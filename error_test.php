<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 错误测试脚本
 */

echo "=== 错误诊断测试 ===\n\n";

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "1. 测试HTTP请求并获取详细错误...\n";

function getHttpResponse($url) {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    $headers = $http_response_header ?? [];
    
    return [
        'response' => $response,
        'headers' => $headers,
        'status' => $headers[0] ?? 'Unknown'
    ];
}

$urls = [
    'http://localhost:8000/',
    'http://localhost:8000/simple/test',
    'http://localhost:8000/docs',
    'http://localhost:8000/api/test/index'
];

foreach ($urls as $url) {
    echo "\n   测试: {$url}\n";
    $result = getHttpResponse($url);
    echo "   状态: " . $result['status'] . "\n";
    
    if ($result['response']) {
        // 检查是否是JSON
        $json = json_decode($result['response'], true);
        if ($json) {
            echo "   类型: JSON响应\n";
            if (isset($json['code'])) {
                echo "   代码: " . $json['code'] . "\n";
                echo "   消息: " . ($json['message'] ?? 'N/A') . "\n";
            }
        } else {
            echo "   类型: HTML/文本响应\n";
            // 提取错误信息
            if (preg_match('/<title>(.*?)<\/title>/i', $result['response'], $matches)) {
                echo "   标题: " . $matches[1] . "\n";
            }
            
            // 查找错误消息
            if (preg_match('/Fatal error:(.*?)in/i', $result['response'], $matches)) {
                echo "   致命错误: " . trim($matches[1]) . "\n";
            }
            
            if (preg_match('/Warning:(.*?)in/i', $result['response'], $matches)) {
                echo "   警告: " . trim($matches[1]) . "\n";
            }
            
            // 显示前200个字符
            echo "   内容: " . substr(strip_tags($result['response']), 0, 200) . "...\n";
        }
    } else {
        echo "   响应: 无内容\n";
    }
}

echo "\n2. 测试ThinkPHP组件加载...\n";

// 切换到backend目录
chdir(__DIR__ . '/backend');

try {
    require_once 'vendor/autoload.php';
    echo "   ✅ 自动加载成功\n";
    
    // 测试环境变量加载
    $cacheDriver = env('CACHE_DRIVER');
    echo "   缓存驱动: " . ($cacheDriver ?: 'NULL') . "\n";
    
    $dbHost = env('DATABASE_HOSTNAME');
    echo "   数据库主机: " . ($dbHost ?: 'NULL') . "\n";
    
    // 测试配置加载
    $app = new \think\App();
    echo "   ✅ 应用实例创建成功\n";
    
    // 测试配置获取
    $config = $app->config;
    $cacheConfig = $config->get('cache');
    echo "   缓存配置: " . (is_array($cacheConfig) ? "已加载" : "未加载") . "\n";
    
    if (is_array($cacheConfig)) {
        echo "   默认缓存: " . ($cacheConfig['default'] ?? 'NULL') . "\n";
        echo "   文件缓存路径: " . ($cacheConfig['stores']['file']['path'] ?? 'NULL') . "\n";
    }
    
    // 测试缓存实例化
    try {
        $cache = $app->cache;
        echo "   ✅ 缓存实例创建成功\n";
    } catch (Exception $e) {
        echo "   ❌ 缓存实例创建失败: " . $e->getMessage() . "\n";
    }
    
    // 测试数据库连接
    try {
        $db = $app->db;
        echo "   ✅ 数据库实例创建成功\n";
    } catch (Exception $e) {
        echo "   ❌ 数据库实例创建失败: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ 组件加载失败: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n=== 诊断完成 ===\n";
