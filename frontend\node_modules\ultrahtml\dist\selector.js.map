{"version": 3, "sources": ["../src/selector.ts", "../node_modules/.pnpm/parsel-js@1.1.2/node_modules/parsel-js/dist/parsel.min.js"], "sourcesContent": ["import type { Node } from './index.js';\nimport { ELEMENT_NODE, TEXT_NODE, walkSync } from './index.js';\nimport type { AST, AttributeToken } from 'parsel-js';\nimport {\n\tparse,\n\tspecificity as getSpecificity,\n\tspecificityToNumber,\n} from 'parsel-js';\n\nexport function specificity(selector: string) {\n\treturn specificityToNumber(getSpecificity(selector), 10);\n}\n\nexport function matches(node: Node, selector: string): boolean {\n\tconst match = selectorToMatch(selector);\n\treturn match(node, node.parent, nthChildIndex(node, node.parent));\n}\n\nexport function querySelector(node: Node, selector: string): Node {\n\tconst match = selectorToMatch(selector);\n\ttry {\n\t\treturn select(\n\t\t\tnode,\n\t\t\t(n: Node, parent?: Node, index?: number) => {\n\t\t\t\tlet m = match(n, parent, index);\n\t\t\t\tif (!m) return false;\n\t\t\t\treturn m;\n\t\t\t},\n\t\t\t{ single: true },\n\t\t)[0];\n\t} catch (e) {\n\t\tif (e instanceof Error) {\n\t\t\tthrow e;\n\t\t}\n\t\treturn e as Node;\n\t}\n}\n\nexport function querySelectorAll(node: Node, selector: string): Node[] {\n\tconst match = selectorToMatch(selector);\n\treturn select(node, (n: Node, parent?: Node, index?: number) => {\n\t\tlet m = match(n, parent, index);\n\t\tif (!m) return false;\n\t\treturn m;\n\t});\n}\n\nexport default querySelectorAll;\n\ninterface Matcher {\n\t(n: Node, parent?: Node, index?: number): boolean;\n}\n\nfunction select(\n\tnode: Node,\n\tmatch: Matcher,\n\topts: { single?: boolean } = { single: false },\n): Node[] {\n\tlet nodes: Node[] = [];\n\twalkSync(node, (n, parent, index): void => {\n\t\tif (n && n.type !== ELEMENT_NODE) return;\n\t\tif (match(n, parent, index)) {\n\t\t\tif (opts.single) throw n;\n\t\t\tnodes.push(n);\n\t\t}\n\t});\n\treturn nodes;\n}\n\nconst getAttributeMatch = (selector: AttributeToken) => {\n\tconst { operator = '=' } = selector;\n\tswitch (operator) {\n\t\tcase '=':\n\t\t\treturn (a: string, b: string) => a === b;\n\t\tcase '~=':\n\t\t\treturn (a: string, b: string) => a.split(/\\s+/g).includes(b);\n\t\tcase '|=':\n\t\t\treturn (a: string, b: string) => a.startsWith(b + '-');\n\t\tcase '*=':\n\t\t\treturn (a: string, b: string) => a.indexOf(b) > -1;\n\t\tcase '$=':\n\t\t\treturn (a: string, b: string) => a.endsWith(b);\n\t\tcase '^=':\n\t\t\treturn (a: string, b: string) => a.startsWith(b);\n\t}\n\treturn (a: string, b: string) => false;\n};\n\nconst nthChildIndex = (node: Node, parent?: Node) =>\n\tparent?.children\n\t\t.filter((n: Node) => n.type === ELEMENT_NODE)\n\t\t.findIndex((n: Node) => n === node);\nconst nthChild = (formula: string) => {\n\tlet [_, A = '1', B = '0'] =\n\t\t/^\\s*(?:(-?(?:\\d+)?)n)?\\s*\\+?\\s*(\\d+)?\\s*$/gm.exec(formula) ?? [];\n\tif (A.length === 0) A = '1';\n\tconst a = Number.parseInt(A === '-' ? '-1' : A);\n\tconst b = Number.parseInt(B);\n\treturn (n: number) => a * n + b;\n};\nconst lastChild = (node: Node, parent?: Node) =>\n\tparent?.children.filter((n: Node) => n.type === ELEMENT_NODE).pop() === node;\nconst firstChild = (node: Node, parent?: Node) =>\n\tparent?.children.filter((n: Node) => n.type === ELEMENT_NODE).shift() ===\n\tnode;\nconst onlyChild = (node: Node, parent?: Node) =>\n\tparent?.children.filter((n: Node) => n.type === ELEMENT_NODE).length === 1;\n\nconst createMatch = (selector: AST): Matcher => {\n\tswitch (selector.type) {\n\t\tcase 'type':\n\t\t\treturn (node: Node) => {\n\t\t\t\tif (selector.content === '*') return true;\n\t\t\t\treturn node.name === selector.name;\n\t\t\t};\n\t\tcase 'class':\n\t\t\treturn (node: Node) =>\n\t\t\t\tnode.attributes?.class?.split(/\\s+/g).includes(selector.name);\n\t\tcase 'id':\n\t\t\treturn (node: Node) => node.attributes?.id === selector.name;\n\t\tcase 'pseudo-class': {\n\t\t\tswitch (selector.name) {\n\t\t\t\tcase 'global':\n\t\t\t\t\treturn (...args) =>\n\t\t\t\t\t\tselectorToMatch(parse(selector.argument!)!)(...args);\n\t\t\t\tcase 'not':\n\t\t\t\t\treturn (...args) => !createMatch(selector.subtree!)(...args);\n\t\t\t\tcase 'is':\n\t\t\t\t\treturn (...args) => selectorToMatch(selector.subtree!)(...args);\n\t\t\t\tcase 'where':\n\t\t\t\t\treturn (...args) => selectorToMatch(selector.subtree!)(...args);\n\t\t\t\tcase 'root':\n\t\t\t\t\treturn (node: Node, parent?: Node) =>\n\t\t\t\t\t\tnode.type === ELEMENT_NODE && node.name === 'html';\n\t\t\t\tcase 'empty':\n\t\t\t\t\treturn (node: Node) =>\n\t\t\t\t\t\tnode.type === ELEMENT_NODE &&\n\t\t\t\t\t\t(node.children.length === 0 ||\n\t\t\t\t\t\t\tnode.children.every(\n\t\t\t\t\t\t\t\t(n: Node) => n.type === TEXT_NODE && n.value.trim() === '',\n\t\t\t\t\t\t\t));\n\t\t\t\tcase 'first-child':\n\t\t\t\t\treturn (node: Node, parent?: Node) => firstChild(node, parent);\n\t\t\t\tcase 'last-child':\n\t\t\t\t\treturn (node: Node, parent?: Node) => lastChild(node, parent);\n\t\t\t\tcase 'only-child':\n\t\t\t\t\treturn (node: Node, parent?: Node) => onlyChild(node, parent);\n\t\t\t\tcase 'nth-child':\n\t\t\t\t\treturn (node: Node, parent?: Node) => {\n\t\t\t\t\t\tconst target = nthChildIndex(node, parent) + 1;\n\t\t\t\t\t\tif (Number.isNaN(Number(selector.argument))) {\n\t\t\t\t\t\t\tswitch (selector.argument) {\n\t\t\t\t\t\t\t\tcase 'odd':\n\t\t\t\t\t\t\t\t\treturn Math.abs(target % 2) == 1;\n\t\t\t\t\t\t\t\tcase 'even':\n\t\t\t\t\t\t\t\t\treturn target % 2 === 0;\n\t\t\t\t\t\t\t\tdefault: {\n\t\t\t\t\t\t\t\t\tif (!selector.argument) {\n\t\t\t\t\t\t\t\t\t\tthrow new Error(`Unsupported empty nth-child selector!`);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tconst nth = nthChild(selector.argument);\n\t\t\t\t\t\t\t\t\tconst elements = parent?.children.filter(\n\t\t\t\t\t\t\t\t\t\t(n: Node) => n.type === ELEMENT_NODE,\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t\tconst childIndex = nthChildIndex(node, parent) + 1;\n\t\t\t\t\t\t\t\t\tfor (let i = 0; i < elements.length; i++) {\n\t\t\t\t\t\t\t\t\t\tlet n = nth(i);\n\t\t\t\t\t\t\t\t\t\tif (n > elements.length) return false;\n\t\t\t\t\t\t\t\t\t\tif (n === childIndex) return true;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn target === Number(selector.argument);\n\t\t\t\t\t};\n\t\t\t\tdefault:\n\t\t\t\t\tthrow new Error(`Unhandled pseudo-class: ${selector.name}!`);\n\t\t\t}\n\t\t}\n\t\tcase 'attribute':\n\t\t\treturn (node: Node) => {\n\t\t\t\tlet { caseSensitive, name, value } = selector;\n\t\t\t\tif (!node.attributes) return false;\n\t\t\t\tconst attrs = Object.entries(node.attributes as Record<string, string>);\n\t\t\t\tfor (let [attr, attrVal] of attrs) {\n\t\t\t\t\tif (caseSensitive === 'i') {\n\t\t\t\t\t\tvalue = name.toLowerCase();\n\t\t\t\t\t\tattrVal = attr.toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\tif (attr !== name) continue;\n\t\t\t\t\tif (!value) return true;\n\t\t\t\t\tif (\n\t\t\t\t\t\t(value[0] === '\"' || value[0] === \"'\") &&\n\t\t\t\t\t\tvalue[0] === value[value.length - 1]\n\t\t\t\t\t) {\n\t\t\t\t\t\tvalue = JSON.parse(value);\n\t\t\t\t\t}\n\t\t\t\t\tif (value) {\n\t\t\t\t\t\treturn getAttributeMatch(selector)(attrVal, value);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t};\n\t\tcase 'universal':\n\t\t\treturn (_: Node) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\tdefault: {\n\t\t\tthrow new Error(`Unhandled selector: ${selector.type}`);\n\t\t}\n\t}\n};\n\nconst selectorToMatch = (sel: string | AST): Matcher => {\n\tlet selector = typeof sel === 'string' ? parse(sel) : sel;\n\tswitch (selector?.type) {\n\t\tcase 'list': {\n\t\t\tconst matchers = selector.list.map((s: any) => createMatch(s));\n\t\t\treturn (node: Node, parent?: Node, index?: number) => {\n\t\t\t\tfor (const match of matchers) {\n\t\t\t\t\tif (match(node, parent!)) return true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t};\n\t\t}\n\t\tcase 'compound': {\n\t\t\tconst matchers = selector.list.map((s: any) => createMatch(s));\n\t\t\treturn (node: Node, parent?: Node, index?: number) => {\n\t\t\t\tfor (const match of matchers) {\n\t\t\t\t\tif (!match(node, parent!)) return false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\t\tcase 'complex': {\n\t\t\tconst { left, right, combinator } = selector;\n\t\t\tconst matchLeft = selectorToMatch(left);\n\t\t\tconst matchRight = selectorToMatch(right);\n\t\t\tlet leftMatches = new WeakSet();\n\t\t\treturn (node: Node, parent?: Node, i: number = 0) => {\n\t\t\t\tif (matchLeft(node)) {\n\t\t\t\t\tleftMatches.add(node);\n\t\t\t\t} else if (parent && leftMatches.has(parent) && combinator === ' ') {\n\t\t\t\t\tleftMatches.add(node);\n\t\t\t\t}\n\t\t\t\tif (!matchRight(node)) return false;\n\t\t\t\tswitch (combinator) {\n\t\t\t\t\tcase ' ': // fall-through\n\t\t\t\t\tcase '>':\n\t\t\t\t\t\treturn parent ? leftMatches.has(parent) : false;\n\t\t\t\t\tcase '~': {\n\t\t\t\t\t\tif (!parent) return false;\n\t\t\t\t\t\tfor (let sibling of parent.children.slice(0, i)) {\n\t\t\t\t\t\t\tif (leftMatches.has(sibling)) return true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tcase '+': {\n\t\t\t\t\t\tif (!parent) return false;\n\t\t\t\t\t\tlet prevSiblings = parent.children\n\t\t\t\t\t\t\t.slice(0, i)\n\t\t\t\t\t\t\t.filter((el: Node) => el.type === ELEMENT_NODE);\n\t\t\t\t\t\tif (prevSiblings.length === 0) return false;\n\t\t\t\t\t\tconst prev = prevSiblings[prevSiblings.length - 1];\n\t\t\t\t\t\tif (!prev) return false;\n\t\t\t\t\t\tif (leftMatches.has(prev)) return true;\n\t\t\t\t\t}\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t\tdefault:\n\t\t\treturn createMatch(selector!) as Matcher;\n\t}\n};\n", "const e={attribute:/\\[\\s*(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?(?<name>[-\\w\\P{ASCII}]+)\\s*(?:(?<operator>\\W?=)\\s*(?<value>.+?)\\s*(\\s(?<caseSensitive>[iIsS]))?\\s*)?\\]/gu,id:/#(?<name>[-\\w\\P{ASCII}]+)/gu,class:/\\.(?<name>[-\\w\\P{ASCII}]+)/gu,comma:/\\s*,\\s*/g,combinator:/\\s*[\\s>+~]\\s*/g,\"pseudo-element\":/::(?<name>[-\\w\\P{ASCII}]+)(?:\\((?<argument>¶*)\\))?/gu,\"pseudo-class\":/:(?<name>[-\\w\\P{ASCII}]+)(?:\\((?<argument>¶*)\\))?/gu,universal:/(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?\\*/gu,type:/(?:(?<namespace>\\*|[-\\w\\P{ASCII}]*)\\|)?(?<name>[-\\w\\P{ASCII}]+)/gu},t=new Set([\"combinator\",\"comma\"]),n=new Set([\"not\",\"is\",\"where\",\"has\",\"matches\",\"-moz-any\",\"-webkit-any\",\"nth-child\",\"nth-last-child\"]),s=/(?<index>[\\dn+-]+)\\s+of\\s+(?<subtree>.+)/,o={\"nth-child\":s,\"nth-last-child\":s},r=t=>{switch(t){case\"pseudo-element\":case\"pseudo-class\":return new RegExp(e[t].source.replace(\"(?<argument>¶*)\",\"(?<argument>.*)\"),\"gu\");default:return e[t]}};function c(e,t){let n=0,s=\"\";for(;t<e.length;t++){const o=e[t];switch(o){case\"(\":++n;break;case\")\":--n}if(s+=o,0===n)return s}return s}function i(n,s=e){if(!n)return[];const o=[n];for(const[e,t]of Object.entries(s))for(let n=0;n<o.length;n++){const s=o[n];if(\"string\"!=typeof s)continue;t.lastIndex=0;const r=t.exec(s);if(!r)continue;const c=r.index-1,i=[],a=r[0],l=s.slice(0,c+1);l&&i.push(l),i.push({...r.groups,type:e,content:a});const u=s.slice(c+a.length+1);u&&i.push(u),o.splice(n,1,...i)}let r=0;for(const e of o)switch(typeof e){case\"string\":throw new Error(`Unexpected sequence ${e} found at index ${r}`);case\"object\":r+=e.content.length,e.pos=[r-e.content.length,r],t.has(e.type)&&(e.content=e.content.trim()||\" \")}return o}const a=/(['\"])([^\\\\\\n]+?)\\1/g,l=/\\\\./g;function u(t,n=e){if(\"\"===(t=t.trim()))return[];const s=[];t=(t=t.replace(l,((e,t)=>(s.push({value:e,offset:t}),\"\".repeat(e.length))))).replace(a,((e,t,n,o)=>(s.push({value:e,offset:o}),`${t}${\"\".repeat(n.length)}${t}`)));{let e,n=0;for(;(e=t.indexOf(\"(\",n))>-1;){const o=c(t,e);s.push({value:o,offset:e}),t=`${t.substring(0,e)}(${\"¶\".repeat(o.length-2)})${t.substring(e+o.length)}`,n=e+o.length}}const o=i(t,n),u=new Set;for(const e of s.reverse())for(const t of o){const{offset:n,value:s}=e;if(!(t.pos[0]<=n&&n+s.length<=t.pos[1]))continue;const{content:o}=t,r=n-t.pos[0];t.content=o.slice(0,r)+s+o.slice(r+s.length),t.content!==o&&u.add(t)}for(const e of u){const t=r(e.type);if(!t)throw new Error(`Unknown token type: ${e.type}`);t.lastIndex=0;const n=t.exec(e.content);if(!n)throw new Error(`Unable to parse content for ${e.type}: ${e.content}`);Object.assign(e,n.groups)}return o}function f(e,{list:t=!0}={}){if(t&&e.find((e=>\"comma\"===e.type))){const t=[],n=[];for(let s=0;s<e.length;s++)if(\"comma\"===e[s].type){if(0===n.length)throw new Error(\"Incorrect comma at \"+s);t.push(f(n,{list:!1})),n.length=0}else n.push(e[s]);if(0===n.length)throw new Error(\"Trailing comma\");return t.push(f(n,{list:!1})),{type:\"list\",list:t}}for(let t=e.length-1;t>=0;t--){let n=e[t];if(\"combinator\"===n.type){let s=e.slice(0,t),o=e.slice(t+1);return{type:\"complex\",combinator:n.content,left:f(s),right:f(o)}}}switch(e.length){case 0:throw new Error(\"Could not build AST.\");case 1:return e[0];default:return{type:\"compound\",list:[...e]}}}function*p(e,t){switch(e.type){case\"list\":for(let t of e.list)yield*p(t,e);break;case\"complex\":yield*p(e.left,e),yield*p(e.right,e);break;case\"compound\":yield*e.list.map((t=>[t,e]));break;default:yield[e,t]}}function h(e,t,n){if(e)for(const[s,o]of p(e,n))t(s,o)}function m(e,{recursive:t=!0,list:s=!0}={}){const r=u(e);if(!r)return;const c=f(r,{list:s});if(!t)return c;for(const[e]of p(c)){if(\"pseudo-class\"!==e.type||!e.argument)continue;if(!n.has(e.name))continue;let t=e.argument;const s=o[e.name];if(s){const n=s.exec(t);if(!n)continue;Object.assign(e,n.groups),t=n.groups.subtree}t&&Object.assign(e,{subtree:m(t,{recursive:!0,list:!0})})}return c}function g(e){let t;return t=Array.isArray(e)?e:[...p(e)].map((([e])=>e)),t.map((e=>e.content)).join(\"\")}function d(e,t){return t=t||Math.max(...e)+1,e[0]*(t<<1)+e[1]*t+e[2]}function w(e){let t=e;if(\"string\"==typeof t&&(t=m(t,{recursive:!0})),!t)return[];if(\"list\"===t.type&&\"list\"in t){let e=10;const n=t.list.map((t=>{const n=w(t);return e=Math.max(e,...w(t)),n})),s=n.map((t=>d(t,e)));return n[s.indexOf(Math.max(...s))]}const s=[0,0,0];for(const[e]of p(t))switch(e.type){case\"id\":s[0]++;break;case\"class\":case\"attribute\":s[1]++;break;case\"pseudo-element\":case\"type\":s[2]++;break;case\"pseudo-class\":if(\"where\"===e.name)break;if(!n.has(e.name)||!e.subtree){s[1]++;break}w(e.subtree).forEach(((e,t)=>s[t]+=e)),\"nth-child\"!==e.name&&\"nth-last-child\"!==e.name||s[1]++}return s}export{n as RECURSIVE_PSEUDO_CLASSES,o as RECURSIVE_PSEUDO_CLASSES_ARGS,e as TOKENS,t as TRIM_TOKENS,p as flatten,c as gobbleParens,m as parse,w as specificity,d as specificityToNumber,g as stringify,u as tokenize,i as tokenizeBy,h as walk};\n"], "mappings": "AACA,OAAS,gBAAAA,EAAc,aAAAC,EAAW,YAAAC,MAAgB,aCDlD,IAAMC,EAAE,CAAC,UAAU,uJAAuJ,GAAG,8BAA8B,MAAM,+BAA+B,MAAM,WAAW,WAAW,iBAAiB,iBAAiB,uDAAuD,eAAe,sDAAsD,UAAU,8CAA8C,KAAK,mEAAmE,EAAEC,EAAE,IAAI,IAAI,CAAC,aAAa,OAAO,CAAC,EAAEC,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,MAAM,UAAU,WAAW,cAAc,YAAY,gBAAgB,CAAC,EAAEC,EAAE,2CAA2CC,EAAE,CAAC,YAAYD,EAAE,iBAAiBA,CAAC,EAAEE,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,iBAAiB,IAAI,eAAe,OAAO,IAAI,OAAOL,EAAE,CAAC,EAAE,OAAO,QAAQ,qBAAkB,iBAAiB,EAAE,IAAI,EAAE,QAAQ,OAAOA,EAAE,CAAC,CAAC,CAAC,EAAE,SAASM,EAAEN,EAAEC,EAAE,CAAC,IAAIC,EAAE,EAAEC,EAAE,GAAG,KAAKF,EAAED,EAAE,OAAOC,IAAI,CAAC,IAAMG,EAAEJ,EAAEC,CAAC,EAAE,OAAOG,EAAE,CAAC,IAAI,IAAI,EAAEF,EAAE,MAAM,IAAI,IAAI,EAAEA,CAAC,CAAC,GAAGC,GAAGC,EAAMF,IAAJ,EAAM,OAAOC,CAAC,CAAC,OAAOA,CAAC,CAAC,SAASI,EAAEL,EAAEC,EAAEH,EAAE,CAAC,GAAG,CAACE,EAAE,MAAM,CAAC,EAAE,IAAME,EAAE,CAACF,CAAC,EAAE,OAAS,CAACF,EAAEC,CAAC,IAAI,OAAO,QAAQE,CAAC,EAAE,QAAQD,EAAE,EAAEA,EAAEE,EAAE,OAAOF,IAAI,CAAC,IAAMC,EAAEC,EAAEF,CAAC,EAAE,GAAa,OAAOC,GAAjB,SAAmB,SAASF,EAAE,UAAU,EAAE,IAAMI,EAAEJ,EAAE,KAAKE,CAAC,EAAE,GAAG,CAACE,EAAE,SAAS,IAAMC,EAAED,EAAE,MAAM,EAAEE,EAAE,CAAC,EAAEC,EAAEH,EAAE,CAAC,EAAEI,EAAEN,EAAE,MAAM,EAAEG,EAAE,CAAC,EAAEG,GAAGF,EAAE,KAAKE,CAAC,EAAEF,EAAE,KAAK,CAAC,GAAGF,EAAE,OAAO,KAAKL,EAAE,QAAQQ,CAAC,CAAC,EAAE,IAAME,EAAEP,EAAE,MAAMG,EAAEE,EAAE,OAAO,CAAC,EAAEE,GAAGH,EAAE,KAAKG,CAAC,EAAEN,EAAE,OAAOF,EAAE,EAAE,GAAGK,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,QAAUP,KAAKI,EAAE,OAAO,OAAOJ,EAAE,CAAC,IAAI,SAAS,MAAM,IAAI,MAAM,uBAAuBA,CAAC,mBAAmB,CAAC,EAAE,EAAE,IAAI,SAAS,GAAGA,EAAE,QAAQ,OAAOA,EAAE,IAAI,CAAC,EAAEA,EAAE,QAAQ,OAAO,CAAC,EAAEC,EAAE,IAAID,EAAE,IAAI,IAAIA,EAAE,QAAQA,EAAE,QAAQ,KAAK,GAAG,IAAI,CAAC,OAAOI,CAAC,CAAC,IAAMI,EAAE,uBAAuBC,EAAE,OAAO,SAASC,EAAE,EAAE,EAAEV,EAAE,CAAC,IAAS,EAAE,EAAE,KAAK,KAAf,GAAkB,MAAM,CAAC,EAAE,IAAMG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,QAAQM,EAAG,CAACT,EAAEC,KAAKE,EAAE,KAAK,CAAC,MAAMH,EAAE,OAAOC,CAAC,CAAC,EAAE,SAAI,OAAOD,EAAE,MAAM,EAAG,GAAG,QAAQQ,EAAG,CAACR,EAAEC,EAAEC,EAAEE,KAAKD,EAAE,KAAK,CAAC,MAAMH,EAAE,OAAOI,CAAC,CAAC,EAAE,GAAGH,CAAC,GAAG,SAAI,OAAOC,EAAE,MAAM,CAAC,GAAGD,CAAC,GAAI,EAAE,CAAC,IAAID,EAAEE,EAAE,EAAE,MAAMF,EAAE,EAAE,QAAQ,IAAIE,CAAC,GAAG,IAAI,CAAC,IAAME,EAAEE,EAAE,EAAEN,CAAC,EAAEG,EAAE,KAAK,CAAC,MAAMC,EAAE,OAAOJ,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,UAAU,EAAEA,CAAC,CAAC,IAAI,OAAI,OAAOI,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,UAAUJ,EAAEI,EAAE,MAAM,CAAC,GAAGF,EAAEF,EAAEI,EAAE,MAAM,CAAC,CAAC,IAAMA,EAAEG,EAAE,EAAE,CAAC,EAAEG,EAAE,IAAI,IAAI,QAAUV,KAAKG,EAAE,QAAQ,EAAE,QAAUF,KAAKG,EAAE,CAAC,GAAK,CAAC,OAAOF,EAAE,MAAMC,CAAC,EAAEH,EAAE,GAAG,EAAEC,EAAE,IAAI,CAAC,GAAGC,GAAGA,EAAEC,EAAE,QAAQF,EAAE,IAAI,CAAC,GAAG,SAAS,GAAK,CAAC,QAAQG,CAAC,EAAEH,EAAEI,EAAEH,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,QAAQG,EAAE,MAAM,EAAEC,CAAC,EAAEF,EAAEC,EAAE,MAAMC,EAAEF,EAAE,MAAM,EAAEF,EAAE,UAAUG,GAAGM,EAAE,IAAIT,CAAC,CAAC,CAAC,QAAUD,KAAKU,EAAE,CAAC,IAAMT,EAAEI,EAAEL,EAAE,IAAI,EAAE,GAAG,CAACC,EAAE,MAAM,IAAI,MAAM,uBAAuBD,EAAE,IAAI,EAAE,EAAEC,EAAE,UAAU,EAAE,IAAMC,EAAED,EAAE,KAAKD,EAAE,OAAO,EAAE,GAAG,CAACE,EAAE,MAAM,IAAI,MAAM,+BAA+BF,EAAE,IAAI,KAAKA,EAAE,OAAO,EAAE,EAAE,OAAO,OAAOA,EAAEE,EAAE,MAAM,CAAC,CAAC,OAAOE,CAAC,CAAC,SAASO,EAAEX,EAAE,CAAC,KAAKC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAGA,GAAGD,EAAE,KAAM,GAAa,EAAE,OAAZ,OAAiB,EAAE,CAAC,IAAMC,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAEF,EAAE,OAAO,IAAI,GAAaA,EAAE,CAAC,EAAE,OAAf,QAAoB,CAAC,GAAOE,EAAE,SAAN,EAAa,MAAM,IAAI,MAAM,sBAAsB,CAAC,EAAED,EAAE,KAAKU,EAAET,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAEA,EAAE,OAAO,CAAC,MAAMA,EAAE,KAAKF,EAAE,CAAC,CAAC,EAAE,GAAOE,EAAE,SAAN,EAAa,MAAM,IAAI,MAAM,gBAAgB,EAAE,OAAOD,EAAE,KAAKU,EAAET,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,KAAKD,CAAC,CAAC,CAAC,QAAQA,EAAED,EAAE,OAAO,EAAEC,GAAG,EAAEA,IAAI,CAAC,IAAIC,EAAEF,EAAEC,CAAC,EAAE,GAAkBC,EAAE,OAAjB,aAAsB,CAAC,IAAI,EAAEF,EAAE,MAAM,EAAEC,CAAC,EAAE,EAAED,EAAE,MAAMC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,UAAU,WAAWC,EAAE,QAAQ,KAAKS,EAAE,CAAC,EAAE,MAAMA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOX,EAAE,OAAO,CAAC,IAAK,GAAE,MAAM,IAAI,MAAM,sBAAsB,EAAE,IAAK,GAAE,OAAOA,EAAE,CAAC,EAAE,QAAQ,MAAM,CAAC,KAAK,WAAW,KAAK,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAEA,EAAEC,EAAE,CAAC,OAAOD,EAAE,KAAK,CAAC,IAAI,OAAO,QAAQC,KAAKD,EAAE,KAAK,MAAM,EAAEC,EAAED,CAAC,EAAE,MAAM,IAAI,UAAU,MAAM,EAAEA,EAAE,KAAKA,CAAC,EAAE,MAAM,EAAEA,EAAE,MAAMA,CAAC,EAAE,MAAM,IAAI,WAAW,MAAMA,EAAE,KAAK,IAAKC,GAAG,CAACA,EAAED,CAAC,CAAE,EAAE,MAAM,QAAQ,KAAK,CAACA,EAAEC,CAAC,CAAC,CAAC,CAAuD,SAASW,EAAEC,EAAE,CAAC,UAAUC,EAAE,GAAG,KAAKC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAM,EAAEC,EAAEH,CAAC,EAAE,GAAG,CAAC,EAAE,OAAO,IAAMI,EAAEC,EAAE,EAAE,CAAC,KAAKH,CAAC,CAAC,EAAE,GAAG,CAACD,EAAE,OAAOG,EAAE,OAAS,CAACJ,CAAC,IAAI,EAAEI,CAAC,EAAE,CAAkD,GAA7BJ,EAAE,OAAnB,gBAAyB,CAACA,EAAE,UAAqB,CAACM,EAAE,IAAIN,EAAE,IAAI,EAAE,SAAS,IAAIC,EAAED,EAAE,SAAeE,EAAEK,EAAEP,EAAE,IAAI,EAAE,GAAGE,EAAE,CAAC,IAAMI,EAAEJ,EAAE,KAAKD,CAAC,EAAE,GAAG,CAACK,EAAE,SAAS,OAAO,OAAON,EAAEM,EAAE,MAAM,EAAEL,EAAEK,EAAE,OAAO,OAAO,CAACL,GAAG,OAAO,OAAOD,EAAE,CAAC,QAAQD,EAAEE,EAAE,CAAC,UAAU,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAOG,CAAC,CAA0G,SAASI,EAAEC,EAAEC,EAAE,CAAC,OAAOA,EAAEA,GAAG,KAAK,IAAI,GAAGD,CAAC,EAAE,EAAEA,EAAE,CAAC,GAAGC,GAAG,GAAGD,EAAE,CAAC,EAAEC,EAAED,EAAE,CAAC,CAAC,CAAC,SAASE,EAAEF,EAAE,CAAC,IAAIC,EAAED,EAAE,GAAa,OAAOC,GAAjB,WAAqBA,EAAEE,EAAEF,EAAE,CAAC,UAAU,EAAE,CAAC,GAAG,CAACA,EAAE,MAAM,CAAC,EAAE,GAAYA,EAAE,OAAX,QAAiB,SAASA,EAAE,CAAC,IAAID,EAAE,GAASI,EAAEH,EAAE,KAAK,IAAKA,GAAG,CAAC,IAAMG,EAAEF,EAAED,CAAC,EAAE,OAAOD,EAAE,KAAK,IAAIA,EAAE,GAAGE,EAAED,CAAC,CAAC,EAAEG,CAAC,CAAE,EAAEC,EAAED,EAAE,IAAKH,GAAGF,EAAEE,EAAED,CAAC,CAAE,EAAE,OAAOI,EAAEC,EAAE,QAAQ,KAAK,IAAI,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,IAAMA,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAS,CAACL,CAAC,IAAI,EAAEC,CAAC,EAAE,OAAOD,EAAE,KAAK,CAAC,IAAI,KAAKK,EAAE,CAAC,IAAI,MAAM,IAAI,QAAQ,IAAI,YAAYA,EAAE,CAAC,IAAI,MAAM,IAAI,iBAAiB,IAAI,OAAOA,EAAE,CAAC,IAAI,MAAM,IAAI,eAAe,GAAaL,EAAE,OAAZ,QAAiB,MAAM,GAAG,CAACI,EAAE,IAAIJ,EAAE,IAAI,GAAG,CAACA,EAAE,QAAQ,CAACK,EAAE,CAAC,IAAI,KAAK,CAACH,EAAEF,EAAE,OAAO,EAAE,QAAS,CAACA,EAAEC,IAAII,EAAEJ,CAAC,GAAGD,CAAE,EAAgBA,EAAE,OAAhB,aAAyCA,EAAE,OAArB,kBAA2BK,EAAE,CAAC,GAAG,CAAC,OAAOA,CAAC,CDS9iJ,SAASC,EAAYC,EAAkB,CAC7C,OAAOC,EAAoBC,EAAeF,CAAQ,EAAG,EAAE,CACxD,CAEO,SAASG,EAAQC,EAAYJ,EAA2B,CAE9D,OADcK,EAAgBL,CAAQ,EACzBI,EAAMA,EAAK,OAAQE,EAAcF,EAAMA,EAAK,MAAM,CAAC,CACjE,CAEO,SAASG,EAAcH,EAAYJ,EAAwB,CACjE,IAAMQ,EAAQH,EAAgBL,CAAQ,EACtC,GAAI,CACH,OAAOS,EACNL,EACA,CAACM,EAASC,EAAeC,IAAmB,CAC3C,IAAIC,EAAIL,EAAME,EAAGC,EAAQC,CAAK,EAC9B,OAAKC,GAAU,EAEhB,EACA,CAAE,OAAQ,EAAK,CAChB,EAAE,CAAC,CACJ,OAASC,EAAG,CACX,GAAIA,aAAa,MAChB,MAAMA,EAEP,OAAOA,CACR,CACD,CAEO,SAASC,EAAiBX,EAAYJ,EAA0B,CACtE,IAAMQ,EAAQH,EAAgBL,CAAQ,EACtC,OAAOS,EAAOL,EAAM,CAACM,EAASC,EAAeC,IAAmB,CAC/D,IAAIC,EAAIL,EAAME,EAAGC,EAAQC,CAAK,EAC9B,OAAKC,GAAU,EAEhB,CAAC,CACF,CAEA,IAAOG,EAAQD,EAMf,SAASN,EACRL,EACAI,EACAS,EAA6B,CAAE,OAAQ,EAAM,EACpC,CACT,IAAIC,EAAgB,CAAC,EACrB,OAAAC,EAASf,EAAM,CAACM,EAAGC,EAAQC,IAAgB,CAC1C,GAAI,EAAAF,GAAKA,EAAE,OAASU,IAChBZ,EAAME,EAAGC,EAAQC,CAAK,EAAG,CAC5B,GAAIK,EAAK,OAAQ,MAAMP,EACvBQ,EAAM,KAAKR,CAAC,CACb,CACD,CAAC,EACMQ,CACR,CAEA,IAAMG,EAAqBrB,GAA6B,CACvD,GAAM,CAAE,SAAAsB,EAAW,GAAI,EAAItB,EAC3B,OAAQsB,EAAU,CACjB,IAAK,IACJ,MAAO,CAACC,EAAWC,IAAcD,IAAMC,EACxC,IAAK,KACJ,MAAO,CAACD,EAAWC,IAAcD,EAAE,MAAM,MAAM,EAAE,SAASC,CAAC,EAC5D,IAAK,KACJ,MAAO,CAACD,EAAWC,IAAcD,EAAE,WAAWC,EAAI,GAAG,EACtD,IAAK,KACJ,MAAO,CAACD,EAAWC,IAAcD,EAAE,QAAQC,CAAC,EAAI,GACjD,IAAK,KACJ,MAAO,CAACD,EAAWC,IAAcD,EAAE,SAASC,CAAC,EAC9C,IAAK,KACJ,MAAO,CAACD,EAAWC,IAAcD,EAAE,WAAWC,CAAC,CACjD,CACA,MAAO,CAACD,EAAWC,IAAc,EAClC,EAEMlB,EAAgB,CAACF,EAAYO,IAClCA,GAAA,YAAAA,EAAQ,SACN,OAAQD,GAAYA,EAAE,OAASU,GAC/B,UAAWV,GAAYA,IAAMN,GAC1BqB,EAAYC,GAAoB,CACrC,GAAI,CAACC,EAAGC,EAAI,IAAKC,EAAI,GAAG,EACvB,8CAA8C,KAAKH,CAAO,GAAK,CAAC,EAC7DE,EAAE,SAAW,IAAGA,EAAI,KACxB,IAAML,EAAI,OAAO,SAASK,IAAM,IAAM,KAAOA,CAAC,EACxCJ,EAAI,OAAO,SAASK,CAAC,EAC3B,OAAQnB,GAAca,EAAIb,EAAIc,CAC/B,EACMM,EAAY,CAAC1B,EAAYO,KAC9BA,GAAA,YAAAA,EAAQ,SAAS,OAAQD,GAAYA,EAAE,OAASU,GAAc,SAAUhB,EACnE2B,EAAa,CAAC3B,EAAYO,KAC/BA,GAAA,YAAAA,EAAQ,SAAS,OAAQD,GAAYA,EAAE,OAASU,GAAc,WAC9DhB,EACK4B,EAAY,CAAC5B,EAAYO,KAC9BA,GAAA,YAAAA,EAAQ,SAAS,OAAQD,GAAYA,EAAE,OAASU,GAAc,UAAW,EAEpEa,EAAejC,GAA2B,CAC/C,OAAQA,EAAS,KAAM,CACtB,IAAK,OACJ,OAAQI,GACHJ,EAAS,UAAY,IAAY,GAC9BI,EAAK,OAASJ,EAAS,KAEhC,IAAK,QACJ,OAAQI,GAAY,CApHvB,IAAA8B,EAAAC,EAqHI,OAAAA,GAAAD,EAAA9B,EAAK,aAAL,YAAA8B,EAAiB,QAAjB,YAAAC,EAAwB,MAAM,QAAQ,SAASnC,EAAS,OAC1D,IAAK,KACJ,OAAQI,GAAY,CAvHvB,IAAA8B,EAuH0B,QAAAA,EAAA9B,EAAK,aAAL,YAAA8B,EAAiB,MAAOlC,EAAS,MACzD,IAAK,eACJ,OAAQA,EAAS,KAAM,CACtB,IAAK,SACJ,MAAO,IAAIoC,IACV/B,EAAgBQ,EAAMb,EAAS,QAAS,CAAE,EAAE,GAAGoC,CAAI,EACrD,IAAK,MACJ,MAAO,IAAIA,IAAS,CAACH,EAAYjC,EAAS,OAAQ,EAAE,GAAGoC,CAAI,EAC5D,IAAK,KACJ,MAAO,IAAIA,IAAS/B,EAAgBL,EAAS,OAAQ,EAAE,GAAGoC,CAAI,EAC/D,IAAK,QACJ,MAAO,IAAIA,IAAS/B,EAAgBL,EAAS,OAAQ,EAAE,GAAGoC,CAAI,EAC/D,IAAK,OACJ,MAAO,CAAChC,EAAYO,IACnBP,EAAK,OAASgB,GAAgBhB,EAAK,OAAS,OAC9C,IAAK,QACJ,OAAQA,GACPA,EAAK,OAASgB,IACbhB,EAAK,SAAS,SAAW,GACzBA,EAAK,SAAS,MACZM,GAAYA,EAAE,OAAS2B,GAAa3B,EAAE,MAAM,KAAK,IAAM,EACzD,GACH,IAAK,cACJ,MAAO,CAACN,EAAYO,IAAkBoB,EAAW3B,EAAMO,CAAM,EAC9D,IAAK,aACJ,MAAO,CAACP,EAAYO,IAAkBmB,EAAU1B,EAAMO,CAAM,EAC7D,IAAK,aACJ,MAAO,CAACP,EAAYO,IAAkBqB,EAAU5B,EAAMO,CAAM,EAC7D,IAAK,YACJ,MAAO,CAACP,EAAYO,IAAkB,CACrC,IAAM2B,EAAShC,EAAcF,EAAMO,CAAM,EAAI,EAC7C,GAAI,OAAO,MAAM,OAAOX,EAAS,QAAQ,CAAC,EACzC,OAAQA,EAAS,SAAU,CAC1B,IAAK,MACJ,OAAO,KAAK,IAAIsC,EAAS,CAAC,GAAK,EAChC,IAAK,OACJ,OAAOA,EAAS,IAAM,EACvB,QAAS,CACR,GAAI,CAACtC,EAAS,SACb,MAAM,IAAI,MAAM,uCAAuC,EAExD,IAAMuC,EAAMd,EAASzB,EAAS,QAAQ,EAChCwC,EAAW7B,GAAA,YAAAA,EAAQ,SAAS,OAChCD,GAAYA,EAAE,OAASU,GAEnBqB,EAAanC,EAAcF,EAAMO,CAAM,EAAI,EACjD,QAAS+B,EAAI,EAAGA,EAAIF,EAAS,OAAQE,IAAK,CACzC,IAAIhC,EAAI6B,EAAIG,CAAC,EACb,GAAIhC,EAAI8B,EAAS,OAAQ,MAAO,GAChC,GAAI9B,IAAM+B,EAAY,MAAO,EAC9B,CACA,MAAO,EACR,CACD,CAED,OAAOH,IAAW,OAAOtC,EAAS,QAAQ,CAC3C,EACD,QACC,MAAM,IAAI,MAAM,2BAA2BA,EAAS,IAAI,GAAG,CAC7D,CAED,IAAK,YACJ,OAAQI,GAAe,CACtB,GAAI,CAAE,cAAAuC,EAAe,KAAAC,EAAM,MAAAC,CAAM,EAAI7C,EACrC,GAAI,CAACI,EAAK,WAAY,MAAO,GAC7B,IAAM0C,EAAQ,OAAO,QAAQ1C,EAAK,UAAoC,EACtE,OAAS,CAAC2C,EAAMC,CAAO,IAAKF,EAK3B,GAJIH,IAAkB,MACrBE,EAAQD,EAAK,YAAY,EACzBI,EAAUD,EAAK,YAAY,GAExBA,IAASH,EACb,IAAI,CAACC,EAAO,MAAO,GAOnB,IALEA,EAAM,CAAC,IAAM,KAAOA,EAAM,CAAC,IAAM,MAClCA,EAAM,CAAC,IAAMA,EAAMA,EAAM,OAAS,CAAC,IAEnCA,EAAQ,KAAK,MAAMA,CAAK,GAErBA,EACH,OAAOxB,EAAkBrB,CAAQ,EAAEgD,EAASH,CAAK,EAGnD,MAAO,EACR,EACD,IAAK,YACJ,OAAQlB,GACA,GAET,QACC,MAAM,IAAI,MAAM,uBAAuB3B,EAAS,IAAI,EAAE,CAExD,CACD,EAEMK,EAAmB4C,GAA+B,CACvD,IAAIjD,EAAW,OAAOiD,GAAQ,SAAWpC,EAAMoC,CAAG,EAAIA,EACtD,OAAQjD,GAAA,YAAAA,EAAU,KAAM,CACvB,IAAK,OAAQ,CACZ,IAAMkD,EAAWlD,EAAS,KAAK,IAAKmD,GAAWlB,EAAYkB,CAAC,CAAC,EAC7D,MAAO,CAAC/C,EAAYO,EAAeC,IAAmB,CACrD,QAAWJ,KAAS0C,EACnB,GAAI1C,EAAMJ,EAAMO,CAAO,EAAG,MAAO,GAElC,MAAO,EACR,CACD,CACA,IAAK,WAAY,CAChB,IAAMuC,EAAWlD,EAAS,KAAK,IAAKmD,GAAWlB,EAAYkB,CAAC,CAAC,EAC7D,MAAO,CAAC/C,EAAYO,EAAeC,IAAmB,CACrD,QAAWJ,KAAS0C,EACnB,GAAI,CAAC1C,EAAMJ,EAAMO,CAAO,EAAG,MAAO,GAEnC,MAAO,EACR,CACD,CACA,IAAK,UAAW,CACf,GAAM,CAAE,KAAAyC,EAAM,MAAAC,EAAO,WAAAC,CAAW,EAAItD,EAC9BuD,EAAYlD,EAAgB+C,CAAI,EAChCI,EAAanD,EAAgBgD,CAAK,EACpCI,EAAc,IAAI,QACtB,MAAO,CAACrD,EAAYO,EAAe+B,EAAY,IAAM,CAMpD,IALIa,EAAUnD,CAAI,GAEPO,GAAU8C,EAAY,IAAI9C,CAAM,GAAK2C,IAAe,MAC9DG,EAAY,IAAIrD,CAAI,EAEjB,CAACoD,EAAWpD,CAAI,EAAG,MAAO,GAC9B,OAAQkD,EAAY,CACnB,IAAK,IACL,IAAK,IACJ,OAAO3C,EAAS8C,EAAY,IAAI9C,CAAM,EAAI,GAC3C,IAAK,IAAK,CACT,GAAI,CAACA,EAAQ,MAAO,GACpB,QAAS+C,KAAW/C,EAAO,SAAS,MAAM,EAAG+B,CAAC,EAC7C,GAAIe,EAAY,IAAIC,CAAO,EAAG,MAAO,GAEtC,MAAO,EACR,CACA,IAAK,IAAK,CACT,GAAI,CAAC/C,EAAQ,MAAO,GACpB,IAAIgD,EAAehD,EAAO,SACxB,MAAM,EAAG+B,CAAC,EACV,OAAQkB,GAAaA,EAAG,OAASxC,CAAY,EAC/C,GAAIuC,EAAa,SAAW,EAAG,MAAO,GACtC,IAAME,EAAOF,EAAaA,EAAa,OAAS,CAAC,EACjD,GAAI,CAACE,EAAM,MAAO,GAClB,GAAIJ,EAAY,IAAII,CAAI,EAAG,MAAO,EACnC,CACA,QACC,MAAO,EACT,CACD,CACD,CACA,QACC,OAAO5B,EAAYjC,CAAS,CAC9B,CACD", "names": ["ELEMENT_NODE", "TEXT_NODE", "walkSync", "e", "t", "n", "s", "o", "r", "c", "i", "a", "l", "u", "f", "m", "e", "t", "s", "u", "c", "f", "n", "o", "d", "e", "t", "w", "m", "n", "s", "specificity", "selector", "d", "w", "matches", "node", "selectorToMatch", "nthChildIndex", "querySelector", "match", "select", "n", "parent", "index", "m", "e", "querySelectorAll", "selector_default", "opts", "nodes", "walkSync", "ELEMENT_NODE", "getAttributeMatch", "operator", "a", "b", "nthChild", "formula", "_", "A", "B", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "createMatch", "_a", "_b", "args", "TEXT_NODE", "target", "nth", "elements", "childIndex", "i", "caseSensitive", "name", "value", "attrs", "attr", "attrVal", "sel", "matchers", "s", "left", "right", "combinator", "matchLeft", "matchRight", "leftMatches", "sibling", "prevSiblings", "el", "prev"]}