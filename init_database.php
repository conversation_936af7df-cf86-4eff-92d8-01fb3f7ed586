<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 数据库初始化脚本
 */

echo "=== QiyeDIY数据库初始化 ===\n\n";

// 读取配置
$config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'username' => 'root',
    'password' => '123456',
    'database' => 'qiyediy',
    'charset' => 'utf8mb4'
];

try {
    echo "1. 连接MySQL服务器...\n";
    
    // 连接MySQL服务器（不指定数据库）
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]
    );
    
    echo "✅ MySQL连接成功\n\n";
    
    echo "2. 检查数据库是否存在...\n";
    
    // 检查数据库是否存在
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([$config['database']]);
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "⚠️  数据库 '{$config['database']}' 已存在\n";
        echo "是否要重新创建数据库？这将删除所有现有数据！(y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) === 'y') {
            echo "正在删除现有数据库...\n";
            $pdo->exec("DROP DATABASE `{$config['database']}`");
            echo "✅ 数据库已删除\n";
        } else {
            echo "跳过数据库创建\n\n";
            goto connect_database;
        }
    }
    
    echo "3. 创建数据库...\n";
    $pdo->exec("CREATE DATABASE `{$config['database']}` CHARACTER SET {$config['charset']} COLLATE {$config['charset']}_unicode_ci");
    echo "✅ 数据库 '{$config['database']}' 创建成功\n\n";
    
    connect_database:
    echo "4. 连接到目标数据库...\n";
    
    // 重新连接到目标数据库
    $pdo = new PDO(
        "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]
    );
    
    echo "✅ 已连接到数据库 '{$config['database']}'\n\n";
    
    echo "5. 检查SQL文件...\n";
    $sqlFile = __DIR__ . '/backend/database/qiyediy.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: {$sqlFile}");
    }
    
    echo "✅ SQL文件存在: " . basename($sqlFile) . "\n\n";
    
    echo "6. 执行SQL脚本...\n";
    $sql = file_get_contents($sqlFile);
    
    if (empty($sql)) {
        throw new Exception("SQL文件为空");
    }
    
    // 分割SQL语句
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        try {
            if (trim($statement)) {
                $pdo->exec($statement);
                $successCount++;
                
                // 显示执行的语句类型
                if (preg_match('/^(CREATE TABLE|INSERT INTO|DROP TABLE)/i', trim($statement), $matches)) {
                    echo "   ✅ " . strtoupper($matches[1]) . " 执行成功\n";
                }
            }
        } catch (PDOException $e) {
            $errorCount++;
            echo "   ❌ SQL执行错误: " . $e->getMessage() . "\n";
            echo "   语句: " . substr($statement, 0, 100) . "...\n";
        }
    }
    
    echo "\n📊 执行结果:\n";
    echo "   成功: {$successCount} 条语句\n";
    echo "   失败: {$errorCount} 条语句\n\n";
    
    echo "7. 验证数据库结构...\n";
    
    // 检查表是否创建成功
    $tables = [
        'qd_users' => '用户表',
        'qd_roles' => '角色表',
        'qd_permissions' => '权限表',
        'qd_diy_pages' => 'DIY页面表',
        'qd_diy_components' => 'DIY组件表',
        'qd_diy_templates' => 'DIY模板表',
        'qd_contents' => '内容表',
        'qd_media_files' => '媒体文件表'
    ];
    
    foreach ($tables as $table => $description) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            // 获取表的记录数
            $countStmt = $pdo->query("SELECT COUNT(*) as count FROM `{$table}`");
            $count = $countStmt->fetch()['count'];
            echo "   ✅ {$description} ({$table}) - {$count} 条记录\n";
        } else {
            echo "   ❌ {$description} ({$table}) - 不存在\n";
        }
    }
    
    echo "\n8. 验证初始数据...\n";
    
    // 检查管理员账号
    $stmt = $pdo->prepare("SELECT username, email FROM qd_users WHERE id = 1");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "   ✅ 管理员账号: {$admin['username']} ({$admin['email']})\n";
        echo "   🔑 默认密码: admin123\n";
    } else {
        echo "   ❌ 管理员账号不存在\n";
    }
    
    // 检查角色数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_roles");
    $roleCount = $stmt->fetch()['count'];
    echo "   ✅ 系统角色: {$roleCount} 个\n";
    
    // 检查权限数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_permissions");
    $permissionCount = $stmt->fetch()['count'];
    echo "   ✅ 系统权限: {$permissionCount} 个\n";
    
    // 检查组件数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_diy_components");
    $componentCount = $stmt->fetch()['count'];
    echo "   ✅ DIY组件: {$componentCount} 个\n";
    
    // 检查模板数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM qd_diy_templates");
    $templateCount = $stmt->fetch()['count'];
    echo "   ✅ DIY模板: {$templateCount} 个\n";
    
    echo "\n=== 数据库初始化完成 ===\n";
    echo "🎉 QiyeDIY数据库已成功初始化！\n\n";
    
    echo "📋 登录信息:\n";
    echo "   用户名: admin\n";
    echo "   邮箱: <EMAIL>\n";
    echo "   密码: admin123\n\n";
    
    echo "🔧 下一步操作:\n";
    echo "   1. 启动后端服务: cd backend && php think run\n";
    echo "   2. 启动管理后台: cd admin && npm run dev\n";
    echo "   3. 启动前端展示: cd frontend && npm run dev\n";
    echo "   4. 访问管理后台: http://localhost:5173\n\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "请检查数据库配置和连接信息\n";
    exit(1);
}
