{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#app": ["../node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/nuxt/dist/app/compat/vue-demi"], "#image": ["../node_modules/@nuxt/image/dist/runtime"], "#image/*": ["../node_modules/@nuxt/image/dist/runtime/*"], "pinia": ["../node_modules/pinia/dist/pinia"], "#vue-router": ["../node_modules/vue-router"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "#imports": ["./imports"], "#app-manifest": ["./manifest/meta/dev"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": false, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../**/*", "../.config/nuxt.*", "./nuxt.d.ts", "../node_modules/@nuxtjs/tailwindcss/runtime", "../node_modules/@nuxtjs/tailwindcss/dist/runtime", "../node_modules/@nuxt/image/runtime", "../node_modules/@nuxt/image/dist/runtime", "../node_modules/@pinia/nuxt/runtime", "../node_modules/@pinia/nuxt/dist/runtime", "../node_modules/@vueuse/nuxt/runtime", "../node_modules/@vueuse/nuxt/dist/runtime", "../node_modules/@nuxtjs/google-fonts/runtime", "../node_modules/@nuxtjs/google-fonts/dist/runtime", "../node_modules/@nuxt/devtools/runtime", "../node_modules/@nuxt/devtools/dist/runtime", "../node_modules/@nuxt/telemetry/runtime", "../node_modules/@nuxt/telemetry/dist/runtime", ".."], "exclude": ["../dist", "../.data", "../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/@nuxt/image/node_modules", "../node_modules/@pinia/nuxt/node_modules", "../node_modules/@vueuse/nuxt/node_modules", "../node_modules/@nuxtjs/google-fonts/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/@nuxtjs/tailwindcss/dist/runtime/server", "../node_modules/@nuxt/image/runtime/server", "../node_modules/@nuxt/image/dist/runtime/server", "../node_modules/@pinia/nuxt/runtime/server", "../node_modules/@pinia/nuxt/dist/runtime/server", "../node_modules/@vueuse/nuxt/runtime/server", "../node_modules/@vueuse/nuxt/dist/runtime/server", "../node_modules/@nuxtjs/google-fonts/runtime/server", "../node_modules/@nuxtjs/google-fonts/dist/runtime/server", "../node_modules/@nuxt/devtools/runtime/server", "../node_modules/@nuxt/devtools/dist/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "../node_modules/@nuxt/telemetry/dist/runtime/server", "../.output"]}