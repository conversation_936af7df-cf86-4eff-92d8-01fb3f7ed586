var F={attribute:/\[\s*(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)\s*(?:(?<operator>\W?=)\s*(?<value>.+?)\s*(\s(?<caseSensitive>[iIsS]))?\s*)?\]/gu,id:/#(?<name>[-\w\P{ASCII}]+)/gu,class:/\.(?<name>[-\w\P{ASCII}]+)/gu,comma:/\s*,\s*/g,combinator:/\s*[\s>+~]\s*/g,"pseudo-element":/::(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,"pseudo-class":/:(?<name>[-\w\P{ASCII}]+)(?:\((?<argument>¶*)\))?/gu,universal:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?\*/gu,type:/(?:(?<namespace>\*|[-\w\P{ASCII}]*)\|)?(?<name>[-\w\P{ASCII}]+)/gu},kr=new Set(["combinator","comma"]),Ir=new Set(["not","is","where","has","matches","-moz-any","-webkit-any","nth-child","nth-last-child"]),ar=/(?<index>[\dn+-]+)\s+of\s+(?<subtree>.+)/,Or={"nth-child":ar,"nth-last-child":ar},Tr=r=>{switch(r){case"pseudo-element":case"pseudo-class":return new RegExp(F[r].source.replace("(?<argument>\xB6*)","(?<argument>.*)"),"gu");default:return F[r]}};function Cr(r,e){let t=0,n="";for(;e<r.length;e++){let s=r[e];switch(s){case"(":++t;break;case")":--t}if(n+=s,t===0)return n}return n}function Nr(r,e=F){if(!r)return[];let t=[r];for(let[s,a]of Object.entries(e))for(let i=0;i<t.length;i++){let o=t[i];if(typeof o!="string")continue;a.lastIndex=0;let c=a.exec(o);if(!c)continue;let p=c.index-1,l=[],h=c[0],S=o.slice(0,p+1);S&&l.push(S),l.push({...c.groups,type:s,content:h});let d=o.slice(p+h.length+1);d&&l.push(d),t.splice(i,1,...l)}let n=0;for(let s of t)switch(typeof s){case"string":throw new Error(`Unexpected sequence ${s} found at index ${n}`);case"object":n+=s.content.length,s.pos=[n-s.content.length,n],kr.has(s.type)&&(s.content=s.content.trim()||" ")}return t}var Rr=/(['"])([^\\\n]+?)\1/g,Mr=/\\./g;function $r(r,e=F){if((r=r.trim())==="")return[];let t=[];r=(r=r.replace(Mr,(a,i)=>(t.push({value:a,offset:i}),"\uE000".repeat(a.length)))).replace(Rr,(a,i,o,c)=>(t.push({value:a,offset:c}),`${i}${"\uE001".repeat(o.length)}${i}`));{let a,i=0;for(;(a=r.indexOf("(",i))>-1;){let o=Cr(r,a);t.push({value:o,offset:a}),r=`${r.substring(0,a)}(${"\xB6".repeat(o.length-2)})${r.substring(a+o.length)}`,i=a+o.length}}let n=Nr(r,e),s=new Set;for(let a of t.reverse())for(let i of n){let{offset:o,value:c}=a;if(!(i.pos[0]<=o&&o+c.length<=i.pos[1]))continue;let{content:p}=i,l=o-i.pos[0];i.content=p.slice(0,l)+c+p.slice(l+c.length),i.content!==p&&s.add(i)}for(let a of s){let i=Tr(a.type);if(!i)throw new Error(`Unknown token type: ${a.type}`);i.lastIndex=0;let o=i.exec(a.content);if(!o)throw new Error(`Unable to parse content for ${a.type}: ${a.content}`);Object.assign(a,o.groups)}return n}function L(r,{list:e=!0}={}){if(e&&r.find(t=>t.type==="comma")){let t=[],n=[];for(let s=0;s<r.length;s++)if(r[s].type==="comma"){if(n.length===0)throw new Error("Incorrect comma at "+s);t.push(L(n,{list:!1})),n.length=0}else n.push(r[s]);if(n.length===0)throw new Error("Trailing comma");return t.push(L(n,{list:!1})),{type:"list",list:t}}for(let t=r.length-1;t>=0;t--){let n=r[t];if(n.type==="combinator"){let s=r.slice(0,t),a=r.slice(t+1);return{type:"complex",combinator:n.content,left:L(s),right:L(a)}}}switch(r.length){case 0:throw new Error("Could not build AST.");case 1:return r[0];default:return{type:"compound",list:[...r]}}}function*D(r,e){switch(r.type){case"list":for(let t of r.list)yield*D(t,r);break;case"complex":yield*D(r.left,r),yield*D(r.right,r);break;case"compound":yield*r.list.map(t=>[t,r]);break;default:yield[r,e]}}function J(r,{recursive:e=!0,list:t=!0}={}){let n=$r(r);if(!n)return;let s=L(n,{list:t});if(!e)return s;for(let[a]of D(s)){if(a.type!=="pseudo-class"||!a.argument||!Ir.has(a.name))continue;let i=a.argument,o=Or[a.name];if(o){let c=o.exec(i);if(!c)continue;Object.assign(a,c.groups),i=c.groups.subtree}i&&Object.assign(a,{subtree:J(i,{recursive:!0,list:!0})})}return s}var K="comm",Y="rule",V="decl";var ir="@import";var cr="@keyframes";var ur="@layer";var Q=Math.abs,z=String.fromCharCode;function W(r){return r.trim()}function N(r,e,t){return r.replace(e,t)}function fr(r,e,t){return r.indexOf(e,t)}function T(r,e){return r.charCodeAt(e)|0}function A(r,e,t){return r.slice(e,t)}function g(r){return r.length}function B(r){return r.length}function R(r,e){return e.push(r),r}var G=1,M=1,pr=0,b=0,f=0,P="";function Z(r,e,t,n,s,a,i,o){return{value:r,root:e,parent:t,type:n,props:s,children:a,line:G,column:M,length:i,return:"",siblings:o}}function lr(){return f}function hr(){return f=b>0?T(P,--b):0,M--,f===10&&(M=1,G--),f}function w(){return f=b<pr?T(P,b++):0,M++,f===10&&(M=1,G++),f}function k(){return T(P,b)}function _(){return b}function q(r,e){return A(P,r,e)}function $(r){switch(r){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function mr(r){return G=M=1,pr=g(P=r),b=0,[]}function gr(r){return P="",r}function H(r){return W(q(b-1,rr(r===91?r+2:r===40?r+1:r)))}function xr(r){for(;(f=k())&&f<33;)w();return $(r)>2||$(f)>3?"":" "}function br(r,e){for(;--e&&w()&&!(f<48||f>102||f>57&&f<65||f>70&&f<97););return q(r,_()+(e<6&&k()==32&&w()==32))}function rr(r){for(;w();)switch(f){case r:return b;case 34:case 39:r!==34&&r!==39&&rr(f);break;case 40:r===41&&rr(r);break;case 92:w();break}return b}function wr(r,e){for(;w()&&r+f!==57;)if(r+f===84&&k()===47)break;return"/*"+q(e,b-1)+"*"+z(r===47?r:w())}function dr(r){for(;!$(k());)w();return q(r,b)}function tr(r){return gr(X("",null,null,null,[""],r=mr(r),0,[0],r))}function X(r,e,t,n,s,a,i,o,c){for(var p=0,l=0,h=i,S=0,d=0,y=0,x=1,U=1,v=1,m=0,I="",C=s,O=a,E=n,u=I;U;)switch(y=m,m=w()){case 40:if(y!=108&&T(u,h-1)==58){fr(u+=N(H(m),"&","&\f"),"&\f",Q(p?o[p-1]:0))!=-1&&(v=-1);break}case 34:case 39:case 91:u+=H(m);break;case 9:case 10:case 13:case 32:u+=xr(y);break;case 92:u+=br(_()-1,7);continue;case 47:switch(k()){case 42:case 47:R(Ur(wr(w(),_()),e,t,c),c),($(y||1)==5||$(k()||1)==5)&&g(u)&&A(u,-1,void 0)!==" "&&(u+=" ");break;default:u+="/"}break;case 123*x:o[p++]=g(u)*v;case 125*x:case 59:case 0:switch(m){case 0:case 125:U=0;case 59+l:v==-1&&(u=N(u,/\f/g,"")),d>0&&(g(u)-h||x===0&&y===47)&&R(d>32?Sr(u+";",n,t,h-1,c):Sr(N(u," ","")+";",n,t,h-2,c),c);break;case 59:u+=";";default:if(R(E=Er(u,e,t,p,l,s,o,I,C=[],O=[],h,a),a),m===123)if(l===0)X(u,e,E,E,C,a,h,o,O);else switch(S===99&&T(u,3)===110?100:S){case 100:case 108:case 109:case 115:X(r,E,E,n&&R(Er(r,E,E,0,0,s,o,I,s,C=[],h,O),O),s,O,h,o,n?C:O);break;default:X(u,E,E,E,[""],O,0,o,O)}}p=l=d=0,x=v=1,I=u="",h=i;break;case 58:h=1+g(u),d=y;default:if(x<1){if(m==123)--x;else if(m==125&&x++==0&&hr()==125)continue}switch(u+=z(m),m*x){case 38:v=l>0?1:(u+="\f",-1);break;case 44:o[p++]=(g(u)-1)*v,v=1;break;case 64:k()===45&&(u+=H(w())),S=k(),l=h=g(I=u+=dr(_())),m++;break;case 45:y===45&&g(u)==2&&(x=0)}}return a}function Er(r,e,t,n,s,a,i,o,c,p,l,h){for(var S=s-1,d=s===0?a:[""],y=B(d),x=0,U=0,v=0;x<n;++x)for(var m=0,I=A(r,S+1,S=Q(U=i[x])),C=r;m<y;++m)(C=W(U>0?d[m]+" "+I:N(I,/&\f/g,d[m])))&&(c[v++]=C);return Z(r,e,t,s===0?Y:o,c,p,l,h)}function Ur(r,e,t,n){return Z(r,e,t,K,z(lr()),A(r,2,-2),0,n)}function Sr(r,e,t,n,s){return Z(r,e,t,V,A(r,0,n),A(r,n+1,-1),n,s)}function j(r,e){for(var t="",n=0;n<r.length;n++)t+=e(r[n],n,r,e)||"";return t}function yr(r,e,t,n){switch(r.type){case ur:if(r.children.length)break;case ir:case V:return r.return=r.return||r.value;case K:return"";case cr:return r.return=r.value+"{"+j(r.children,n)+"}";case Y:if(!g(r.value=r.props.join(",")))return""}return g(t=j(r.children,n))?r.return=r.value+"{"+t+"}":""}function er(r){var e=B(r);return function(t,n,s,a){for(var i="",o=0;o<e;o++)i+=r[o](t,n,s,a)||"";return i}}import{ELEMENT_NODE as nr,TEXT_NODE as Lr,render as zr,walkSync as vr}from"../index.js";import{matches as _r}from"../selector.js";function jr(r={}){return async e=>{let t=r.hash??Br(await zr(e)),n=[],s=!1,a=new Set,i=new Set;vr(e,o=>{if(o.type===nr&&o.name==="style"&&(!r.attribute||Fr(o,r.attribute))){s=!0,r.attribute&&delete o.attributes[r.attribute];for(let c of Vr(o.children[0].value))a.add(c)}o.type===nr&&i.add(o)}),s&&vr(e,o=>{o.type===nr&&(n.push(()=>Kr(o,t,a)),o.name==="style"&&n.push(()=>{o.children=o.children.map(c=>(c.type!==Lr||(c.value=Yr(c.value,t),c.value===""&&(o.parent.children=o.parent.children.filter(p=>p!==o))),c))}))});for(let o of n)o();return e}}var Dr=new Set(["base","font","frame","frameset","head","link","meta","noframes","noscript","script","style","title"]);function Fr(r,e){return e in r.attributes?r.attributes[e]!=="false":!1}function Kr(r,e,t){let{name:n}=r;if(n&&!(n.length<1)&&!Dr.has(n)&&!r.attributes["data-scope"]){for(let s of t)if(_r(r,s)){r.attributes["data-scope"]=e;return}}}function Ar(r,e){let t=J(r),n=s=>{switch(s.type){case"pseudo-class":return s.name==="root"?s.content:s.name==="global"?s.argument:`${s.content}:where([data-scope="${e}"])`;case"compound":return`${r}:where([data-scope="${e}"])`;case"complex":{let{left:a,right:i,combinator:o}=s;return`${n(a)}${o}${n(i)}`}case"list":return s.list.map(a=>n(a)).join(" ");default:return`${s.content}:where([data-scope="${e}"])`}};return n(t)}function Yr(r,e){return j(tr(r),er([t=>{t.type==="rule"&&(Array.isArray(t.props)?t.props=t.props.map(n=>Ar(n,e)):t.props=Ar(t.props,e))},yr]))}function Vr(r){let e=new Set;return j(tr(r),er([t=>{if(t.type==="rule")if(Array.isArray(t.props))for(let n of t.props)e.add(n);else e.add(t.props)}])),Array.from(e)}var or="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXY",sr=or.length;function Wr(r){let e=0;if(r.length===0)return e;for(let t=0;t<r.length;t++){let n=r.charCodeAt(t);e=(e<<5)-e+n,e=e&e}return e}function Br(r){let e,t="",n=Wr(r),s=n<0?"Z":"";for(n=Math.abs(n);n>=sr;)e=n%sr,n=Math.floor(n/sr),t=or[e]+t;return n>0&&(t=or[n]+t),s+t}export{jr as default};
/**
 * shorthash - https://github.com/bibig/node-shorthash
 *
 * @license
 *
 * (The MIT License)
 *
 * Copyright (c) 2013 Bibig <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person
 * obtaining a copy of this software and associated documentation
 * files (the "Software"), to deal in the Software without
 * restriction, including without limitation the rights to use,
 * copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following
 * conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 * HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 * WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 */
