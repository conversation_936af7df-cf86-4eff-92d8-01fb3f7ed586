@echo off
REM 三只鱼网络科技 | 韩总 | 2024-12-19
REM QiyeDIY企业建站系统 - 系统启动脚本

echo ========================================
echo   QiyeDIY企业建站系统 - 系统启动
echo ========================================
echo.

echo [1/4] 检查系统环境...
php --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PHP 未安装或未配置到环境变量
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或未配置到环境变量
    pause
    exit /b 1
)

echo ✅ PHP 和 Node.js 环境检查通过

echo.
echo [2/4] 启动后端API服务...
start "QiyeDIY-Backend" cmd /k "cd /d %~dp0backend && php think run -p 8000"
timeout /t 3 >nul

echo.
echo [3/4] 启动管理后台...
start "QiyeDIY-Admin" cmd /k "cd /d %~dp0admin && npm run dev"
timeout /t 5 >nul

echo.
echo [4/4] 启动前端展示...
start "QiyeDIY-Frontend" cmd /k "cd /d %~dp0frontend && npm run dev"
timeout /t 5 >nul

echo.
echo ========================================
echo   系统启动完成！
echo ========================================
echo.
echo 📋 访问地址：
echo   - 管理后台: http://localhost:5173
echo   - 前端展示: http://localhost:3000  
echo   - 后端API:  http://localhost:8000
echo.
echo 🔧 系统功能：
echo   - DIY可视化编辑器
echo   - 企业网站模板库
echo   - 用户权限管理系统
echo   - 响应式页面构建
echo.
echo ⚡ 等待服务启动完成...
timeout /t 10 >nul

echo.
echo 🎉 正在运行系统演示...
php system_demo.php

echo.
echo 📖 使用说明：
echo   1. 首次使用请访问管理后台进行初始化设置
echo   2. 在DIY编辑器中创建您的第一个页面
echo   3. 选择合适的模板开始构建网站
echo   4. 配置网站基本信息和SEO设置
echo.
echo 💡 技术支持：
echo   - 开发者：韩总 (三只鱼网络科技)
echo   - 技术栈：ThinkPHP8 + Vue3 + Nuxt3
echo   - 版本：v1.0.0 企业版
echo.
echo 按任意键打开管理后台...
pause >nul
start http://localhost:5173

echo.
echo 系统将继续在后台运行...
echo 关闭此窗口不会停止服务
echo.
pause
