@echo off
REM 三只鱼网络科技 | 韩总 | 2024-12-19
REM QiyeDIY企业建站系统 - 系统停止脚本

echo ========================================
echo   QiyeDIY企业建站系统 - 系统停止
echo ========================================
echo.

echo [1/3] 停止后端API服务...
taskkill /f /im php.exe >nul 2>&1
if errorlevel 1 (
    echo ⚠️  未找到运行中的PHP进程
) else (
    echo ✅ 后端API服务已停止
)

echo.
echo [2/3] 停止前端服务...
taskkill /f /im node.exe >nul 2>&1
if errorlevel 1 (
    echo ⚠️  未找到运行中的Node.js进程
) else (
    echo ✅ 前端服务已停止
)

echo.
echo [3/3] 清理临时文件...
if exist "%~dp0backend\runtime\temp\*" (
    del /q "%~dp0backend\runtime\temp\*" >nul 2>&1
    echo ✅ 后端临时文件已清理
)

if exist "%~dp0admin\.nuxt\*" (
    rmdir /s /q "%~dp0admin\.nuxt" >nul 2>&1
    echo ✅ 管理后台缓存已清理
)

if exist "%~dp0frontend\.nuxt\*" (
    rmdir /s /q "%~dp0frontend\.nuxt" >nul 2>&1
    echo ✅ 前端缓存已清理
)

echo.
echo ========================================
echo   系统已完全停止
echo ========================================
echo.
echo 📊 系统状态：
echo   - 后端API服务：已停止
echo   - 管理后台：已停止
echo   - 前端展示：已停止
echo   - 临时文件：已清理
echo.
echo 💡 重新启动系统请运行：start_system.bat
echo.
pause
