import { StatusResult } from '../../../typings';
export declare class StatusSummary implements StatusResult {
    not_added: never[];
    conflicted: never[];
    created: never[];
    deleted: never[];
    ignored: undefined;
    modified: never[];
    renamed: never[];
    files: never[];
    staged: never[];
    ahead: number;
    behind: number;
    current: null;
    tracking: null;
    detached: boolean;
    isClean: () => boolean;
}
export declare const parseStatusSummary: (text: string) => StatusResult;
