<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 测试控制器
 */

declare(strict_types=1);

namespace app\controller;

use app\BaseController;
use think\Response;

/**
 * 测试控制器
 */
class TestController extends BaseController
{
    /**
     * 测试API
     * @return Response
     */
    public function index(): Response
    {
        return $this->success([
            'message' => 'QiyeDIY API 测试成功',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0.0'
        ]);
    }

    /**
     * 测试数据库连接
     * @return Response
     */
    public function database(): Response
    {
        try {
            $db = \think\facade\Db::connect();
            
            // 测试查询
            $userCount = $db->table('qd_users')->count();
            $roleCount = $db->table('qd_roles')->count();
            $componentCount = $db->table('qd_diy_components')->count();
            $templateCount = $db->table('qd_diy_templates')->count();
            
            return $this->success([
                'database_status' => 'connected',
                'statistics' => [
                    'users' => $userCount,
                    'roles' => $roleCount,
                    'components' => $componentCount,
                    'templates' => $templateCount
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->error('数据库连接失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试DIY统计（无权限检查）
     * @return Response
     */
    public function diyStats(): Response
    {
        try {
            $db = \think\facade\Db::connect();
            
            $pageStats = [
                'total' => $db->table('qd_diy_pages')->count(),
                'published' => $db->table('qd_diy_pages')->where('status', 1)->count(),
                'draft' => $db->table('qd_diy_pages')->where('status', 0)->count(),
            ];

            $componentStats = [
                'total' => $db->table('qd_diy_components')->count(),
                'active' => $db->table('qd_diy_components')->where('is_active', 1)->count(),
                'system' => $db->table('qd_diy_components')->where('is_system', 1)->count(),
            ];

            $templateStats = [
                'total' => $db->table('qd_diy_templates')->count(),
                'free' => $db->table('qd_diy_templates')->where('is_free', 1)->count(),
                'paid' => $db->table('qd_diy_templates')->where('is_free', 0)->count(),
                'system' => $db->table('qd_diy_templates')->where('is_system', 1)->count(),
            ];

            return $this->success([
                'pages' => $pageStats,
                'components' => $componentStats,
                'templates' => $templateStats
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败: ' . $e->getMessage());
        }
    }
}
