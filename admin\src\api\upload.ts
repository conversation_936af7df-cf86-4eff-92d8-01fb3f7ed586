/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 文件上传API接口
 */

import { http } from '@/utils/http'
import type { ApiResponse } from '@/types/auth'

/**
 * 上传文件接口
 */
export interface UploadFile {
  id: number
  filename: string
  original_name: string
  mime_type: string
  size: number
  path: string
  url: string
  alt_text?: string
  caption?: string
  meta_data?: Record<string, any>
  storage_driver: string
  folder?: string
  uploaded_by: number
  created_at: string
  updated_at: string
}

/**
 * 上传结果接口
 */
export interface UploadResult {
  id: number
  filename: string
  original_name: string
  mime_type: string
  size: number
  path: string
  url: string
  thumbnail?: string
  width?: number
  height?: number
  duration?: number
}

/**
 * 批量上传结果接口
 */
export interface BatchUploadResult {
  success: UploadResult[]
  failed: {
    filename: string
    error: string
  }[]
  total: number
  success_count: number
  failed_count: number
}

/**
 * 上传配置接口
 */
export interface UploadConfig {
  max_file_size: number
  allowed_extensions: string[]
  allowed_mime_types: string[]
  upload_path: string
  thumbnail_sizes: number[]
  image_quality: number
  video_max_duration: number
  storage_driver: string
  cdn_domain?: string
}

/**
 * 文件上传相关API
 */
export const uploadApi = {
  /**
   * 上传图片
   */
  uploadImage: (file: File, options?: {
    folder?: string
    alt_text?: string
    caption?: string
  }): Promise<ApiResponse<UploadResult>> => {
    const formData = new FormData()
    formData.append('file', file)
    if (options?.folder) formData.append('folder', options.folder)
    if (options?.alt_text) formData.append('alt_text', options.alt_text)
    if (options?.caption) formData.append('caption', options.caption)

    return http.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传视频
   */
  uploadVideo: (file: File, options?: {
    folder?: string
    caption?: string
  }): Promise<ApiResponse<UploadResult>> => {
    const formData = new FormData()
    formData.append('file', file)
    if (options?.folder) formData.append('folder', options.folder)
    if (options?.caption) formData.append('caption', options.caption)

    return http.post('/upload/video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传文档
   */
  uploadDocument: (file: File, options?: {
    folder?: string
    caption?: string
  }): Promise<ApiResponse<UploadResult>> => {
    const formData = new FormData()
    formData.append('file', file)
    if (options?.folder) formData.append('folder', options.folder)
    if (options?.caption) formData.append('caption', options.caption)

    return http.post('/upload/document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传头像
   */
  uploadAvatar: (file: File): Promise<ApiResponse<UploadResult>> => {
    const formData = new FormData()
    formData.append('file', file)

    return http.post('/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 批量上传
   */
  batchUpload: (files: File[], options?: {
    folder?: string
  }): Promise<ApiResponse<BatchUploadResult>> => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files[]', file)
    })
    if (options?.folder) formData.append('folder', options.folder)

    return http.post('/upload/batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * Base64上传
   */
  uploadBase64: (data: {
    data: string
    type: 'image' | 'video' | 'document'
    filename?: string
    folder?: string
    alt_text?: string
    caption?: string
  }): Promise<ApiResponse<UploadResult>> => {
    return http.post('/upload/base64', data)
  },

  /**
   * 获取上传配置
   */
  getConfig: (): Promise<ApiResponse<UploadConfig>> => {
    return http.get('/upload/config')
  },

  /**
   * 删除文件
   */
  deleteFile: (path: string): Promise<ApiResponse<{ deleted: boolean }>> => {
    return http.delete('/upload/delete', {
      data: { path }
    })
  },

  /**
   * 获取文件信息
   */
  getFileInfo: (path: string): Promise<ApiResponse<UploadFile>> => {
    return http.get('/upload/info', {
      params: { path }
    })
  },

  /**
   * 生成缩略图
   */
  generateThumbnail: (data: {
    path: string
    width?: number
    height?: number
    quality?: number
  }): Promise<ApiResponse<{ thumbnail_url: string }>> => {
    return http.post('/upload/thumbnail', data)
  },

  /**
   * 图片裁剪
   */
  cropImage: (data: {
    path: string
    x: number
    y: number
    width: number
    height: number
    quality?: number
  }): Promise<ApiResponse<UploadResult>> => {
    return http.post('/upload/crop', data)
  },

  /**
   * 图片压缩
   */
  compressImage: (data: {
    path: string
    quality?: number
    max_width?: number
    max_height?: number
  }): Promise<ApiResponse<UploadResult>> => {
    return http.post('/upload/compress', data)
  },

  /**
   * 获取上传统计
   */
  getStatistics: (): Promise<ApiResponse<{
    total_files: number
    total_size: number
    today_uploads: number
    today_size: number
    type_stats: {
      type: string
      count: number
      size: number
    }[]
    monthly_stats: {
      month: string
      count: number
      size: number
    }[]
  }>> => {
    return http.get('/upload/statistics')
  }
}
