<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - 系统演示和测试
 */

echo "=== QiyeDIY企业建站系统 - 完整演示 ===\n\n";

// 1. 系统状态检查
echo "1. 检查系统状态...\n";
$systemCheck = shell_exec('php system_check.php');
echo $systemCheck . "\n";

// 2. 检查后端服务
echo "2. 检查后端服务状态...\n";
$backendStatus = checkBackendService();
echo "后端服务状态: " . ($backendStatus ? "✅ 运行中" : "❌ 未启动") . "\n\n";

// 3. 检查前端服务
echo "3. 检查前端服务状态...\n";
$frontendStatus = checkFrontendService();
echo "前端服务状态: " . ($frontendStatus ? "✅ 运行中" : "❌ 未启动") . "\n\n";

// 4. 检查管理后台
echo "4. 检查管理后台状态...\n";
$adminStatus = checkAdminService();
echo "管理后台状态: " . ($adminStatus ? "✅ 运行中" : "❌ 未启动") . "\n\n";

// 5. 数据库连接测试
echo "5. 测试数据库连接...\n";
$dbStatus = testDatabaseConnection();
echo "数据库连接: " . ($dbStatus ? "✅ 正常" : "❌ 失败") . "\n\n";

// 6. API接口测试
echo "6. 测试API接口...\n";
testApiEndpoints();

// 7. 功能演示
echo "7. 系统功能演示...\n";
demonstrateFeatures();

// 8. 性能测试
echo "8. 性能测试...\n";
performanceTest();

echo "\n=== 演示完成 ===\n";
echo "🎉 QiyeDIY企业建站系统已完全就绪！\n\n";

echo "📋 访问地址：\n";
echo "- 管理后台: http://localhost:5173\n";
echo "- 前端展示: http://localhost:3000\n";
echo "- 后端API: http://localhost:8000\n\n";

echo "🔧 下一步操作：\n";
echo "1. 访问管理后台进行系统配置\n";
echo "2. 创建DIY页面和模板\n";
echo "3. 配置网站基本信息\n";
echo "4. 开始构建您的企业网站\n\n";

/**
 * 检查后端服务状态
 */
function checkBackendService(): bool
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

/**
 * 检查前端服务状态
 */
function checkFrontendService(): bool
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:3000');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

/**
 * 检查管理后台状态
 */
function checkAdminService(): bool
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:5173');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection(): bool
{
    try {
        $config = [
            'host' => 'localhost',
            'dbname' => 'qiyediy',
            'username' => 'root',
            'password' => ''
        ];
        
        $pdo = new PDO(
            "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4",
            $config['username'],
            $config['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $stmt = $pdo->query('SELECT COUNT(*) FROM diy_pages');
        $count = $stmt->fetchColumn();
        
        echo "   - 数据库连接成功\n";
        echo "   - DIY页面数量: {$count}\n";
        
        return true;
    } catch (Exception $e) {
        echo "   - 数据库连接失败: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * 测试API接口
 */
function testApiEndpoints(): void
{
    $endpoints = [
        'GET /api/diy/statistics' => 'http://localhost:8000/api/diy/statistics',
        'GET /api/upload/config' => 'http://localhost:8000/api/upload/config',
        'GET /api/public/site/info' => 'http://localhost:8000/api/public/site/info'
    ];
    
    foreach ($endpoints as $name => $url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $status = ($httpCode >= 200 && $httpCode < 300) ? "✅" : "❌";
        echo "   {$status} {$name} (HTTP {$httpCode})\n";
        
        if ($result && $httpCode === 200) {
            $data = json_decode($result, true);
            if ($data && isset($data['code'])) {
                echo "      响应: " . substr(json_encode($data, JSON_UNESCAPED_UNICODE), 0, 100) . "...\n";
            }
        }
    }
    echo "\n";
}

/**
 * 功能演示
 */
function demonstrateFeatures(): void
{
    echo "   🎨 DIY编辑器功能:\n";
    echo "      - 拖拽式页面构建\n";
    echo "      - 40+ 预设组件\n";
    echo "      - 响应式设计支持\n";
    echo "      - 实时预览功能\n\n";
    
    echo "   📊 管理后台功能:\n";
    echo "      - 用户权限管理\n";
    echo "      - 页面内容管理\n";
    echo "      - 模板库管理\n";
    echo "      - 系统设置配置\n\n";
    
    echo "   🌐 前端展示功能:\n";
    echo "      - SSR服务端渲染\n";
    echo "      - SEO优化支持\n";
    echo "      - 移动端适配\n";
    echo "      - 高性能加载\n\n";
}

/**
 * 性能测试
 */
function performanceTest(): void
{
    echo "   ⚡ 系统性能指标:\n";
    
    // 测试页面加载速度
    $start = microtime(true);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:3000');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $result = curl_exec($ch);
    curl_close($ch);
    $loadTime = round((microtime(true) - $start) * 1000, 2);
    
    echo "      - 前端页面加载: {$loadTime}ms\n";
    
    // 测试API响应速度
    $start = microtime(true);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/public/site/info');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $result = curl_exec($ch);
    curl_close($ch);
    $apiTime = round((microtime(true) - $start) * 1000, 2);
    
    echo "      - API响应速度: {$apiTime}ms\n";
    
    // 内存使用情况
    $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
    echo "      - 内存使用: {$memoryUsage}MB\n";
    
    // 磁盘使用情况
    $diskUsage = round(disk_total_space('.') / 1024 / 1024 / 1024, 2);
    $diskFree = round(disk_free_space('.') / 1024 / 1024 / 1024, 2);
    echo "      - 磁盘空间: {$diskFree}GB / {$diskUsage}GB 可用\n\n";
}
