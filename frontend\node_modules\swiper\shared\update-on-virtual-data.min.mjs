import{e as extend,p as paramsList,i as isObject,n as needsNavigation,a as needsPagination,b as needsScrollbar}from"./update-swiper.min.mjs";import{d as defaults}from"./swiper-core.min.mjs";function getParams(a,e){void 0===a&&(a={}),void 0===e&&(e=!0);const s={on:{}},t={},n={};extend(s,defaults),s._emitClasses=!0,s.init=!1;const r={},i=paramsList.map((a=>a.replace(/_/,""))),l=Object.assign({},a);return Object.keys(l).forEach((l=>{void 0!==a[l]&&(i.indexOf(l)>=0?isObject(a[l])?(s[l]={},n[l]={},extend(s[l],a[l]),extend(n[l],a[l])):(s[l]=a[l],n[l]=a[l]):0===l.search(/on[A-Z]/)&&"function"==typeof a[l]?e?t[`${l[2].toLowerCase()}${l.substr(3)}`]=a[l]:s.on[`${l[2].toLowerCase()}${l.substr(3)}`]=a[l]:r[l]=a[l])})),["navigation","pagination","scrollbar"].forEach((a=>{!0===s[a]&&(s[a]={}),!1===s[a]&&delete s[a]})),{params:s,passedParams:n,rest:r,events:t}}function mountSwiper(a,e){let{el:s,nextEl:t,prevEl:n,paginationEl:r,scrollbarEl:i,swiper:l}=a;needsNavigation(e)&&t&&n&&(l.params.navigation.nextEl=t,l.originalParams.navigation.nextEl=t,l.params.navigation.prevEl=n,l.originalParams.navigation.prevEl=n),needsPagination(e)&&r&&(l.params.pagination.el=r,l.originalParams.pagination.el=r),needsScrollbar(e)&&i&&(l.params.scrollbar.el=i,l.originalParams.scrollbar.el=i),l.init(s)}function getChangedParams(a,e,s,t,n){const r=[];if(!e)return r;const i=a=>{r.indexOf(a)<0&&r.push(a)};if(s&&t){const a=t.map(n),e=s.map(n);a.join("")!==e.join("")&&i("children"),t.length!==s.length&&i("children")}return paramsList.filter((a=>"_"===a[0])).map((a=>a.replace(/_/,""))).forEach((s=>{if(s in a&&s in e)if(isObject(a[s])&&isObject(e[s])){const t=Object.keys(a[s]),n=Object.keys(e[s]);t.length!==n.length?i(s):(t.forEach((t=>{a[s][t]!==e[s][t]&&i(s)})),n.forEach((t=>{a[s][t]!==e[s][t]&&i(s)})))}else a[s]!==e[s]&&i(s)})),r}const updateOnVirtualData=a=>{!a||a.destroyed||!a.params.virtual||a.params.virtual&&!a.params.virtual.enabled||(a.updateSlides(),a.updateProgress(),a.updateSlidesClasses(),a.emit("_virtualUpdated"),a.parallax&&a.params.parallax&&a.params.parallax.enabled&&a.parallax.setTranslate())};export{getChangedParams as a,getParams as g,mountSwiper as m,updateOnVirtualData as u};
//# sourceMappingURL=update-on-virtual-data.min.mjs.map