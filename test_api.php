<?php
/**
 * 三只鱼网络科技 | 韩总 | 2024-12-19
 * QiyeDIY企业建站系统 - API测试脚本
 */

echo "=== QiyeDIY API测试 ===\n\n";

// 测试基础API
echo "1. 测试基础API...\n";
$response = file_get_contents('http://localhost:8000');
if ($response) {
    $data = json_decode($response, true);
    echo "✅ 基础API正常: " . $data['data']['message'] . "\n";
} else {
    echo "❌ 基础API失败\n";
}

echo "\n2. 测试登录API...\n";
$loginData = json_encode([
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $loginData
    ]
]);

$response = file_get_contents('http://localhost:8000/api/auth/login', false, $context);
if ($response) {
    $data = json_decode($response, true);
    if ($data['code'] == 200) {
        echo "✅ 登录API正常: " . $data['message'] . "\n";
        echo "   Token: " . $data['data']['token'] . "\n";
        $token = $data['data']['token'];
    } else {
        echo "❌ 登录失败: " . $data['message'] . "\n";
        $token = null;
    }
} else {
    echo "❌ 登录API请求失败\n";
    $token = null;
}

echo "\n3. 测试数据库连接...\n";
try {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=qiyediy;charset=utf8mb4',
        'root',
        '123456',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $pdo->query('SELECT COUNT(*) as count FROM qd_users');
    $userCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query('SELECT COUNT(*) as count FROM qd_diy_components');
    $componentCount = $stmt->fetchColumn();
    
    echo "✅ 数据库连接正常\n";
    echo "   用户数量: {$userCount}\n";
    echo "   组件数量: {$componentCount}\n";
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

if ($token) {
    echo "\n4. 测试认证API...\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => "Authorization: Bearer {$token}"
        ]
    ]);
    
    $response = file_get_contents('http://localhost:8000/api/diy/statistics', false, $context);
    if ($response) {
        $data = json_decode($response, true);
        if ($data['code'] == 200) {
            echo "✅ DIY统计API正常\n";
            echo "   数据: " . json_encode($data['data'], JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "❌ DIY统计API失败: " . $data['message'] . "\n";
        }
    } else {
        echo "❌ DIY统计API请求失败\n";
    }
}

echo "\n=== 测试完成 ===\n";
