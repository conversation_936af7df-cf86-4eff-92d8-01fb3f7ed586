import{ELEMENT_NODE as a,walkSync as p}from"../index.js";function f(t){var n;if(t===void 0)return{allowElements:[],dropElements:["script"],allowComponents:!1,allowCustomElements:!1,allowComments:!1};{let e=new Set([]);(n=t.allowElements)!=null&&n.includes("script")||e.add("script");for(let l of t.dropElements??[])e.add(l);return{allowComponents:!1,allowCustomElements:!1,allowComments:!1,...t,dropElements:Array.from(e)}}}function E(t){return t.name.includes("-")?"custom-element":/[\_\$A-Z]/.test(t.name[0])||t.name.includes(".")?"component":"element"}function w(t,n,e){var l,o,s,r;return((l=e.allowElements)==null?void 0:l.length)>0&&e.allowElements.includes(t)?"allow":((o=e.blockElements)==null?void 0:o.length)>0&&e.blockElements.includes(t)?"block":((s=e.dropElements)==null?void 0:s.length)>0&&e.dropElements.find(u=>u===t)||n==="component"&&!e.allowComponents||n==="custom-element"&&!e.allowCustomElements?"drop":e.unblockElements?e.unblockElements.some(u=>u===t)?"allow":"block":((r=e.allowElements)==null?void 0:r.length)>0?"drop":"allow"}function b(t,n){var l,o,s,r,u,m,c,d;let e=t.attributes;for(let i of Object.keys(t.attributes))(l=n.allowAttributes)!=null&&l[i]&&((o=n.allowAttributes)!=null&&o[i].includes(t.name))||(r=(s=n.allowAttributes)==null?void 0:s[i])!=null&&r.includes("*")||((u=n.dropAttributes)!=null&&u[i]&&((m=n.dropAttributes)!=null&&m[i].includes(t.name))||(d=(c=n.dropAttributes)==null?void 0:c[i])!=null&&d.includes("*"))&&delete e[i];return e}function g(t,n,e){let l=E(n),{name:o}=n,s=w(o,l,t);return s==="drop"?()=>{e.children=e.children.filter(r=>r!==n)}:s==="block"?()=>{e.children=e.children.map(r=>r===n?r.children:r).flat(1)}:()=>{n.attributes=b(n,t)}}function N(t){let n=f(t);return e=>{let l=[];p(e,(o,s)=>{switch(o.type){case a:{l.push(g(n,o,s));return}default:return}});for(let o=l.length-1;o>=0;o--)l[o]();return e}}export{N as default};
