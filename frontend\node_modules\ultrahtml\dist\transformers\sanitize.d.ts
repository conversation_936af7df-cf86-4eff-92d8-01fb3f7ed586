// Generated by dts-bundle-generator v9.5.1

type Node$1 = DocumentNode | ElementNode | TextNode | CommentNode | DoctypeNode;
type NodeType = typeof DOCUMENT_NODE | typeof ELEMENT_NODE | typeof TEXT_NODE | typeof COMMENT_NODE | typeof DOCTYPE_NODE;
interface Location$1 {
	start: number;
	end: number;
}
interface BaseNode {
	type: NodeType;
	loc: [
		Location$1,
		Location$1
	];
	parent: Node$1;
	[key: string]: any;
}
interface LiteralNode extends BaseNode {
	value: string;
}
interface ParentNode$1 extends BaseNode {
	children: Node$1[];
}
interface DocumentNode extends Omit<ParentNode$1, "parent"> {
	type: typeof DOCUMENT_NODE;
	attributes: Record<string, string>;
	parent: undefined;
}
interface ElementNode extends ParentNode$1 {
	type: typeof ELEMENT_NODE;
	name: string;
	attributes: Record<string, string>;
}
interface TextNode extends LiteralNode {
	type: typeof TEXT_NODE;
}
interface CommentNode extends LiteralNode {
	type: typeof COMMENT_NODE;
}
interface DoctypeNode extends LiteralNode {
	type: typeof DOCTYPE_NODE;
}
declare const DOCUMENT_NODE = 0;
declare const ELEMENT_NODE = 1;
declare const TEXT_NODE = 2;
declare const COMMENT_NODE = 3;
declare const DOCTYPE_NODE = 4;
export interface SanitizeOptions {
	/** An Array of strings indicating elements that the sanitizer should not remove. All elements not in the array will be dropped. */
	allowElements?: string[];
	/** An Array of strings indicating elements that the sanitizer should not remove. All elements not in the array will be removed while keeping their child content. */
	unblockElements?: string[];
	/** An Array of strings indicating elements that the sanitizer should remove, but keeping their child elements. */
	blockElements?: string[];
	/** An Array of strings indicating elements (including nested elements) that the sanitizer should remove. */
	dropElements?: string[];
	/** An Object where each key is the attribute name and the value is an Array of allowed tag names. Matching attributes will not be removed. All attributes that are not in the array will be dropped. */
	allowAttributes?: Record<string, string[]>;
	/** An Object where each key is the attribute name and the value is an Array of dropped tag names. Matching attributes will be removed. */
	dropAttributes?: Record<string, string[]>;
	/** A Boolean value set to false (default) to remove components and their children. If set to true, components will be subject to built-in and custom configuration checks (and will be retained or dropped based on those checks). */
	allowComponents?: boolean;
	/** A Boolean value set to false (default) to remove custom elements and their children. If set to true, custom elements will be subject to built-in and custom configuration checks (and will be retained or dropped based on those checks). */
	allowCustomElements?: boolean;
	/** A Boolean value set to false (default) to remove HTML comments. Set to true in order to keep comments. */
	allowComments?: boolean;
}
declare function sanitize(opts?: SanitizeOptions): (doc: Node$1) => Node$1;

export {
	sanitize as default,
};

export {};
