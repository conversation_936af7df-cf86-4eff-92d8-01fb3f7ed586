# QiyeDIY企业建站系统 - 部署指南

> 三只鱼网络科技 | 韩总 | 2024-12-19  
> 完整的生产环境部署指南

## 📋 系统要求

### 服务器环境
- **操作系统**: Linux (Ubuntu 20.04+) / Windows Server 2019+
- **Web服务器**: Nginx 1.18+ / Apache 2.4+
- **PHP版本**: PHP 8.1+
- **数据库**: MySQL 8.0+ / MariaDB 10.6+
- **缓存**: Redis 7.0+
- **Node.js**: 18.0+ (构建时需要)

### 硬件配置
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **存储**: 20GB以上 SSD
- **带宽**: 10Mbps以上

## 🚀 快速部署

### 1. 环境准备

#### Ubuntu/Debian 系统
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y nginx mysql-server redis-server php8.1-fpm php8.1-mysql php8.1-redis php8.1-gd php8.1-curl php8.1-zip php8.1-xml php8.1-mbstring

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### CentOS/RHEL 系统
```bash
# 安装EPEL和Remi仓库
sudo yum install -y epel-release
sudo yum install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm

# 安装软件包
sudo yum module enable php:remi-8.1 -y
sudo yum install -y nginx mysql-server redis php81-php-fpm php81-php-mysql php81-php-redis php81-php-gd

# 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

### 2. 数据库配置

```bash
# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
```

```sql
CREATE DATABASE qiyediy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'qiyediy'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON qiyediy.* TO 'qiyediy'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 项目部署

```bash
# 创建项目目录
sudo mkdir -p /var/www/qiyediy
cd /var/www/qiyediy

# 克隆项目 (或上传文件)
git clone https://github.com/your-repo/qiyediy.git .

# 设置权限
sudo chown -R www-data:www-data /var/www/qiyediy
sudo chmod -R 755 /var/www/qiyediy
sudo chmod -R 777 /var/www/qiyediy/backend/runtime
sudo chmod -R 777 /var/www/qiyediy/backend/public/uploads
```

### 4. 后端配置

```bash
cd /var/www/qiyediy/backend

# 安装依赖
composer install --no-dev --optimize-autoloader

# 配置环境变量
cp .env.example .env
nano .env
```

```env
# 数据库配置
DATABASE_HOST=localhost
DATABASE_NAME=qiyediy
DATABASE_USERNAME=qiyediy
DATABASE_PASSWORD=your_strong_password

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# 应用配置
APP_DEBUG=false
APP_DOMAIN=https://your-domain.com
```

```bash
# 导入数据库
mysql -u qiyediy -p qiyediy < database/qiyediy.sql

# 清除缓存
php think clear
```

### 5. 前端构建

```bash
# 构建管理后台
cd /var/www/qiyediy/admin
npm install
npm run build

# 构建前端展示
cd /var/www/qiyediy/frontend
npm install
npm run build
```

### 6. Nginx配置

创建 `/etc/nginx/sites-available/qiyediy.conf`:

```nginx
# 后端API服务
server {
    listen 80;
    server_name api.your-domain.com;
    root /var/www/qiyediy/backend/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }
}

# 管理后台
server {
    listen 80;
    server_name admin.your-domain.com;
    root /var/www/qiyediy/admin/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# 前端展示
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/qiyediy/frontend/.output/public;
    index index.html;

    # Nuxt.js 配置
    location / {
        try_files $uri $uri/ @nuxt;
    }

    location @nuxt {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/qiyediy.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 7. SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com -d admin.your-domain.com -d api.your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 8. 进程管理

创建 `/etc/systemd/system/qiyediy-frontend.service`:

```ini
[Unit]
Description=QiyeDIY Frontend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/qiyediy/frontend
ExecStart=/usr/bin/node .output/server/index.mjs
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
```

```bash
# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable qiyediy-frontend
sudo systemctl start qiyediy-frontend
```

## 🔧 性能优化

### 1. PHP优化

编辑 `/etc/php/8.1/fpm/php.ini`:

```ini
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 50M
post_max_size = 50M
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000
```

### 2. MySQL优化

编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf`:

```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_type = 1
query_cache_size = 64M
max_connections = 200
```

### 3. Redis优化

编辑 `/etc/redis/redis.conf`:

```ini
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 4. Nginx优化

```nginx
# 在http块中添加
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g inactive=60m use_temp_path=off;
```

## 📊 监控和维护

### 1. 日志管理

```bash
# 设置日志轮转
sudo nano /etc/logrotate.d/qiyediy
```

```
/var/www/qiyediy/backend/runtime/log/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. 备份策略

创建备份脚本 `/usr/local/bin/qiyediy-backup.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/backup/qiyediy"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u qiyediy -p qiyediy > $BACKUP_DIR/database_$DATE.sql

# 文件备份
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/qiyediy

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

```bash
# 设置定时备份
sudo crontab -e
# 添加: 0 2 * * * /usr/local/bin/qiyediy-backup.sh
```

### 3. 监控脚本

创建 `/usr/local/bin/qiyediy-monitor.sh`:

```bash
#!/bin/bash
# 检查服务状态
systemctl is-active --quiet nginx || systemctl restart nginx
systemctl is-active --quiet mysql || systemctl restart mysql
systemctl is-active --quiet redis || systemctl restart redis
systemctl is-active --quiet php8.1-fpm || systemctl restart php8.1-fpm
systemctl is-active --quiet qiyediy-frontend || systemctl restart qiyediy-frontend

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "警告：磁盘使用率超过80%" | mail -s "QiyeDIY磁盘空间警告" <EMAIL>
fi
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# UFW防火墙
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3000/tcp  # 禁止直接访问Node.js端口
```

### 2. 安全加固

```bash
# 隐藏服务器信息
echo "server_tokens off;" >> /etc/nginx/nginx.conf

# 设置文件权限
find /var/www/qiyediy -type f -exec chmod 644 {} \;
find /var/www/qiyediy -type d -exec chmod 755 {} \;
chmod 600 /var/www/qiyediy/backend/.env
```

## 📞 技术支持

- **开发者**: 韩总 (三只鱼网络科技)
- **邮箱**: <EMAIL>
- **技术文档**: https://docs.qiyediy.com
- **问题反馈**: https://github.com/qiyediy/issues

---

© 2024 三只鱼网络科技 | QiyeDIY企业建站系统
