import{walkSync as Rt,ELEMENT_NODE as Ct,TEXT_NODE as At}from"../index.js";import{querySelectorAll as St,specificity as ht}from"../selector.js";var Pe="comm",Ie="rule",we="decl";var ce=Math.abs,U=String.fromCharCode;function J(e){return e.trim()}function Y(e,r,t){return e.replace(r,t)}function Ne(e,r,t){return e.indexOf(r,t)}function L(e,r){return e.charCodeAt(r)|0}function S(e,r,t){return e.slice(r,t)}function R(e){return e.length}function ke(e){return e.length}function F(e,r){return r.push(e),e}var ee=1,D=1,Oe=0,N=0,T=0,G="";function te(e,r,t,o,n,i,a,s){return{value:e,root:r,parent:t,type:o,props:n,children:i,line:ee,column:D,length:a,return:"",siblings:s}}function Re(){return T}function Ce(){return T=N>0?L(G,--N):0,D--,T===10&&(D=1,ee--),T}function k(){return T=N<Oe?L(G,N++):0,D++,T===10&&(D=1,ee++),T}function A(){return L(G,N)}function Q(){return N}function re(e,r){return S(G,e,r)}function B(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Ae(e){return ee=D=1,Oe=R(G=e),N=0,[]}function Se(e){return G="",e}function ne(e){return J(re(N-1,de(e===91?e+2:e===40?e+1:e)))}function _e(e){for(;(T=A())&&T<33;)k();return B(e)>2||B(T)>3?"":" "}function Me(e,r){for(;--r&&k()&&!(T<48||T>102||T>57&&T<65||T>70&&T<97););return re(e,Q()+(r<6&&A()==32&&k()==32))}function de(e){for(;k();)switch(T){case e:return N;case 34:case 39:e!==34&&e!==39&&de(T);break;case 40:e===41&&de(e);break;case 92:k();break}return N}function Le(e,r){for(;k()&&e+T!==57;)if(e+T===84&&A()===47)break;return"/*"+re(r,N-1)+"*"+U(e===47?e:k())}function Ve(e){for(;!B(A());)k();return re(e,N)}function fe(e){return Se(oe("",null,null,null,[""],e=Ae(e),0,[0],e))}function oe(e,r,t,o,n,i,a,s,l){for(var p=0,u=0,c=a,d=0,f=0,m=0,v=1,b=1,P=1,y=0,I="",E=n,x=i,g=o,h=I;b;)switch(m=y,y=k()){case 40:if(m!=108&&L(h,c-1)==58){Ne(h+=Y(ne(y),"&","&\f"),"&\f",ce(p?s[p-1]:0))!=-1&&(P=-1);break}case 34:case 39:case 91:h+=ne(y);break;case 9:case 10:case 13:case 32:h+=_e(m);break;case 92:h+=Me(Q()-1,7);continue;case 47:switch(A()){case 42:case 47:F(vt(Le(k(),Q()),r,t,l),l),(B(m||1)==5||B(A()||1)==5)&&R(h)&&S(h,-1,void 0)!==" "&&(h+=" ");break;default:h+="/"}break;case 123*v:s[p++]=R(h)*P;case 125*v:case 59:case 0:switch(y){case 0:case 125:b=0;case 59+u:P==-1&&(h=Y(h,/\f/g,"")),f>0&&(R(h)-c||v===0&&m===47)&&F(f>32?De(h+";",o,t,c-1,l):De(Y(h," ","")+";",o,t,c-2,l),l);break;case 59:h+=";";default:if(F(g=Fe(h,r,t,p,u,n,s,I,E=[],x=[],c,i),i),y===123)if(u===0)oe(h,r,g,g,E,i,c,s,x);else switch(d===99&&L(h,3)===110?100:d){case 100:case 108:case 109:case 115:oe(e,g,g,o&&F(Fe(e,g,g,0,0,n,s,I,n,E=[],c,x),x),n,x,c,s,o?E:x);break;default:oe(h,g,g,g,[""],x,0,s,x)}}p=u=f=0,v=P=1,I=h="",c=a;break;case 58:c=1+R(h),f=m;default:if(v<1){if(y==123)--v;else if(y==125&&v++==0&&Ce()==125)continue}switch(h+=U(y),y*v){case 38:P=u>0?1:(h+="\f",-1);break;case 44:s[p++]=(R(h)-1)*P,P=1;break;case 64:A()===45&&(h+=ne(k())),d=A(),u=c=R(I=h+=Ve(Q())),y++;break;case 45:m===45&&R(h)==2&&(v=0)}}return i}function Fe(e,r,t,o,n,i,a,s,l,p,u,c){for(var d=n-1,f=n===0?i:[""],m=ke(f),v=0,b=0,P=0;v<o;++v)for(var y=0,I=S(e,d+1,d=ce(b=a[v])),E=e;y<m;++y)(E=J(b>0?f[y]+" "+I:Y(I,/&\f/g,f[y])))&&(l[P++]=E);return te(e,r,t,n===0?Ie:s,l,p,u,c)}function vt(e,r,t,o){return te(e,r,t,Pe,U(Re()),S(e,2,-2),0,o)}function De(e,r,t,o,n){return te(e,r,t,we,S(e,0,o),S(e,o+1,-1),o,n)}var C=e=>typeof e=="object"&&e!==null&&"errid"in e,yt=e=>{let r=[[]],t=[];for(let o of e)if(o.type==="comma"&&t.length===0)r.push([]);else{switch(o.type){case"function":case"(":t.push(")");break;case"[":t.push("]");break;case"{":t.push("}");break;case")":case"]":case"}":t.at(-1)===o.type&&t.pop();break}r[r.length-1].push(o)}return r},Be=e=>{let r=yt(e);if(r.length===1&&r[0].length===0)return{type:"query-list",mediaQueries:[{type:"query"}]};{let t=[];for(let o of r){let n=Ge(o);C(n)?t.push({type:"query",prefix:"not"}):t.push(n)}return{type:"query-list",mediaQueries:t}}},Ge=e=>{var r,t,o;let n=e.at(0);if(n){if(n.type==="("){let i=_(e,!0);if(C(i)){let{start:a,end:s}=(r=e.at(1))!==null&&r!==void 0?r:n;return{errid:"EXPECT_FEATURE_OR_CONDITION",start:a,end:s,child:i}}return{type:"query",mediaCondition:i}}if(n.type==="ident"){let i,a,{value:s,end:l}=n;s!=="only"&&s!=="not"||(i=s);let p=i===void 0?0:1,u=e.at(p);if(!u)return{errid:"EXPECT_LPAREN_OR_TYPE",start:l,end:l};if(u.type!=="ident"){if(i==="not"&&u.type==="("){let c=_(e.slice(p),!0);if(C(c)){let{start:d,end:f}=(t=e.at(p+1))!==null&&t!==void 0?t:u;return{errid:"EXPECT_CONDITION",start:d,end:f,child:c}}return{type:"query",mediaCondition:{type:"condition",operator:"not",children:[c]}}}{let{start:c,end:d}=u;return{errid:"EXPECT_TYPE",start:c,end:d}}}{let{value:c,start:d,end:f}=u;if(c==="all")a=void 0;else if(c==="print"||c==="screen")a=c;else{if(c!=="tty"&&c!=="tv"&&c!=="projection"&&c!=="handheld"&&c!=="braille"&&c!=="embossed"&&c!=="aural"&&c!=="speech")return{errid:"EXPECT_TYPE",start:d,end:f};i=i==="not"?void 0:"not",a=void 0}}if(p+1===e.length)return{type:"query",prefix:i,mediaType:a};{let c=e[p+1];if(c.type==="ident"&&c.value==="and"){let d=e.at(-1),f=e.at(p+2),m,v=d.end+1;if((f==null?void 0:f.type)==="ident"&&f.value==="not"){v+=1;let y=_(e.slice(p+3),!1);m=C(y)?y:{type:"condition",operator:"not",children:[y]}}else m=_(e.slice(p+2),!1);let{start:b,end:P}=(o=e.at(p+2))!==null&&o!==void 0?o:{start:v,end:v};return C(m)?{errid:"EXPECT_CONDITION",start:b,end:P,child:m}:{type:"query",prefix:i,mediaType:a,mediaCondition:m}}return{errid:"EXPECT_AND",start:c.start,end:c.end}}}return{errid:"EXPECT_LPAREN_OR_TYPE_OR_MODIFIER",start:n.start,end:n.end}}return{errid:"EMPTY_QUERY",start:0,end:0}},_=(e,r,t)=>{let o=e.at(0);if(o){if(o.type!=="(")return{errid:"EXPECT_LPAREN",start:o.start,end:o.end};let n,i=e.length-1,a=0,s=0;for(let[p,u]of e.entries())if(u.type==="("?(s+=1,a=Math.max(a,s)):u.type===")"&&(s-=1),s===0){i=p;break}if(s!==0)return{errid:"MISMATCH_PARENS",start:o.start,end:e[e.length-1].end};let l=e.slice(0,i+1);if(n=a===1?qe(l):l[1].type==="ident"&&l[1].value==="not"?_(l.slice(2,-1),!0,"not"):_(l.slice(1,-1),!0),C(n))return{errid:"EXPECT_FEATURE_OR_CONDITION",start:o.start,end:l[l.length-1].end,child:n};if(i===e.length-1)return{type:"condition",operator:t,children:[n]};{let p=e[i+1];if(p.type!=="ident"||p.value!=="and"&&p.value!=="or")return{errid:"EXPECT_AND_OR_OR",start:p.start,end:p.end};if(t!==void 0&&t!==p.value)return{errid:"MIX_AND_WITH_OR",start:p.start,end:p.end};if(p.value==="or"&&!r)return{errid:"MIX_AND_WITH_OR",start:p.start,end:p.end};let u=_(e.slice(i+2),r,p.value);return C(u)?u:{type:"condition",operator:p.value,children:[n,...u.children]}}}return{errid:"EMPTY_CONDITION",start:0,end:0}},qe=e=>{let r=e.at(0);if(r){if(r.type!=="(")return{errid:"EXPECT_LPAREN",start:r.start,end:r.end};let t=e[e.length-1];if(t.type!==")")return{errid:"EXPECT_RPAREN",start:t.end+1,end:t.end+1};let o=[e[0]];for(let i=1;i<e.length;i++){if(i<e.length-2){let a=e[i],s=e[i+1],l=e[i+2];if(a.type==="number"&&a.value>0&&s.type==="delim"&&s.value===47&&l.type==="number"&&l.value>0){o.push({type:"ratio",numerator:a.value,denominator:l.value,hasSpaceBefore:a.hasSpaceBefore,hasSpaceAfter:l.hasSpaceAfter,start:a.start,end:l.end}),i+=2;continue}}o.push(e[i])}let n=o[1];if(n.type==="ident"&&o.length===3)return{type:"feature",context:"boolean",feature:n.value};if(o.length===5&&o[1].type==="ident"&&o[2].type==="colon"){let i=o[3];if(i.type==="number"||i.type==="dimension"||i.type==="ratio"||i.type==="ident"){let a,s=o[1].value,l=s.slice(0,4);l==="min-"?(a="min",s=s.slice(4)):l==="max-"&&(a="max",s=s.slice(4));let{hasSpaceBefore:p,hasSpaceAfter:u,start:c,end:d,...f}=i;return{type:"feature",context:"value",prefix:a,feature:s,value:f}}return{errid:"EXPECT_VALUE",start:i.start,end:i.end}}if(o.length>=5){let i=gt(o);if(C(i))return{errid:"EXPECT_RANGE",start:r.start,end:o[o.length-1].end,child:i};{let{feature:a,...s}=i;return{type:"feature",context:"range",feature:a,range:s}}}return{errid:"INVALID_FEATURE",start:r.start,end:e[e.length-1].end}}return{errid:"EMPTY_FEATURE",start:0,end:0}},gt=e=>{var r,t,o,n,i,a,s,l;if(e.length<5)return{errid:"INVALID_RANGE",start:(t=(r=e.at(0))===null||r===void 0?void 0:r.start)!==null&&t!==void 0?t:0,end:(n=(o=e.at(-1))===null||o===void 0?void 0:o.end)!==null&&n!==void 0?n:0};if(e[0].type!=="(")return{errid:"EXPECT_LPAREN",start:e[0].start,end:e[0].end};let p=e[e.length-1];if(p.type!==")")return{errid:"EXPECT_RPAREN",start:p.start,end:p.end};let u={feature:""},c=e[1].type==="number"||e[1].type==="dimension"||e[1].type==="ratio"||e[1].type==="ident"&&e[1].value==="infinite";if(e[2].type==="delim"){if(e[2].value===60)e[3].type!=="delim"||e[3].value!==61||e[3].hasSpaceBefore?u[c?"leftOp":"rightOp"]="<":u[c?"leftOp":"rightOp"]="<=";else if(e[2].value===62)e[3].type!=="delim"||e[3].value!==61||e[3].hasSpaceBefore?u[c?"leftOp":"rightOp"]=">":u[c?"leftOp":"rightOp"]=">=";else{if(e[2].value!==61)return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};u[c?"leftOp":"rightOp"]="="}if(c)u.leftToken=e[1];else{if(e[1].type!=="ident")return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};u.feature=e[1].value}let d=2+((a=(i=u[c?"leftOp":"rightOp"])===null||i===void 0?void 0:i.length)!==null&&a!==void 0?a:0),f=e[d];if(c){if(f.type!=="ident")return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};if(u.feature=f.value,e.length>=7){let g=e[d+1],h=e[d+2];if(g.type!=="delim")return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};{let $=g.value;if($===60)h.type!=="delim"||h.value!==61||h.hasSpaceBefore?u.rightOp="<":u.rightOp="<=";else{if($!==62)return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};h.type!=="delim"||h.value!==61||h.hasSpaceBefore?u.rightOp=">":u.rightOp=">="}let Z=d+1+((l=(s=u.rightOp)===null||s===void 0?void 0:s.length)!==null&&l!==void 0?l:0),q=e.at(Z);if(Z+2!==e.length)return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};u.rightToken=q}}else if(d+2!==e.length)return{errid:"INVALID_RANGE",start:e[0].start,end:p.end}}else u.rightToken=f;let m,{leftToken:v,leftOp:b,feature:P,rightOp:y,rightToken:I}=u,E,x;if(v!==void 0){if(v.type==="ident"){let{type:g,value:h}=v;h==="infinite"&&(E={type:g,value:h})}else if(v.type==="number"||v.type==="dimension"||v.type==="ratio"){let{hasSpaceBefore:g,hasSpaceAfter:h,start:$,end:Z,...q}=v;E=q}}if(I!==void 0){if(I.type==="ident"){let{type:g,value:h}=I;h==="infinite"&&(x={type:g,value:h})}else if(I.type==="number"||I.type==="dimension"||I.type==="ratio"){let{hasSpaceBefore:g,hasSpaceAfter:h,start:$,end:Z,...q}=I;x=q}}if(E!==void 0&&x!==void 0)if(b!=="<"&&b!=="<="||y!=="<"&&y!=="<="){if(b!==">"&&b!==">="||y!==">"&&y!==">=")return{errid:"INVALID_RANGE",start:e[0].start,end:p.end};m={leftToken:E,leftOp:b,feature:P,rightOp:y,rightToken:x}}else m={leftToken:E,leftOp:b,feature:P,rightOp:y,rightToken:x};else(E===void 0&&b===void 0&&y!==void 0&&x!==void 0||E!==void 0&&b!==void 0&&y===void 0&&x===void 0)&&(m={leftToken:E,leftOp:b,feature:P,rightOp:y,rightToken:x});return m??{errid:"INVALID_RANGE",start:e[0].start,end:p.end}}return{errid:"INVALID_RANGE",start:e[0].start,end:p.end}};var Ue=e=>({type:"query-list",mediaQueries:e.mediaQueries.map(r=>Ye(r))}),Ye=e=>e.mediaCondition?{type:"query",prefix:e.prefix,mediaType:e.mediaType,mediaCondition:me(e.mediaCondition)}:e,me=e=>{let r=[];for(let t of e.children)if(t.type==="condition"){let o=me(t);o.operator===void 0&&o.children.length===1?r.push(o.children[0]):o.operator!==e.operator||o.operator!=="and"&&o.operator!=="or"?r.push(o):r.push(...o.children)}else r.push(t);if(r.length===1){let t=r[0];if(t.type==="condition"){if(e.operator===void 0)return t;if(e.operator==="not"&&t.operator==="not")return{type:"condition",children:t.children}}}return{type:"condition",operator:e.operator,children:r}};var Qe=e=>{let r=[e];for(let t=e.child;t!==void 0;t=t.child)r.push(t);for(let t=r.length-2;t>=0;t--)r[t+1].child=r.at(t);return delete r[0].child,r.at(-1)},M=e=>{switch(e.type){case"query-list":for(let r of e.mediaQueries)M(r);return e;case"query":return e.prefix===void 0&&delete e.prefix,e.mediaType===void 0&&delete e.mediaType,e.mediaCondition===void 0?delete e.mediaCondition:M(e.mediaCondition),e;case"condition":e.operator===void 0&&delete e.operator;for(let r of e.children)M(r);return e;case"feature":return e.context==="value"?(e.prefix===void 0&&delete e.prefix,M(e.value)):e.context==="range"&&(e.range.leftOp===void 0&&delete e.range.leftOp,e.range.rightOp===void 0&&delete e.range.rightOp,e.range.leftToken===void 0?delete e.range.leftToken:M(e.range.leftToken),e.range.rightToken===void 0?delete e.range.rightToken:M(e.range.rightToken)),e;default:return e}};var he,je=e=>{let r=(()=>{let n;return he?n=he:(n=new TextEncoder,he=n),n})().encode(e),t=[],o=r.length;for(let n=0;n<o;n+=1){let i=r.at(n);if(i<128)switch(i){case 0:t.push(65533);break;case 12:t.push(10);break;case 13:t.push(10),r.at(n+1)===10&&(n+=1);break;default:t.push(i)}else i<224?t.push(i<<59>>>53|r[++n]<<58>>>58):i<240?t.push(i<<60>>>48|r[++n]<<58>>>52|r[++n]<<58>>>58):t.push(i<<61>>>43|r[++n]<<58>>>46|r[++n]<<58>>>52|r[++n]<<58>>>58)}return t};var Ke=e=>{let r=[],t=!1;for(let o of e)switch(o.type){case"{":return{errid:"NO_LCURLY",start:o.start,end:o.end};case"semicolon":return{errid:"NO_SEMICOLON",start:o.start,end:o.end};case"whitespace":t=!0,r.length>0&&(r[r.length-1].hasSpaceAfter=!0);break;case"EOF":break;default:r.push({...o,hasSpaceBefore:t,hasSpaceAfter:!1}),t=!1}return r};var He=(e,r=0)=>{let t=[];for(;r<e.length;r+=1){let o=e.at(r),n=r;if(o===47&&e.at(r+1)===42){r+=2;for(let i=e.at(r);i!==void 0;i=e.at(++r))if(i===42&&e.at(r+1)===47){r+=1;break}}else if(o===9||o===32||o===10){let i=e.at(++r);for(;i===9||i===32||i===10;)i=e.at(++r);r-=1;let a=t.at(-1);(a==null?void 0:a.type)==="whitespace"?(t.pop(),t.push({type:"whitespace",start:a.start,end:r})):t.push({type:"whitespace",start:n,end:r})}else if(o===34){let i=ze(e,r);if(i===null)return{errid:"INVALID_STRING",start:r,end:r};let[a,s]=i;r=a,t.push({type:"string",value:s,start:n,end:r})}else if(o===35){if(r+1<e.length){let i=e.at(r+1);if(i===95||i>=65&&i<=90||i>=97&&i<=122||i>=128||i>=48&&i<=57||i===92&&r+2<e.length&&e.at(r+2)!==10){let a=We(e,r+1)?"id":"unrestricted",s=$e(e,r+1);if(s!==null){let[l,p]=s;r=l,t.push({type:"hash",value:p.toLowerCase(),flag:a,start:n,end:r});continue}}}t.push({type:"delim",value:o,start:n,end:r})}else if(o===39){let i=ze(e,r);if(i===null)return{errid:"INVALID_STRING",start:r,end:r};let[a,s]=i;r=a,t.push({type:"string",value:s,start:n,end:r})}else if(o===40)t.push({type:"(",start:n,end:r});else if(o===41)t.push({type:")",start:n,end:r});else if(o===43){let i=ie(e,r);if(i===null)t.push({type:"delim",value:o,start:n,end:r});else{let[a,s]=i;r=a,s[0]==="dimension"?t.push({type:"dimension",value:s[1],unit:s[2].toLowerCase(),flag:"number",start:n,end:r}):s[0]==="number"?t.push({type:s[0],value:s[1],flag:s[2],start:n,end:r}):t.push({type:s[0],value:s[1],flag:"number",start:n,end:r})}}else if(o===44)t.push({type:"comma",start:n,end:r});else if(o===45){let i=ie(e,r);if(i!==null){let[s,l]=i;r=s,l[0]==="dimension"?t.push({type:"dimension",value:l[1],unit:l[2].toLowerCase(),flag:"number",start:n,end:r}):l[0]==="number"?t.push({type:l[0],value:l[1],flag:l[2],start:n,end:r}):t.push({type:l[0],value:l[1],flag:"number",start:n,end:r});continue}if(r+2<e.length){let s=e.at(r+1),l=e.at(r+2);if(s===45&&l===62){r+=2,t.push({type:"CDC",start:n,end:r});continue}}let a=Xe(e,r);if(a!==null){let[s,l,p]=a;r=s,t.push({type:p,value:l,start:n,end:r});continue}t.push({type:"delim",value:o,start:n,end:r})}else if(o===46){let i=ie(e,r);if(i!==null){let[a,s]=i;r=a,s[0]==="dimension"?t.push({type:"dimension",value:s[1],unit:s[2].toLowerCase(),flag:"number",start:n,end:r}):s[0]==="number"?t.push({type:s[0],value:s[1],flag:s[2],start:n,end:r}):t.push({type:s[0],value:s[1],flag:"number",start:n,end:r});continue}t.push({type:"delim",value:o,start:n,end:r})}else if(o===58)t.push({type:"colon",start:n,end:r});else if(o===59)t.push({type:"semicolon",start:n,end:r});else if(o===60){if(r+3<e.length){let i=e.at(r+1),a=e.at(r+2),s=e.at(r+3);if(i===33&&a===45&&s===45){r+=3,t.push({type:"CDO",start:n,end:r});continue}}t.push({type:"delim",value:o,start:n,end:r})}else if(o===64){let i=ye(e,r+1);if(i!==null){let[a,s]=i;r=a,t.push({type:"at-keyword",value:s.toLowerCase(),start:n,end:r});continue}t.push({type:"delim",value:o,start:n,end:r})}else if(o===91)t.push({type:"[",start:n,end:r});else if(o===93)t.push({type:"]",start:n,end:r});else if(o===123)t.push({type:"{",start:n,end:r});else if(o===125)t.push({type:"}",start:n,end:r});else if(o>=48&&o<=57){let i=ie(e,r),[a,s]=i;r=a,s[0]==="dimension"?t.push({type:"dimension",value:s[1],unit:s[2].toLowerCase(),flag:"number",start:n,end:r}):s[0]==="number"?t.push({type:s[0],value:s[1],flag:s[2],start:n,end:r}):t.push({type:s[0],value:s[1],flag:"number",start:n,end:r})}else if(o===95||o>=65&&o<=90||o>=97&&o<=122||o>=128||o===92){let i=Xe(e,r);if(i===null)t.push({type:"delim",value:o,start:n,end:r});else{let[a,s,l]=i;r=a,t.push({type:l,value:s,start:n,end:r})}}else t.push({type:"delim",value:o,start:n,end:r})}return t.push({type:"EOF",start:r,end:r}),t},ze=(e,r)=>{if(e.length<=r+1)return null;let t=e.at(r),o=[];for(let n=r+1;n<e.length;n+=1){let i=e.at(n);if(i===t)return[n,String.fromCodePoint(...o)];if(i===92){let a=ve(e,n);if(a===null)return null;let[s,l]=a;o.push(l),n=s}else{if(i===10)return null;o.push(i)}}return null},We=(e,r)=>{let t=e.at(r);if(t===void 0)return!1;if(t===45){let o=e.at(r+1);return o===void 0?!1:o===45||o===95||o>=65&&o<=90||o>=97&&o<=122||o>=128?!0:o===92?e.length<=r+2?!1:e.at(r+2)!==10:!1}return t===95||t>=65&&t<=90||t>=97&&t<=122||t>=128?!0:t===92?e.length<=r+1?!1:e.at(r+1)!==10:!1},ve=(e,r)=>{if(e.length<=r+1||e.at(r)!==92)return null;let t=e.at(r+1);if(t===10)return null;if(t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102){let o=[t],n=Math.min(r+7,e.length),i=r+2;for(;i<n;i+=1){let a=e.at(i);if(!(a>=48&&a<=57||a>=65&&a<=70||a>=97&&a<=102))break;o.push(a)}if(i<e.length){let a=e.at(i);a!==9&&a!==32&&a!==10||(i+=1)}return[i-1,Number.parseInt(String.fromCodePoint(...o),16)]}return[r+1,t]},ie=(e,r)=>{let t=xt(e,r);if(t===null)return null;let[o,n,i]=t,a=ye(e,o+1);if(a!==null){let[s,l]=a;return[s,["dimension",n,l]]}return o+1<e.length&&e.at(o+1)===37?[o+1,["percentage",n]]:[o,["number",n,i]]},xt=(e,r)=>{let t=e.at(r);if(t===void 0)return null;let o="integer",n=[];for(t!==43&&t!==45||(r+=1,t===45&&n.push(45));r<e.length;){let s=e.at(r);if(!(s>=48&&s<=57))break;n.push(s),r+=1}if(r+1<e.length){let s=e.at(r),l=e.at(r+1);if(s===46&&l>=48&&l<=57)for(n.push(s,l),o="number",r+=2;r<e.length;){let p=e.at(r);if(!(p>=48&&p<=57))break;n.push(p),r+=1}}if(r+1<e.length){let s=e.at(r),l=e.at(r+1),p=e.at(r+2);if(s===69||s===101){let u=!1;if(l>=48&&l<=57?(n.push(69,l),r+=2,u=!0):(l===45||l===43)&&p!==void 0&&p>=48&&p<=57&&(n.push(69),l===45&&n.push(45),n.push(p),r+=3,u=!0),u)for(o="number";r<e.length;){let c=e.at(r);if(!(c>=48&&c<=57))break;n.push(c),r+=1}}}let i=String.fromCodePoint(...n),a=o==="number"?Number.parseFloat(i):Number.parseInt(i);return a===0&&(a=0),Number.isNaN(a)?null:[r-1,a,o]},$e=(e,r)=>{if(e.length<=r)return null;let t=[];for(let o=e.at(r);r<e.length;o=e.at(++r)){if(!(o===45||o===95||o>=65&&o<=90||o>=97&&o<=122||o>=128||o>=48&&o<=57)){{let n=ve(e,r);if(n!==null){let[i,a]=n;t.push(a),r=i;continue}}break}t.push(o)}return r===0?null:[r-1,String.fromCodePoint(...t)]},ye=(e,r)=>We(e,r)?$e(e,r):null,bt=(e,r)=>{let t=e.at(r);for(;t===9||t===32||t===10;)t=e.at(++r);let o=[],n=!1;for(;r<e.length;){if(t===41)return[r,String.fromCodePoint(...o)];if(t===34||t===39||t===40)return null;if(t===9||t===32||t===10)!n&&o.length>0&&(n=!0);else if(t===92){let i=ve(e,r);if(i===null||n)return null;let[a,s]=i;o.push(s),r=a}else{if(n)return null;o.push(t)}t=e.at(++r)}return null},Xe=(e,r)=>{let t=ye(e,r);if(t===null)return null;let[o,n]=t;if(n.toLowerCase()==="url"){if(e.length>o+1&&e.at(o+1)===40){for(let i=2;o+i<e.length;i+=1){let a=e.at(o+i);if(a===34||a===39)return[o+1,n.toLowerCase(),"function"];if(a!==9&&a!==32&&a!==10){let s=bt(e,o+i);if(s===null)return null;let[l,p]=s;return[l,p,"url"]}}return[o+1,n.toLowerCase(),"function"]}}else if(e.length>o+1&&e.at(o+1)===40)return[o+1,n.toLowerCase(),"function"];return[o,n.toLowerCase(),"ident"]};var Ze=e=>{let r=He(je(e));return C(r)?r:Ke(r)};var ae=e=>typeof e=="object"&&e!==null&&"errid"in e;var Je=e=>{let r=Ze(e);return ae(r)?Qe(r):M(Ue(Be(r)))};var le={"any-hover":{none:1,hover:1},"any-pointer":{none:1,coarse:1,fine:1},"color-gamut":{srgb:1,p3:1,rec2020:1},grid:{0:1,1:1},hover:{none:1,hover:1},"overflow-block":{none:1,scroll:1,paged:1},"overflow-inline":{none:1,scroll:1},pointer:{none:1,coarse:1,fine:1},scan:{interlace:1,progressive:1},update:{none:1,slow:1,fast:1},"display-mode":{fullscreen:1,standalone:1,"minimal-ui":1,browser:1},"dynamic-range":{standard:1,high:1},"environment-blending":{opaque:1,additive:1,subtractive:1},"forced-colors":{none:1,active:1},"inverted-colors":{none:1,inverted:1},"nav-controls":{none:1,back:1},"prefers-color-scheme":{light:1,dark:1},"prefers-contrast":{"no-preference":1,less:1,more:1,custom:1},"prefers-reduced-data":{"no-preference":1,reduce:1},"prefers-reduced-motion":{"no-preference":1,reduce:1},"prefers-reduced-transparency":{"no-preference":1,reduce:1},scripting:{none:1,"initial-only":1,enabled:1},"video-color-gamut":{srgb:1,p3:1,rec2020:1},"video-dynamic-range":{standard:1,high:1}},j={color:{feature:"color",type:"integer",bounds:[!0,0,1/0,!1]},"color-index":{feature:"color-index",type:"integer",bounds:[!0,0,1/0,!1]},monochrome:{feature:"monochrome",type:"integer",bounds:[!0,0,1/0,!1]},"device-height":{feature:"device-height",type:"length",bounds:[!0,0,1/0,!1]},"device-width":{feature:"device-width",type:"length",bounds:[!0,0,1/0,!1]},height:{feature:"height",type:"length",bounds:[!0,0,1/0,!1]},width:{feature:"width",type:"length",bounds:[!0,0,1/0,!1]},resolution:{feature:"resolution",type:"resolution",bounds:[!0,0,1/0,!1]},"horizontal-viewport-segments":{feature:"horizontal-viewport-segments",type:"integer",bounds:[!0,0,1/0,!1]},"vertical-viewport-segments":{feature:"vertical-viewport-segments",type:"integer",bounds:[!0,0,1/0,!1]}},ge={"aspect-ratio":{feature:"aspect-ratio",type:"ratio",bounds:[!1,[0,1],[1/0,1],!1]},"device-aspect-ratio":{feature:"device-aspect-ratio",type:"ratio",bounds:[!1,[0,1],[1/0,1],!1]}},K=e=>Object.entries(e).filter(r=>r[1]!==void 0),et=new Set(Object.keys(le)),tt=e=>et.has(e[0]),ue=e=>et.has(e),rt=new Set(Object.keys(ge)),z=e=>rt.has(e[0]),X=e=>rt.has(e),nt=new Set(Object.keys(j)),xe=e=>nt.has(e[0]),ot=e=>nt.has(e),it=e=>xe(e)||z(e),be=e=>ot(e)||X(e);var Ee=e=>ot(e)||X(e)||ue(e),V=(e,r)=>{e[r[0]]=r[1]},se=(...e)=>e.reduce((r,t)=>r==="{true}"?t:t==="{true}"?r:r==="{false}"||t==="{false}"?"{false}":((o,n)=>{let[i,a,s,l]=o,p=typeof a=="number"?a:a[0]/a[1],u=typeof s=="number"?s:s[0]/s[1],[c,d,f,m]=n,v=typeof d=="number"?d:d[0]/d[1],b=typeof f=="number"?f:f[0]/f[1],P=i!==c&&!i;p!==v&&(P=p>v);let y=l!==m&&!l;u!==b&&(y=u<b);let I=P?i:c,E=P?a:d,x=y?s:f,g=y?l:m;return E>x||E===x&&(!I||!g)?"{false}":[I,E,x,g]})(r,t),"{true}"),pe=e=>{if(z(e)){let{bounds:r}=ge[e[0]],t=se(e[1],r);if(typeof t=="string")return t;if(t[0]===r[0]&&t[1][0]===r[1][0]&&t[1][1]===r[1][1]&&t[2][0]===r[2][0]&&t[2][1]===r[2][1]&&t[3]===r[3])return"{true}";{let o=t[1][0]/t[1][1],n=t[2][0]/t[2][1];return o>n||o===n&&(!t[0]||!t[3])?"{false}":t}}{let{bounds:r}=j[e[0]],t=se(e[1],r);return typeof t=="string"?t:t[0]===r[0]&&t[1]===r[1]&&t[2]===r[2]&&t[3]===r[3]?"{true}":t[1]>t[2]||t[1]===t[2]&&(!t[0]||!t[3])?"{false}":t}};var at=e=>{if(typeof e[1]=="string")throw new Error("expected range");let{bounds:r}=ge[e[0]],[t,o,n,i]=e[1],a=o[0]/o[1],s=n[0]/n[1],l=r[1][0]/r[1][1],p=r[0],u=r[2][0]/r[2][1],c=r[3],d=s>u||s===u&&!(c&&!i);return a<l||a===l&&!(p&&!t)?d?"{false}":[[!i,n,r[2],r[3]]]:d?[[r[0],r[1],o,!t]]:[[r[0],r[1],o,!t],[!i,n,r[2],r[3]]]};function st(e){if(typeof e[1]=="string")throw new Error("expected range");let{bounds:r}=j[e[0]],[t,o,n,i]=e[1],a=r[1],s=r[0],l=r[2],p=r[3],u=n>l||n===l&&!(p&&!i);return o<a||o===a&&!(s&&!t)?u?"{false}":[[!i,n,r[2],r[3]]]:u?[[r[0],r[1],o,!t]]:[[r[0],r[1],o,!t],[!i,n,r[2],r[3]]]}var Et={widthPx:1920,heightPx:1080,writingMode:"horizontal-tb",emPx:16,lhPx:16,exPx:8,chPx:8,capPx:11,icPx:16},O=(e,r)=>{if(e.type==="number")return{type:"number",value:e.value};if(e.type==="dimension"){let t;switch(e.unit){case"s":case"ms":t="time";break;case"hz":case"khz":t="frequency";break;case"dpi":case"dpcm":case"dppx":case"x":t="resolution";break;default:t="length"}if(e.unit==="px")return{type:"dimension",subtype:"length",px:e.value};if(t==="time")return{type:"dimension",subtype:"time",ms:e.unit==="s"?Math.round(1e3*e.value):e.value};if(t==="frequency")return{type:"dimension",subtype:"frequency",hz:e.unit==="khz"?Math.round(1e3*e.value):e.value};if(t==="resolution"){let o=e.value;return e.unit==="dpi"?o=Number.parseFloat((.0104166667*e.value).toFixed(3)):e.unit==="dpcm"&&(o=Number.parseFloat((.0264583333*e.value).toFixed(3))),{type:"dimension",subtype:"resolution",dppx:o}}if(e.unit in r){let o=r[e.unit];return{type:"dimension",subtype:"length",px:Number.parseFloat((e.value*o).toFixed(3))}}return{type:"ident",value:"{never}"}}return e.type==="ident"?e.value==="infinite"?{type:"infinite"}:{type:"ident",value:e.value}:{type:"ratio",numerator:e.numerator,denominator:e.denominator}},lt=e=>{let r={};typeof e.emPx=="number"&&(r={exPx:Math.round(.5*e.emPx),chPx:Math.round(.5*e.emPx),capPx:Math.round(.7*e.emPx),icPx:Math.round(e.emPx)});let t={...Et,...r,...e},{widthPx:o,heightPx:n,writingMode:i,emPx:a,lhPx:s,exPx:l,chPx:p,capPx:u,icPx:c}=t,d=o/100,f=n/100;return{em:a,rem:a,lh:s,rlh:s,ex:l,ch:p,cap:u,ic:c,vw:d,vh:f,vmin:Math.min(f,d),vmax:Math.max(f,d),vi:i==="horizontal-tb"?d:f,vb:i==="horizontal-tb"?f:d,cm:37.79527559,mm:.03779527559,in:96,q:.009448818898,pc:16,pt:16}},Tt={"<":">","<=":">=",">":"<",">=":"<="},ut=(e,r)=>{if(e.context==="range"){if(be(e.feature)){let{range:t,feature:o}=e;return t.leftToken!==void 0&&t.rightToken!==void 0?t.leftOp==="<"||t.leftOp==="<="?{type:"double",name:o,minOp:t.leftOp,min:O(t.leftToken,r),maxOp:t.rightOp,max:O(t.rightToken,r)}:{type:"double",name:o,minOp:t.rightOp===">"?"<":"<=",min:O(t.rightToken,r),maxOp:t.leftOp?"<":"<=",max:O(t.leftToken,r)}:t.rightToken===void 0?t.leftOp==="="?{type:"equals",name:o,value:O(t.leftToken,r)}:{type:"single",name:o,op:Tt[t.leftOp],value:O(t.leftToken,r)}:t.rightOp==="="?{type:"equals",name:o,value:O(t.rightToken,r)}:{type:"single",name:o,op:t.rightOp,value:O(t.rightToken,r)}}}else if(e.context==="value"){if(e.feature==="orientation"){if(e.prefix===void 0&&e.value.type==="ident"){if(e.value.value==="portrait")return{type:"single",name:"aspect-ratio",op:"<=",value:{type:"ratio",numerator:1,denominator:1}};if(e.value.value==="landscape")return{type:"single",name:"aspect-ratio",op:">=",value:{type:"ratio",numerator:1,denominator:1}}}}else if(Ee(e.feature)){if(e.prefix===void 0)return{type:"equals",name:e.feature,value:O(e.value,r)};if(be(e.feature))return e.prefix==="min"?{type:"single",name:e.feature,op:">=",value:O(e.value,r)}:{type:"single",name:e.feature,op:"<=",value:O(e.value,r)}}}else{if(e.feature==="orientation")return{type:"double",name:"aspect-ratio",min:{type:"ratio",numerator:0,denominator:1},minOp:"<",maxOp:"<",max:{type:"ratio",numerator:Number.POSITIVE_INFINITY,denominator:1}};if(Ee(e.feature))return{type:"boolean",name:e.feature}}return{type:"invalid",name:e.feature}},H=e=>e.type==="number"&&e.value>0?[e.value,1]:e.type==="ratio"?[e.numerator,e.denominator]:null,W=(e,r)=>{let t=j[r];if(e.type==="infinite"){if(r==="resolution")return Number.POSITIVE_INFINITY}else if(t.type==="integer"){if(e.type==="number"&&Number.isInteger(e.value))return e.value}else if(t.type==="resolution"){if(e.type==="dimension"&&e.subtype==="resolution")return e.dppx}else if(t.type==="length"){if(e.type==="dimension"&&e.subtype==="length")return e.px;if(e.type==="number"&&e.value===0)return 0}return null};var pt=(e,r)=>{let t=[];for(let o of e)for(let n of r){let i=Pt(o,n);Object.keys(i).length>0&&t.push(i)}return t},Pt=(e,r)=>{let t={};for(let o of K(e))o[1]!==void 0&&V(t,o);for(let o of K(r))if(o[0]in t){if(t[o[0]]!==void 0){let n=t;if(o[0]!=="media-type"){if(o[0]==="invalid-features")n[o[0]].push(...o[1]);else if(n[o[0]]==="{false}"||o[1]==="{false}")n[o[0]]="{false}";else if(n[o[0]]==="{true}")V(n,o);else if(o[1]!=="{true}"){let i=t;xe(o)||z(o)?V(i,[o[0],se(i[o[0]],o[1])]):o[0]==="color-gamut"||o[0]==="video-color-gamut"?i[o[0]]=[i[o[0]][0]&&o[1][0],i[o[0]][1]&&o[1][1],i[o[0]][2]&&o[1][2],i[o[0]][3]&&o[1][3]]:V(i,[o[0],i[o[0]]===o[1]?i[o[0]]:"{false}"])}}}}else V(t,o);return t},ct=e=>e.map(r=>dt(r)).reduce((r,t)=>pt(r,t)),dt=e=>{let r=K(e),t=[];for(let n of r)if(n[1]!==void 0){let i,a;if(n[0]==="invalid-features")return[{[n[0]]:n[1]}];if(n[0]==="media-type")continue;if(i=n,n[1]==="{false}")a=[[n[0],"{true}"]];else if(n[1]==="{true}")a=[[n[0],"{false}"]];else if(tt(n))if(n[0]==="color-gamut"){let s=n[1];a=[["color-gamut",[!s[0],!s[1],!s[2],!s[3]]]]}else a=n[0]==="grid"?[["grid",n[1]===0?1:0]]:Object.keys(le[n[0]]).filter(s=>s!==n[1]).map(s=>[n[0],s]);else if(z(n)){let s=at(n);a=(s==="{false}"?["{false}"]:s).map(l=>[n[0],l])}else{let s=st(n);a=(s==="{false}"?["{false}"]:s).map(l=>[n[0],l])}t.push([i,a])}let o=[];for(let[,n]of t)for(let i of n)o.push({[i[0]]:i[1]});return o},It=(e,r)=>{let t=ut(e,r),o=[{"invalid-features":[e.feature]}];if(t.type==="invalid")return o;if(t.type==="boolean")return t.name==="color-gamut"?[{"color-gamut":[!1,!0,!0,!0]}]:t.name==="grid"?[{grid:1}]:ue(t.name)?dt({[t.name]:"none"}):X(t.name)?[{[t.name]:[!1,[0,1],[Number.POSITIVE_INFINITY,1],!0]}]:[{[t.name]:[!1,0,Number.POSITIVE_INFINITY,!0]}];if(ue(t.name)){if(t.type==="equals"){let n=t.value;if(t.name==="grid"){if(n.type==="number"&&(n.value===0||n.value===1))return[{grid:n.value}]}else if(n.type==="ident"&&n.value in le[t.name]){if(t.name!=="color-gamut")return[{[t.name]:n.value}];{let i=["srgb","p3","rec2020"].indexOf(n.value);if(i!==-1)return[{"color-gamut":[!1,i<=0,i<=1,i<=2]}]}}}return o}if(X(t.name)){let n=null;if(t.type==="equals"){let i=H(t.value);i!==null&&(n=[!0,i,i,!0])}else if(t.type==="single"){let i=H(t.value);i!==null&&(n=t.op==="<"?[!0,[Number.NEGATIVE_INFINITY,1],i,!1]:t.op==="<="?[!0,[Number.NEGATIVE_INFINITY,1],i,!0]:t.op===">"?[!1,i,[Number.POSITIVE_INFINITY,1],!0]:[!0,i,[Number.POSITIVE_INFINITY,1],!0])}else if(t.type==="double"){let i=H(t.min),a=H(t.max);i!==null&&a!==null&&(n=[t.minOp==="<=",i,a,t.maxOp==="<="])}return n===null?o:[{[t.name]:pe([t.name,n])}]}{let n=null;if(t.type==="equals"){let i=W(t.value,t.name);i!==null&&(n=[!0,i,i,!0])}else if(t.type==="single"){let i=W(t.value,t.name);i!==null&&(n=t.op==="<"?[!0,Number.NEGATIVE_INFINITY,i,!1]:t.op==="<="?[!0,Number.NEGATIVE_INFINITY,i,!0]:t.op===">"?[!1,i,Number.POSITIVE_INFINITY,!0]:[!0,i,Number.POSITIVE_INFINITY,!0])}else if(t.type==="double"){let i=W(t.min,t.name),a=W(t.max,t.name);i!==null&&a!==null&&(n=[t.minOp==="<=",i,a,t.maxOp==="<="])}return n===null?o:[{[t.name]:pe([t.name,n])}]}},Te=(e,r)=>{let t=[];for(let o of e.children)"context"in o?t.push(It(o,r)):t.push(Te(o,r));return e.operator==="or"||e.operator===void 0?t.flat():e.operator==="and"?t.reduce((o,n)=>pt(o,n)):ct(t[0])},wt=e=>{let r=[],t=new Set,o=new Set;for(let n of e){let i=!1;if(Array.isArray(n["invalid-features"])&&n["invalid-features"].length>0){for(let s of n["invalid-features"])t.add(s);i=!0}let a={};for(let s of K(n))if(s[0]!=="invalid-features"){if(s[0]==="color-gamut"){let l=s[1].toString();l==="false,false,false,false"?s[1]="{false}":l==="true,true,true,true"&&(s[1]="{true}")}else it(s)&&(s[1]=pe(s));s[1]==="{false}"?(o.add(s[0]),i=!0):s[1]==="{true}"||s[0]==="media-type"&&s[1]==="all"||V(a,s)}i||r.push(a)}return{simplePerms:r,invalidFeatures:[...t].sort(),falseFeatures:[...o].sort()}},Nt=(e,r={})=>{let t=lt(r),o=[];for(let n of e.mediaQueries){let i=[];n.prefix==="not"?(n.mediaType==="print"?i.push({"media-type":"not-print"}):n.mediaType==="screen"&&i.push({"media-type":"not-screen"}),n.mediaCondition!==void 0&&i.push(...ct(Te(n.mediaCondition,t)))):n.mediaCondition===void 0?i.push({"media-type":n.mediaType}):i.push(...Te(n.mediaCondition,t).map(a=>({...a,"media-type":n.mediaType}))),o.push(...i)}return wt(o)},ft=(e,r={})=>{let t=Je(e);if(ae(t))throw new Error(`Error parsing media query list: ${t.errid} at chars ${t.start}:${t.end}`);return Nt(t,r)};var kt={mediaType:"screen",anyHover:"hover",anyPointer:"fine",colorGamut:"srgb-but-not-p3",grid:"bitmap",hover:"hover",overflowBlock:"scroll",overflowInline:"scroll",pointer:"fine",scan:"progressive",update:"fast",colorIndex:"none",colorBits:8,monochromeBits:"not-monochrome",displayMode:"browser",dynamicRange:"not-hdr",environmentBlending:"opaque",forcedColors:"none",invertedColors:"none",navControls:"back",prefersColorScheme:"no-preference",prefersContrast:"no-preference",prefersReducedData:"no-preference",prefersReducedMotion:"no-preference",prefersReducedTransparency:"no-preference",scripting:"enabled",videoColorGamut:"srgb-but-not-p3",videoDynamicRange:"not-hdr",horizontalViewportSegments:1,verticalViewportSegments:1},w=e=>new Error(`Invalid option: ${e}`),Ot=e=>{if(e.mediaType!=="screen"&&e.mediaType!=="print"&&e.mediaType!=="not-screen-or-print")throw w("mediaType");if(e.anyHover!=="none"&&e.anyHover!=="hover")throw w("anyHover");if(e.anyPointer!=="none"&&e.anyPointer!=="coarse"&&e.anyPointer!=="fine")throw w("anyPointer");if(e.colorGamut!=="not-srgb"&&e.colorGamut!=="srgb-but-not-p3"&&e.colorGamut!=="p3-but-not-rec2020"&&e.colorGamut!=="rec2020")throw w("colorGamut");if(e.grid!=="bitmap"&&e.grid!=="grid")throw w("grid");if(e.hover!=="none"&&e.hover!=="hover")throw w("hover");if(e.overflowBlock!=="none"&&e.overflowBlock!=="scroll"&&e.overflowBlock!=="paged")throw w("overflowBlock");if(e.overflowInline!=="none"&&e.overflowInline!=="scroll")throw w("overflowInline");if(e.pointer!=="none"&&e.pointer!=="coarse"&&e.pointer!=="fine")throw w("pointer");if(e.scan!=="interlace"&&e.scan!=="progressive")throw w("scan");if(e.update!=="none"&&e.update!=="slow"&&e.update!=="fast")throw w("update");if(!(Number.isInteger(e.widthPx)&&e.widthPx>=0))throw w("widthPx");if(!(Number.isInteger(e.heightPx)&&e.heightPx>=0))throw w("heightPx");if(!(Number.isInteger(e.deviceWidthPx)&&e.deviceWidthPx>=0))throw w("deviceWidthPx");if(!(Number.isInteger(e.deviceHeightPx)&&e.deviceHeightPx>=0))throw w("deviceHeightPx");if(!(Number.isInteger(e.colorBits)&&e.colorBits>=0))throw w("colorBits");if(e.dppx<=0)throw w("dppx");if(e.monochromeBits!=="not-monochrome"&&!(Number.isInteger(e.monochromeBits)&&e.monochromeBits>=0))throw w("monochromeBits");if(e.colorIndex!=="none"&&!(Number.isInteger(e.colorIndex)&&e.colorIndex>=0))throw w("colorIndex")},mt=(e,r)=>{let t={...kt,...r};Ot(t);for(let o of e.simplePerms){let n=!0;for(let i in o){let a=i,s=o;if(a==="media-type"){let l=s[a];if(l==="print"){if(t.mediaType==="screen"||t.mediaType==="not-screen-or-print"){n=!1;break}}else if(l==="screen"){if(t.mediaType==="print"||t.mediaType==="not-screen-or-print"){n=!1;break}}else if(l==="not-screen"){if(t.mediaType==="screen"){n=!1;break}}else if(t.mediaType==="print"){n=!1;break}}else if(a==="any-hover"){if(s[a]!==t.anyHover){n=!1;break}}else if(a==="hover"){if(s[a]!==t.hover){n=!1;break}}else if(a==="any-pointer"){if(s[a]!==t.anyPointer){n=!1;break}}else if(a==="pointer"){if(s[a]!==t.pointer){n=!1;break}}else if(a==="grid"){let l=s[a];if(l===0&&t.grid==="grid"||l===1&&t.grid==="bitmap"){n=!1;break}}else if(a==="color-gamut"){let[l,p,u,c]=s[a];if(t.colorGamut==="not-srgb"&&!l||t.colorGamut==="srgb-but-not-p3"&&!p||t.colorGamut==="p3-but-not-rec2020"&&!u||t.colorGamut==="rec2020"&&!c){n=!1;break}}else if(a==="video-color-gamut"){let[l,p,u,c]=s[a];if(t.videoColorGamut==="not-srgb"&&!l||t.videoColorGamut==="srgb-but-not-p3"&&!p||t.videoColorGamut==="p3-but-not-rec2020"&&!u||t.videoColorGamut==="rec2020"&&!c){n=!1;break}}else if(a==="overflow-block"){if(s[a]!==t.overflowBlock){n=!1;break}}else if(a==="overflow-inline"){if(s[a]!==t.overflowInline){n=!1;break}}else if(a==="scan"){if(s[a]!==t.scan){n=!1;break}}else if(a==="update"){if(s[a]!==t.update){n=!1;break}}else if(a==="scripting"){if(s[a]!==t.scripting){n=!1;break}}else if(a==="display-mode"){if(s[a]!==t.displayMode){n=!1;break}}else if(a==="environment-blending"){if(s[a]!==t.environmentBlending){n=!1;break}}else if(a==="forced-colors"){if(s[a]!==t.forcedColors){n=!1;break}}else if(a==="inverted-colors"){if(s[a]!==t.invertedColors){n=!1;break}}else if(a==="nav-controls"){if(s[a]!==t.navControls){n=!1;break}}else if(a==="prefers-color-scheme"){if(s[a]!==t.prefersColorScheme){n=!1;break}}else if(a==="prefers-contrast"){if(s[a]!==t.prefersContrast){n=!1;break}}else if(a==="prefers-reduced-data"){if(s[a]!==t.prefersReducedData){n=!1;break}}else if(a==="prefers-reduced-motion"){if(s[a]!==t.prefersReducedMotion){n=!1;break}}else if(a==="prefers-reduced-transparency"){if(s[a]!==t.prefersReducedTransparency){n=!1;break}}else if(a==="dynamic-range"){if(s[a]==="high"&&t.dynamicRange==="not-hdr"){n=!1;break}}else if(a==="video-dynamic-range"){if(s[a]==="high"&&t.videoDynamicRange==="not-hdr"){n=!1;break}}else if(a==="vertical-viewport-segments"){let[l,p,u,c]=s[a];if(t.verticalViewportSegments<p||t.verticalViewportSegments>u||p===t.verticalViewportSegments&&!l||u===t.verticalViewportSegments&&!c){n=!1;break}}else if(a==="horizontal-viewport-segments"){let[l,p,u,c]=s[a];if(t.horizontalViewportSegments<p||t.horizontalViewportSegments>u||p===t.horizontalViewportSegments&&!l||u===t.horizontalViewportSegments&&!c){n=!1;break}}else if(a==="width"){let[l,p,u,c]=s[a];if(t.widthPx<p||t.widthPx>u||p===t.widthPx&&!l||u===t.widthPx&&!c){n=!1;break}}else if(a==="device-width"){let[l,p,u,c]=s[a];if(t.deviceWidthPx<p||t.deviceWidthPx>u||p===t.deviceWidthPx&&!l||u===t.deviceWidthPx&&!c){n=!1;break}}else if(a==="height"){let[l,p,u,c]=s[a];if(t.heightPx<p||t.heightPx>u||p===t.heightPx&&!l||u===t.heightPx&&!c){n=!1;break}}else if(a==="device-height"){let[l,p,u,c]=s[a];if(t.deviceHeightPx<p||t.deviceHeightPx>u||p===t.deviceHeightPx&&!l||u===t.deviceHeightPx&&!c){n=!1;break}}else if(a==="color"){let[l,p,u,c]=s[a];if(t.colorBits<p||t.colorBits>u||p===t.colorBits&&!l||u===t.colorBits&&!c){n=!1;break}}else if(a==="monochrome"){let[l,p,u,c]=s[a];if(t.monochromeBits==="not-monochrome")(p>0||p===0&&!l||u===0&&!c)&&(n=!1);else if(t.monochromeBits<p||t.monochromeBits>u||p===t.monochromeBits&&!l||u===t.monochromeBits&&!c){n=!1;break}}else if(a==="resolution"){let[l,p,u,c]=s[a];if(t.dppx<p||t.dppx>u||p===t.dppx&&!l||u===t.dppx&&!c){n=!1;break}}else if(a==="color-index"){let[l,p,u,c]=s[a];if(t.colorIndex==="none"){if(p>0||p===0&&!l||u===0&&!c){n=!1;break}}else if(t.colorIndex<p||t.colorIndex>u||p===t.colorIndex&&!l||u===t.colorIndex&&!c){n=!1;break}}else if(a==="aspect-ratio"){let[l,p,u,c]=s[a],d=p[0]/p[1],f=u[0]/u[1],m=t.widthPx/t.heightPx;if(m<d||m>f||d===m&&!l||f===m&&!c){n=!1;break}}else{let[l,p,u,c]=s[a],d=p[0]/p[1],f=u[0]/u[1],m=t.deviceWidthPx/t.deviceHeightPx;if(m<d||m>f||d===m&&!l||f===m&&!c){n=!1;break}}}if(n)return!0}return!1};function _t(e){let{useObjectSyntax:r=!1}=e??{};return t=>{let o=r?[":where([style]) {}"]:[],n=[];Rt(t,(u,c)=>{u.type===Ct&&u.name==="style"&&(o.push(u.children.map(d=>d.type===At?d.value:"").join("")),n.push(()=>{c.children=c.children.filter(d=>d!==u)}))});for(let u of n)u();let i=o.join(`
`),a=fe(i),s=new Map;function l(u){if(u.type==="rule"){let c=Object.fromEntries(u.children.map(d=>[d.props,d.children]));for(let d of u.props){let f=Object.assign(s.get(d)??{},c);s.set(d,f)}}else if(u.type==="@media"&&(e!=null&&e.env)){let c=Mt(e.env),f=(Array.isArray(u.props)?u.props:[u.props]).map(m=>ft(m));for(let m of f)if(mt(m,c)){for(let v of u.children)l(v);return}}}for(let u of a)l(u);let p=new Map;for(let[u,c]of Array.from(s).sort(([d],[f])=>{let m=ht(d),v=ht(f);return m>v?1:v>m?-1:0})){let d=St(t,u);for(let f of d){let m=p.get(f)??{};p.set(f,Object.assign(m,c))}}for(let[u,c]of p){let d=u.attributes.style??"",f={};for(let m of fe(d))m.type==="decl"&&typeof m.props=="string"&&typeof m.children=="string"&&(f[m.props]=m.children);f=Object.assign({},c,f),r?u.attributes.style=f:u.attributes.style=`${Object.entries(f).map(([m,v])=>`${m}:${v.replace("!important","")};`).join("")}`}return t}}function Mt(e){let{width:r,height:t,dppx:o=1,widthPx:n=r,heightPx:i=t,deviceWidthPx:a=r*o,deviceHeightPx:s=t*o,...l}=e;return{widthPx:n,heightPx:i,deviceWidthPx:a,deviceHeightPx:s,dppx:o,...l}}export{_t as default};
/*! Bundled license information:

media-query-parser/dist/index.js:
  (**! media-query-parser | Tom Golden <<EMAIL>> (https://tom.bio) | @license MIT  *)
*/
