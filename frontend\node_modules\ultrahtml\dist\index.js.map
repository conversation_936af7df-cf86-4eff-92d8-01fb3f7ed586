{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["export type Node =\n\t| DocumentNode\n\t| ElementNode\n\t| TextNode\n\t| CommentNode\n\t| DoctypeNode;\nexport type NodeType =\n\t| typeof DOCUMENT_NODE\n\t| typeof ELEMENT_NODE\n\t| typeof TEXT_NODE\n\t| typeof COMMENT_NODE\n\t| typeof DOCTYPE_NODE;\nexport interface Location {\n\tstart: number;\n\tend: number;\n}\ninterface BaseNode {\n\ttype: NodeType;\n\tloc: [Location, Location];\n\tparent: Node;\n\t[key: string]: any;\n}\n\ninterface LiteralNode extends BaseNode {\n\tvalue: string;\n}\n\ninterface ParentNode extends BaseNode {\n\tchildren: Node[];\n}\n\nexport interface DocumentNode extends Omit<ParentNode, 'parent'> {\n\ttype: typeof DOCUMENT_NODE;\n\tattributes: Record<string, string>;\n\tparent: undefined;\n}\n\nexport interface ElementNode extends ParentNode {\n\ttype: typeof ELEMENT_NODE;\n\tname: string;\n\tattributes: Record<string, string>;\n}\n\nexport interface TextNode extends LiteralNode {\n\ttype: typeof TEXT_NODE;\n}\n\nexport interface CommentNode extends LiteralNode {\n\ttype: typeof COMMENT_NODE;\n}\n\nexport interface DoctypeNode extends LiteralNode {\n\ttype: typeof DOCTYPE_NODE;\n}\n\nexport const DOCUMENT_NODE = 0;\nexport const ELEMENT_NODE = 1;\nexport const TEXT_NODE = 2;\nexport const COMMENT_NODE = 3;\nexport const DOCTYPE_NODE = 4;\n\nexport function h(\n\ttype: any,\n\tprops: null | Record<string, any> = {},\n\t...children: any[]\n) {\n\tconst vnode: ElementNode = {\n\t\ttype: ELEMENT_NODE,\n\t\tname: typeof type === 'function' ? type.name : type,\n\t\tattributes: props || {},\n\t\tchildren: children.map((child) =>\n\t\t\ttypeof child === 'string'\n\t\t\t\t? { type: TEXT_NODE, value: escapeHTML(String(child)) }\n\t\t\t\t: child,\n\t\t),\n\t\tparent: undefined as any,\n\t\tloc: [] as any,\n\t};\n\tif (typeof type === 'function') {\n\t\t__unsafeRenderFn(vnode, type);\n\t}\n\treturn vnode;\n}\nexport const Fragment = Symbol('Fragment');\n\nconst VOID_TAGS = new Set<string>([\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr',\n]);\nconst RAW_TAGS = new Set<string>(['script', 'style']);\nconst DOM_PARSER_RE =\n\t/(?:<(\\/?)([a-zA-Z][a-zA-Z0-9\\:-]*)(?:\\s([^>]*?))?((?:\\s*\\/)?)>|(<\\!\\-\\-)([\\s\\S]*?)(\\-\\->)|(<\\!)([\\s\\S]*?)(>))/gm;\n\nconst ATTR_KEY_IDENTIFIER = /[\\@\\.a-z0-9_\\:\\-]/i;\n\nfunction splitAttrs(str?: string) {\n\tlet obj: Record<string, string> = {};\n\tif (str) {\n\t\tlet state: 'none' | 'key' | 'value' = 'none';\n\t\tlet currentKey: string | undefined;\n\t\tlet currentValue: string = '';\n\t\tlet tokenStartIndex: number | undefined;\n\t\tlet valueDelimiter: '\"' | \"'\" | undefined;\n\t\tfor (let currentIndex = 0; currentIndex < str.length; currentIndex++) {\n\t\t\tconst currentChar = str[currentIndex];\n\n\t\t\tif (state === 'none') {\n\t\t\t\tif (ATTR_KEY_IDENTIFIER.test(currentChar)) {\n\t\t\t\t\t// add attribute\n\t\t\t\t\tif (currentKey) {\n\t\t\t\t\t\tobj[currentKey] = currentValue;\n\t\t\t\t\t\tcurrentKey = undefined;\n\t\t\t\t\t\tcurrentValue = '';\n\t\t\t\t\t}\n\n\t\t\t\t\ttokenStartIndex = currentIndex;\n\t\t\t\t\tstate = 'key';\n\t\t\t\t} else if (currentChar === '=' && currentKey) {\n\t\t\t\t\tstate = 'value';\n\t\t\t\t}\n\t\t\t} else if (state === 'key') {\n\t\t\t\tif (!ATTR_KEY_IDENTIFIER.test(currentChar)) {\n\t\t\t\t\tcurrentKey = str.substring(tokenStartIndex!, currentIndex);\n\t\t\t\t\tif (currentChar === '=') {\n\t\t\t\t\t\tstate = 'value';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstate = 'none';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tcurrentChar === valueDelimiter &&\n\t\t\t\t\tcurrentIndex > 0 &&\n\t\t\t\t\tstr[currentIndex - 1] !== '\\\\'\n\t\t\t\t) {\n\t\t\t\t\tif (valueDelimiter) {\n\t\t\t\t\t\tcurrentValue = str.substring(tokenStartIndex!, currentIndex);\n\t\t\t\t\t\tvalueDelimiter = undefined;\n\t\t\t\t\t\tstate = 'none';\n\t\t\t\t\t}\n\t\t\t\t} else if (\n\t\t\t\t\t(currentChar === '\"' || currentChar === \"'\") &&\n\t\t\t\t\t!valueDelimiter\n\t\t\t\t) {\n\t\t\t\t\ttokenStartIndex = currentIndex + 1;\n\t\t\t\t\tvalueDelimiter = currentChar;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (\n\t\t\tstate === 'key' &&\n\t\t\ttokenStartIndex != undefined &&\n\t\t\ttokenStartIndex < str.length\n\t\t) {\n\t\t\tcurrentKey = str.substring(tokenStartIndex, str.length);\n\t\t}\n\t\tif (currentKey) {\n\t\t\tobj[currentKey] = currentValue;\n\t\t}\n\t}\n\treturn obj;\n}\n\nexport function parse(input: string | ReturnType<typeof html>): any {\n\tlet str = typeof input === 'string' ? input : input.value;\n\tlet doc: Node,\n\t\tparent: Node,\n\t\ttoken: any,\n\t\ttext,\n\t\ti,\n\t\tbStart,\n\t\tbText,\n\t\tbEnd,\n\t\ttag: Node;\n\tconst tags: Node[] = [];\n\tDOM_PARSER_RE.lastIndex = 0;\n\tparent = doc = {\n\t\ttype: DOCUMENT_NODE,\n\t\tchildren: [] as Node[],\n\t} as any;\n\n\tlet lastIndex = 0;\n\tfunction commitTextNode() {\n\t\ttext = str.substring(lastIndex, DOM_PARSER_RE.lastIndex - token[0].length);\n\t\tif (text) {\n\t\t\t(parent as ParentNode).children.push({\n\t\t\t\ttype: TEXT_NODE,\n\t\t\t\tvalue: text,\n\t\t\t\tparent,\n\t\t\t} as any);\n\t\t}\n\t}\n\n\twhile ((token = DOM_PARSER_RE.exec(str))) {\n\t\tbStart = token[5] || token[8];\n\t\tbText = token[6] || token[9];\n\t\tbEnd = token[7] || token[10];\n\t\tif (RAW_TAGS.has(parent.name) && token[2] !== parent.name) {\n\t\t\ti = DOM_PARSER_RE.lastIndex - token[0].length;\n\t\t\tif (parent.children.length > 0) {\n\t\t\t\tparent.children[0].value += token[0];\n\t\t\t}\n\t\t\tcontinue;\n\t\t} else if (bStart === '<!--') {\n\t\t\ti = DOM_PARSER_RE.lastIndex - token[0].length;\n\t\t\tif (RAW_TAGS.has(parent.name)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\ttag = {\n\t\t\t\ttype: COMMENT_NODE,\n\t\t\t\tvalue: bText,\n\t\t\t\tparent: parent,\n\t\t\t\tloc: [\n\t\t\t\t\t{\n\t\t\t\t\t\tstart: i,\n\t\t\t\t\t\tend: i + bStart.length,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tstart: DOM_PARSER_RE.lastIndex - bEnd.length,\n\t\t\t\t\t\tend: DOM_PARSER_RE.lastIndex,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t} as any;\n\t\t\ttags.push(tag);\n\t\t\t(tag.parent as any).children.push(tag);\n\t\t} else if (bStart === '<!') {\n\t\t\ti = DOM_PARSER_RE.lastIndex - token[0].length;\n\t\t\ttag = {\n\t\t\t\ttype: DOCTYPE_NODE,\n\t\t\t\tvalue: bText,\n\t\t\t\tparent: parent,\n\t\t\t\tloc: [\n\t\t\t\t\t{\n\t\t\t\t\t\tstart: i,\n\t\t\t\t\t\tend: i + bStart.length,\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tstart: DOM_PARSER_RE.lastIndex - bEnd.length,\n\t\t\t\t\t\tend: DOM_PARSER_RE.lastIndex,\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t};\n\t\t\t// commitTextNode();\n\t\t\ttags.push(tag);\n\t\t\ttag.parent.children.push(tag);\n\t\t} else if (token[1] !== '/') {\n\t\t\tcommitTextNode();\n\t\t\tif (RAW_TAGS.has(parent.name)) {\n\t\t\t\tlastIndex = DOM_PARSER_RE.lastIndex;\n\t\t\t\tcommitTextNode();\n\t\t\t\tcontinue;\n\t\t\t} else {\n\t\t\t\ttag = {\n\t\t\t\t\ttype: ELEMENT_NODE,\n\t\t\t\t\tname: token[2] + '',\n\t\t\t\t\tattributes: splitAttrs(token[3]),\n\t\t\t\t\tparent,\n\t\t\t\t\tchildren: [],\n\t\t\t\t\tloc: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tstart: DOM_PARSER_RE.lastIndex - token[0].length,\n\t\t\t\t\t\t\tend: DOM_PARSER_RE.lastIndex,\n\t\t\t\t\t\t},\n\t\t\t\t\t] as any,\n\t\t\t\t};\n\t\t\t\ttags.push(tag);\n\t\t\t\ttag.parent.children.push(tag);\n\t\t\t\tif (\n\t\t\t\t\t(token[4] && token[4].indexOf('/') > -1) ||\n\t\t\t\t\tVOID_TAGS.has(tag.name)\n\t\t\t\t) {\n\t\t\t\t\ttag.loc[1] = tag.loc[0];\n\t\t\t\t\ttag.isSelfClosingTag = true;\n\t\t\t\t} else {\n\t\t\t\t\tparent = tag;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tcommitTextNode();\n\t\t\t// Close parent node if end-tag matches\n\t\t\tif (token[2] + '' === parent.name) {\n\t\t\t\ttag = parent;\n\t\t\t\tparent = tag.parent!;\n\t\t\t\ttag.loc.push({\n\t\t\t\t\tstart: DOM_PARSER_RE.lastIndex - token[0].length,\n\t\t\t\t\tend: DOM_PARSER_RE.lastIndex,\n\t\t\t\t});\n\t\t\t\ttext = str.substring(tag.loc[0].end, tag.loc[1].start);\n\t\t\t\tif (tag.children.length === 0) {\n\t\t\t\t\ttag.children.push({\n\t\t\t\t\t\ttype: TEXT_NODE,\n\t\t\t\t\t\tvalue: text,\n\t\t\t\t\t\tparent,\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t\t// account for abuse of self-closing tags when an end-tag is also provided:\n\t\t\telse if (\n\t\t\t\ttoken[2] + '' === tags[tags.length - 1].name &&\n\t\t\t\ttags[tags.length - 1].isSelfClosingTag === true\n\t\t\t) {\n\t\t\t\ttag = tags[tags.length - 1];\n\t\t\t\ttag.loc.push({\n\t\t\t\t\tstart: DOM_PARSER_RE.lastIndex - token[0].length,\n\t\t\t\t\tend: DOM_PARSER_RE.lastIndex,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\tlastIndex = DOM_PARSER_RE.lastIndex;\n\t}\n\ttext = str.slice(lastIndex);\n\tparent.children.push({\n\t\ttype: TEXT_NODE,\n\t\tvalue: text,\n\t\tparent,\n\t});\n\treturn doc;\n}\n\nexport interface Visitor {\n\t(node: Node, parent?: Node, index?: number): void | Promise<void>;\n}\n\nexport interface VisitorSync {\n\t(node: Node, parent?: Node, index?: number): void;\n}\n\nclass Walker {\n\tconstructor(private callback: Visitor) {}\n\tasync visit(node: Node, parent?: Node, index?: number): Promise<void> {\n\t\tawait this.callback(node, parent, index);\n\t\tif (Array.isArray(node.children)) {\n\t\t\tlet promises: Promise<void>[] = [];\n\t\t\tfor (let i = 0; i < node.children.length; i++) {\n\t\t\t\tconst child = node.children[i];\n\t\t\t\tpromises.push(this.visit(child, node, i));\n\t\t\t}\n\t\t\tawait Promise.all(promises);\n\t\t}\n\t}\n}\n\nclass WalkerSync {\n\tconstructor(private callback: VisitorSync) {}\n\tvisit(node: Node, parent?: Node, index?: number): void {\n\t\tthis.callback(node, parent, index);\n\t\tif (Array.isArray(node.children)) {\n\t\t\tfor (let i = 0; i < node.children.length; i++) {\n\t\t\t\tconst child = node.children[i];\n\t\t\t\tthis.visit(child, node, i);\n\t\t\t}\n\t\t}\n\t}\n}\n\nconst HTMLString = Symbol('HTMLString');\nconst AttrString = Symbol('AttrString');\nexport const RenderFn = Symbol('RenderFn');\nfunction mark(str: string, tags: symbol[] = [HTMLString]): { value: string } {\n\tconst v = { value: str };\n\tfor (const tag of tags) {\n\t\tObject.defineProperty(v, tag, {\n\t\t\tvalue: true,\n\t\t\tenumerable: false,\n\t\t\twritable: false,\n\t\t});\n\t}\n\treturn v;\n}\n\nexport function __unsafeHTML(str: string) {\n\treturn mark(str);\n}\nexport function __unsafeRenderFn(\n\tnode: ElementNode,\n\tfn: (props: Record<string, any>, ...children: Node[]) => Node,\n) {\n\tObject.defineProperty(node, RenderFn, {\n\t\tvalue: fn,\n\t\tenumerable: false,\n\t});\n\treturn node;\n}\n\nconst ESCAPE_CHARS: Record<string, string> = {\n\t'&': '&amp;',\n\t'<': '&lt;',\n\t'>': '&gt;',\n};\nfunction escapeHTML(str: string): string {\n\treturn str.replace(/[&<>]/g, (c) => ESCAPE_CHARS[c] || c);\n}\nexport function attrs(attributes: Record<string, string>) {\n\tlet attrStr = '';\n\tfor (const [key, value] of Object.entries(attributes)) {\n\t\tattrStr += ` ${key}=\"${value}\"`;\n\t}\n\treturn mark(attrStr, [HTMLString, AttrString]);\n}\nexport function html(tmpl: TemplateStringsArray, ...vals: any[]) {\n\tlet buf = '';\n\tfor (let i = 0; i < tmpl.length; i++) {\n\t\tbuf += tmpl[i];\n\t\tconst expr = vals[i];\n\t\tif (buf.endsWith('...') && expr && typeof expr === 'object') {\n\t\t\tbuf = buf.slice(0, -3).trimEnd();\n\t\t\tbuf += attrs(expr).value;\n\t\t} else if (expr && expr[AttrString]) {\n\t\t\tbuf = buf.trimEnd();\n\t\t\tbuf += expr.value;\n\t\t} else if (expr && expr[HTMLString]) {\n\t\t\tbuf += expr.value;\n\t\t} else if (typeof expr === 'string') {\n\t\t\tbuf += escapeHTML(expr);\n\t\t} else if (expr || expr === 0) {\n\t\t\tbuf += String(expr);\n\t\t}\n\t}\n\treturn mark(buf);\n}\n\nexport function walk(node: Node, callback: Visitor): Promise<void> {\n\tconst walker = new Walker(callback);\n\treturn walker.visit(node);\n}\n\nexport function walkSync(node: Node, callback: VisitorSync): void {\n\tconst walker = new WalkerSync(callback);\n\treturn walker.visit(node);\n}\n\nfunction canSelfClose(node: Node): boolean {\n\tif (node.children.length === 0) {\n\t\tlet n: Node | undefined = node;\n\t\twhile ((n = n.parent)) {\n\t\t\tif (n.name === 'svg') return true;\n\t\t}\n\t}\n\treturn false;\n}\n\nasync function renderElement(node: Node): Promise<string> {\n\tconst { name, attributes = {} } = node;\n\tconst children = await Promise.all(\n\t\tnode.children.map((child: Node) => render(child)),\n\t).then((res) => res.join(''));\n\tif (RenderFn in node) {\n\t\tconst value = await (node as any)[RenderFn](attributes, mark(children));\n\t\tif (value && (value as any)[HTMLString]) return value.value;\n\t\treturn escapeHTML(String(value));\n\t}\n\tif (name === Fragment) return children;\n\tconst isSelfClosing = canSelfClose(node);\n\tif (isSelfClosing || VOID_TAGS.has(name)) {\n\t\treturn `<${node.name}${attrs(attributes).value}${\n\t\t\tisSelfClosing ? ' /' : ''\n\t\t}>`;\n\t}\n\treturn `<${node.name}${attrs(attributes).value}>${children}</${node.name}>`;\n}\n\nfunction renderElementSync(node: Node): string {\n\tconst { name, attributes = {} } = node;\n\tconst children = node.children\n\t\t.map((child: Node) => renderSync(child))\n\t\t.join('');\n\tif (RenderFn in node) {\n\t\tconst value = (node as any)[RenderFn](attributes, mark(children));\n\t\tif (value && (value as any)[HTMLString]) return value.value;\n\t\treturn escapeHTML(String(value));\n\t}\n\tif (name === Fragment) return children;\n\tconst isSelfClosing = canSelfClose(node);\n\tif (isSelfClosing || VOID_TAGS.has(name)) {\n\t\treturn `<${node.name}${attrs(attributes).value}${\n\t\t\tisSelfClosing ? ' /' : ''\n\t\t}>`;\n\t}\n\treturn `<${node.name}${attrs(attributes).value}>${children}</${node.name}>`;\n}\n\nexport function renderSync(node: Node): string {\n\tswitch (node.type) {\n\t\tcase DOCUMENT_NODE:\n\t\t\treturn node.children.map((child: Node) => renderSync(child)).join('');\n\t\tcase ELEMENT_NODE:\n\t\t\treturn renderElementSync(node);\n\t\tcase TEXT_NODE:\n\t\t\treturn `${node.value}`;\n\t\tcase COMMENT_NODE:\n\t\t\treturn `<!--${node.value}-->`;\n\t\tcase DOCTYPE_NODE:\n\t\t\treturn `<!${node.value}>`;\n\t}\n}\n\nexport async function render(node: Node): Promise<string> {\n\tswitch (node.type) {\n\t\tcase DOCUMENT_NODE:\n\t\t\treturn Promise.all(\n\t\t\t\tnode.children.map((child: Node) => render(child)),\n\t\t\t).then((res) => res.join(''));\n\t\tcase ELEMENT_NODE:\n\t\t\treturn renderElement(node);\n\t\tcase TEXT_NODE:\n\t\t\treturn `${node.value}`;\n\t\tcase COMMENT_NODE:\n\t\t\treturn `<!--${node.value}-->`;\n\t\tcase DOCTYPE_NODE:\n\t\t\treturn `<!${node.value}>`;\n\t}\n}\n\nexport interface Transformer {\n\t(node: Node): Node | Promise<Node>;\n}\n\nexport interface TransformerSync {\n\t(node: Node): Node;\n}\n\nfunction parseTransformArgs(\n\tmarkup: string | Node,\n\ttransformers: Transformer[],\n) {\n\tif (!Array.isArray(transformers)) {\n\t\tthrow new Error(\n\t\t\t`Invalid second argument for \\`transform\\`! Expected \\`Transformer[]\\` but got \\`${typeof transformers}\\``,\n\t\t);\n\t}\n\tconst doc = typeof markup === 'string' ? parse(markup) : markup;\n\treturn { doc };\n}\n\nexport async function transform(\n\tmarkup: string | Node,\n\ttransformers: Transformer[] = [],\n): Promise<string> {\n\tconst { doc } = parseTransformArgs(markup, transformers);\n\n\tlet newDoc = doc;\n\tfor (const t of transformers) {\n\t\tnewDoc = await t(newDoc);\n\t}\n\treturn render(newDoc);\n}\n\nexport function transformSync(\n\tmarkup: string | Node,\n\ttransformers: TransformerSync[] = [],\n): string {\n\tconst { doc } = parseTransformArgs(markup, transformers);\n\n\tlet newDoc = doc;\n\tfor (const t of transformers) {\n\t\tnewDoc = t(newDoc);\n\t}\n\treturn renderSync(newDoc);\n}\n"], "mappings": "AAuDO,IAAMA,EAAgB,EAChBC,EAAe,EACfC,EAAY,EACZC,EAAe,EACfC,EAAe,EAErB,SAASC,EACfC,EACAC,EAAoC,CAAC,KAClCC,EACF,CACD,IAAMC,EAAqB,CAC1B,KAAM,EACN,KAAM,OAAOH,GAAS,WAAaA,EAAK,KAAOA,EAC/C,WAAYC,GAAS,CAAC,EACtB,SAAUC,EAAS,IAAKE,GACvB,OAAOA,GAAU,SACd,CAAE,KAAM,EAAW,MAAOC,EAAW,OAAOD,CAAK,CAAC,CAAE,EACpDA,CACJ,EACA,OAAQ,OACR,IAAK,CAAC,CACP,EACA,OAAI,OAAOJ,GAAS,YACnBM,EAAiBH,EAAOH,CAAI,EAEtBG,CACR,CACO,IAAMI,EAAW,OAAO,UAAU,EAEnCC,EAAY,IAAI,IAAY,CACjC,OACA,OACA,KACA,MACA,QACA,KACA,MACA,QACA,SACA,OACA,OACA,QACA,SACA,QACA,KACD,CAAC,EACKC,EAAW,IAAI,IAAY,CAAC,SAAU,OAAO,CAAC,EAC9CC,EACL,kHAEKC,EAAsB,qBAE5B,SAASC,EAAWC,EAAc,CACjC,IAAIC,EAA8B,CAAC,EACnC,GAAID,EAAK,CACR,IAAIE,EAAkC,OAClCC,EACAC,EAAuB,GACvBC,EACAC,EACJ,QAASC,EAAe,EAAGA,EAAeP,EAAI,OAAQO,IAAgB,CACrE,IAAMC,EAAcR,EAAIO,CAAY,EAEhCL,IAAU,OACTJ,EAAoB,KAAKU,CAAW,GAEnCL,IACHF,EAAIE,CAAU,EAAIC,EAClBD,EAAa,OACbC,EAAe,IAGhBC,EAAkBE,EAClBL,EAAQ,OACEM,IAAgB,KAAOL,IACjCD,EAAQ,SAECA,IAAU,MACfJ,EAAoB,KAAKU,CAAW,IACxCL,EAAaH,EAAI,UAAUK,EAAkBE,CAAY,EACrDC,IAAgB,IACnBN,EAAQ,QAERA,EAAQ,QAKTM,IAAgBF,GAChBC,EAAe,GACfP,EAAIO,EAAe,CAAC,IAAM,KAEtBD,IACHF,EAAeJ,EAAI,UAAUK,EAAkBE,CAAY,EAC3DD,EAAiB,OACjBJ,EAAQ,SAGRM,IAAgB,KAAOA,IAAgB,MACxC,CAACF,IAEDD,EAAkBE,EAAe,EACjCD,EAAiBE,EAGpB,CAECN,IAAU,OACVG,GAAmB,MACnBA,EAAkBL,EAAI,SAEtBG,EAAaH,EAAI,UAAUK,EAAiBL,EAAI,MAAM,GAEnDG,IACHF,EAAIE,CAAU,EAAIC,EAEpB,CACA,OAAOH,CACR,CAEO,SAASQ,EAAMC,EAA8C,CACnE,IAAIV,EAAM,OAAOU,GAAU,SAAWA,EAAQA,EAAM,MAChDC,EACHC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACKC,EAAe,CAAC,EACtBvB,EAAc,UAAY,EAC1Be,EAASD,EAAM,CACd,KAAM,EACN,SAAU,CAAC,CACZ,EAEA,IAAIU,EAAY,EAChB,SAASC,GAAiB,CACzBR,EAAOd,EAAI,UAAUqB,EAAWxB,EAAc,UAAYgB,EAAM,CAAC,EAAE,MAAM,EACrEC,GACFF,EAAsB,SAAS,KAAK,CACpC,KAAM,EACN,MAAOE,EACP,OAAAF,CACD,CAAQ,CAEV,CAEA,KAAQC,EAAQhB,EAAc,KAAKG,CAAG,GAAI,CAIzC,GAHAgB,EAASH,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC5BI,EAAQJ,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC3BK,EAAOL,EAAM,CAAC,GAAKA,EAAM,EAAE,EACvBjB,EAAS,IAAIgB,EAAO,IAAI,GAAKC,EAAM,CAAC,IAAMD,EAAO,KAAM,CAC1DG,EAAIlB,EAAc,UAAYgB,EAAM,CAAC,EAAE,OACnCD,EAAO,SAAS,OAAS,IAC5BA,EAAO,SAAS,CAAC,EAAE,OAASC,EAAM,CAAC,GAEpC,QACD,SAAWG,IAAW,OAAQ,CAE7B,GADAD,EAAIlB,EAAc,UAAYgB,EAAM,CAAC,EAAE,OACnCjB,EAAS,IAAIgB,EAAO,IAAI,EAC3B,SAEDO,EAAM,CACL,KAAM,EACN,MAAOF,EACP,OAAQL,EACR,IAAK,CACJ,CACC,MAAOG,EACP,IAAKA,EAAIC,EAAO,MACjB,EACA,CACC,MAAOnB,EAAc,UAAYqB,EAAK,OACtC,IAAKrB,EAAc,SACpB,CACD,CACD,EACAuB,EAAK,KAAKD,CAAG,EACZA,EAAI,OAAe,SAAS,KAAKA,CAAG,CACtC,SAAWH,IAAW,KACrBD,EAAIlB,EAAc,UAAYgB,EAAM,CAAC,EAAE,OACvCM,EAAM,CACL,KAAM,EACN,MAAOF,EACP,OAAQL,EACR,IAAK,CACJ,CACC,MAAOG,EACP,IAAKA,EAAIC,EAAO,MACjB,EACA,CACC,MAAOnB,EAAc,UAAYqB,EAAK,OACtC,IAAKrB,EAAc,SACpB,CACD,CACD,EAEAuB,EAAK,KAAKD,CAAG,EACbA,EAAI,OAAO,SAAS,KAAKA,CAAG,UAClBN,EAAM,CAAC,IAAM,IAEvB,GADAS,EAAe,EACX1B,EAAS,IAAIgB,EAAO,IAAI,EAAG,CAC9BS,EAAYxB,EAAc,UAC1ByB,EAAe,EACf,QACD,MACCH,EAAM,CACL,KAAM,EACN,KAAMN,EAAM,CAAC,EAAI,GACjB,WAAYd,EAAWc,EAAM,CAAC,CAAC,EAC/B,OAAAD,EACA,SAAU,CAAC,EACX,IAAK,CACJ,CACC,MAAOf,EAAc,UAAYgB,EAAM,CAAC,EAAE,OAC1C,IAAKhB,EAAc,SACpB,CACD,CACD,EACAuB,EAAK,KAAKD,CAAG,EACbA,EAAI,OAAO,SAAS,KAAKA,CAAG,EAE1BN,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAE,QAAQ,GAAG,EAAI,IACrClB,EAAU,IAAIwB,EAAI,IAAI,GAEtBA,EAAI,IAAI,CAAC,EAAIA,EAAI,IAAI,CAAC,EACtBA,EAAI,iBAAmB,IAEvBP,EAASO,OAIXG,EAAe,EAEXT,EAAM,CAAC,EAAI,KAAOD,EAAO,MAC5BO,EAAMP,EACNA,EAASO,EAAI,OACbA,EAAI,IAAI,KAAK,CACZ,MAAOtB,EAAc,UAAYgB,EAAM,CAAC,EAAE,OAC1C,IAAKhB,EAAc,SACpB,CAAC,EACDiB,EAAOd,EAAI,UAAUmB,EAAI,IAAI,CAAC,EAAE,IAAKA,EAAI,IAAI,CAAC,EAAE,KAAK,EACjDA,EAAI,SAAS,SAAW,GAC3BA,EAAI,SAAS,KAAK,CACjB,KAAM,EACN,MAAOL,EACP,OAAAF,CACD,CAAC,GAKFC,EAAM,CAAC,EAAI,KAAOO,EAAKA,EAAK,OAAS,CAAC,EAAE,MACxCA,EAAKA,EAAK,OAAS,CAAC,EAAE,mBAAqB,KAE3CD,EAAMC,EAAKA,EAAK,OAAS,CAAC,EAC1BD,EAAI,IAAI,KAAK,CACZ,MAAOtB,EAAc,UAAYgB,EAAM,CAAC,EAAE,OAC1C,IAAKhB,EAAc,SACpB,CAAC,GAGHwB,EAAYxB,EAAc,SAC3B,CACA,OAAAiB,EAAOd,EAAI,MAAMqB,CAAS,EAC1BT,EAAO,SAAS,KAAK,CACpB,KAAM,EACN,MAAOE,EACP,OAAAF,CACD,CAAC,EACMD,CACR,CAUA,IAAMY,EAAN,KAAa,CACZ,YAAoBC,EAAmB,CAAnB,cAAAA,CAAoB,CACxC,MAAM,MAAMC,EAAYb,EAAec,EAA+B,CAErE,GADA,MAAM,KAAK,SAASD,EAAMb,EAAQc,CAAK,EACnC,MAAM,QAAQD,EAAK,QAAQ,EAAG,CACjC,IAAIE,EAA4B,CAAC,EACjC,QAASZ,EAAI,EAAGA,EAAIU,EAAK,SAAS,OAAQV,IAAK,CAC9C,IAAMxB,EAAQkC,EAAK,SAASV,CAAC,EAC7BY,EAAS,KAAK,KAAK,MAAMpC,EAAOkC,EAAMV,CAAC,CAAC,CACzC,CACA,MAAM,QAAQ,IAAIY,CAAQ,CAC3B,CACD,CACD,EAEMC,EAAN,KAAiB,CAChB,YAAoBJ,EAAuB,CAAvB,cAAAA,CAAwB,CAC5C,MAAMC,EAAYb,EAAec,EAAsB,CAEtD,GADA,KAAK,SAASD,EAAMb,EAAQc,CAAK,EAC7B,MAAM,QAAQD,EAAK,QAAQ,EAC9B,QAASV,EAAI,EAAGA,EAAIU,EAAK,SAAS,OAAQV,IAAK,CAC9C,IAAMxB,EAAQkC,EAAK,SAASV,CAAC,EAC7B,KAAK,MAAMxB,EAAOkC,EAAMV,CAAC,CAC1B,CAEF,CACD,EAEMc,EAAa,OAAO,YAAY,EAChCC,EAAa,OAAO,YAAY,EACzBC,EAAW,OAAO,UAAU,EACzC,SAASC,EAAKhC,EAAaoB,EAAiB,CAACS,CAAU,EAAsB,CAC5E,IAAMI,EAAI,CAAE,MAAOjC,CAAI,EACvB,QAAWmB,KAAOC,EACjB,OAAO,eAAea,EAAGd,EAAK,CAC7B,MAAO,GACP,WAAY,GACZ,SAAU,EACX,CAAC,EAEF,OAAOc,CACR,CAEO,SAASC,EAAalC,EAAa,CACzC,OAAOgC,EAAKhC,CAAG,CAChB,CACO,SAASP,EACfgC,EACAU,EACC,CACD,cAAO,eAAeV,EAAMM,EAAU,CACrC,MAAOI,EACP,WAAY,EACb,CAAC,EACMV,CACR,CAEA,IAAMW,EAAuC,CAC5C,IAAK,QACL,IAAK,OACL,IAAK,MACN,EACA,SAAS5C,EAAWQ,EAAqB,CACxC,OAAOA,EAAI,QAAQ,SAAWqC,GAAMD,EAAaC,CAAC,GAAKA,CAAC,CACzD,CACO,SAASC,EAAMC,EAAoC,CACzD,IAAIC,EAAU,GACd,OAAW,CAACC,EAAKC,CAAK,IAAK,OAAO,QAAQH,CAAU,EACnDC,GAAW,IAAIC,CAAG,KAAKC,CAAK,IAE7B,OAAOV,EAAKQ,EAAS,CAACX,EAAYC,CAAU,CAAC,CAC9C,CACO,SAASa,EAAKC,KAA+BC,EAAa,CAChE,IAAIC,EAAM,GACV,QAAS/B,EAAI,EAAGA,EAAI6B,EAAK,OAAQ7B,IAAK,CACrC+B,GAAOF,EAAK7B,CAAC,EACb,IAAMgC,EAAOF,EAAK9B,CAAC,EACf+B,EAAI,SAAS,KAAK,GAAKC,GAAQ,OAAOA,GAAS,UAClDD,EAAMA,EAAI,MAAM,EAAG,EAAE,EAAE,QAAQ,EAC/BA,GAAOR,EAAMS,CAAI,EAAE,OACTA,GAAQA,EAAKjB,CAAU,GACjCgB,EAAMA,EAAI,QAAQ,EAClBA,GAAOC,EAAK,OACFA,GAAQA,EAAKlB,CAAU,EACjCiB,GAAOC,EAAK,MACF,OAAOA,GAAS,SAC1BD,GAAOtD,EAAWuD,CAAI,GACZA,GAAQA,IAAS,KAC3BD,GAAO,OAAOC,CAAI,EAEpB,CACA,OAAOf,EAAKc,CAAG,CAChB,CAEO,SAASE,EAAKvB,EAAYD,EAAkC,CAElE,OADe,IAAID,EAAOC,CAAQ,EACpB,MAAMC,CAAI,CACzB,CAEO,SAASwB,EAASxB,EAAYD,EAA6B,CAEjE,OADe,IAAII,EAAWJ,CAAQ,EACxB,MAAMC,CAAI,CACzB,CAEA,SAASyB,EAAazB,EAAqB,CAC1C,GAAIA,EAAK,SAAS,SAAW,EAAG,CAC/B,IAAI0B,EAAsB1B,EAC1B,KAAQ0B,EAAIA,EAAE,QACb,GAAIA,EAAE,OAAS,MAAO,MAAO,EAE/B,CACA,MAAO,EACR,CAEA,eAAeC,EAAc3B,EAA6B,CACzD,GAAM,CAAE,KAAA4B,EAAM,WAAAd,EAAa,CAAC,CAAE,EAAId,EAC5BpC,EAAW,MAAM,QAAQ,IAC9BoC,EAAK,SAAS,IAAKlC,GAAgB+D,EAAO/D,CAAK,CAAC,CACjD,EAAE,KAAMgE,GAAQA,EAAI,KAAK,EAAE,CAAC,EAC5B,GAAIxB,KAAYN,EAAM,CACrB,IAAMiB,EAAQ,MAAOjB,EAAaM,CAAQ,EAAEQ,EAAYP,EAAK3C,CAAQ,CAAC,EACtE,OAAIqD,GAAUA,EAAcb,CAAU,EAAUa,EAAM,MAC/ClD,EAAW,OAAOkD,CAAK,CAAC,CAChC,CACA,GAAIW,IAAS3D,EAAU,OAAOL,EAC9B,IAAMmE,EAAgBN,EAAazB,CAAI,EACvC,OAAI+B,GAAiB7D,EAAU,IAAI0D,CAAI,EAC/B,IAAI5B,EAAK,IAAI,GAAGa,EAAMC,CAAU,EAAE,KAAK,GAC7CiB,EAAgB,KAAO,EACxB,IAEM,IAAI/B,EAAK,IAAI,GAAGa,EAAMC,CAAU,EAAE,KAAK,IAAIlD,CAAQ,KAAKoC,EAAK,IAAI,GACzE,CAEA,SAASgC,EAAkBhC,EAAoB,CAC9C,GAAM,CAAE,KAAA4B,EAAM,WAAAd,EAAa,CAAC,CAAE,EAAId,EAC5BpC,EAAWoC,EAAK,SACpB,IAAKlC,GAAgBmE,EAAWnE,CAAK,CAAC,EACtC,KAAK,EAAE,EACT,GAAIwC,KAAYN,EAAM,CACrB,IAAMiB,EAASjB,EAAaM,CAAQ,EAAEQ,EAAYP,EAAK3C,CAAQ,CAAC,EAChE,OAAIqD,GAAUA,EAAcb,CAAU,EAAUa,EAAM,MAC/ClD,EAAW,OAAOkD,CAAK,CAAC,CAChC,CACA,GAAIW,IAAS3D,EAAU,OAAOL,EAC9B,IAAMmE,EAAgBN,EAAazB,CAAI,EACvC,OAAI+B,GAAiB7D,EAAU,IAAI0D,CAAI,EAC/B,IAAI5B,EAAK,IAAI,GAAGa,EAAMC,CAAU,EAAE,KAAK,GAC7CiB,EAAgB,KAAO,EACxB,IAEM,IAAI/B,EAAK,IAAI,GAAGa,EAAMC,CAAU,EAAE,KAAK,IAAIlD,CAAQ,KAAKoC,EAAK,IAAI,GACzE,CAEO,SAASiC,EAAWjC,EAAoB,CAC9C,OAAQA,EAAK,KAAM,CAClB,IAAK,GACJ,OAAOA,EAAK,SAAS,IAAKlC,GAAgBmE,EAAWnE,CAAK,CAAC,EAAE,KAAK,EAAE,EACrE,IAAK,GACJ,OAAOkE,EAAkBhC,CAAI,EAC9B,IAAK,GACJ,MAAO,GAAGA,EAAK,KAAK,GACrB,IAAK,GACJ,MAAO,OAAOA,EAAK,KAAK,MACzB,IAAK,GACJ,MAAO,KAAKA,EAAK,KAAK,GACxB,CACD,CAEA,eAAsB6B,EAAO7B,EAA6B,CACzD,OAAQA,EAAK,KAAM,CAClB,IAAK,GACJ,OAAO,QAAQ,IACdA,EAAK,SAAS,IAAKlC,GAAgB+D,EAAO/D,CAAK,CAAC,CACjD,EAAE,KAAMgE,GAAQA,EAAI,KAAK,EAAE,CAAC,EAC7B,IAAK,GACJ,OAAOH,EAAc3B,CAAI,EAC1B,IAAK,GACJ,MAAO,GAAGA,EAAK,KAAK,GACrB,IAAK,GACJ,MAAO,OAAOA,EAAK,KAAK,MACzB,IAAK,GACJ,MAAO,KAAKA,EAAK,KAAK,GACxB,CACD,CAUA,SAASkC,EACRC,EACAC,EACC,CACD,GAAI,CAAC,MAAM,QAAQA,CAAY,EAC9B,MAAM,IAAI,MACT,mFAAmF,OAAOA,CAAY,IACvG,EAGD,MAAO,CAAE,IADG,OAAOD,GAAW,SAAWnD,EAAMmD,CAAM,EAAIA,CAC5C,CACd,CAEA,eAAsBE,EACrBF,EACAC,EAA8B,CAAC,EACb,CAClB,GAAM,CAAE,IAAAlD,CAAI,EAAIgD,EAAmBC,EAAQC,CAAY,EAEnDE,EAASpD,EACb,QAAWqD,KAAKH,EACfE,EAAS,MAAMC,EAAED,CAAM,EAExB,OAAOT,EAAOS,CAAM,CACrB,CAEO,SAASE,EACfL,EACAC,EAAkC,CAAC,EAC1B,CACT,GAAM,CAAE,IAAAlD,CAAI,EAAIgD,EAAmBC,EAAQC,CAAY,EAEnDE,EAASpD,EACb,QAAWqD,KAAKH,EACfE,EAASC,EAAED,CAAM,EAElB,OAAOL,EAAWK,CAAM,CACzB", "names": ["DOCUMENT_NODE", "ELEMENT_NODE", "TEXT_NODE", "COMMENT_NODE", "DOCTYPE_NODE", "h", "type", "props", "children", "vnode", "child", "escapeHTML", "__unsafeRenderFn", "Fragment", "VOID_TAGS", "RAW_TAGS", "DOM_PARSER_RE", "ATTR_KEY_IDENTIFIER", "splitAttrs", "str", "obj", "state", "current<PERSON><PERSON>", "currentValue", "tokenStartIndex", "valueDelimiter", "currentIndex", "currentChar", "parse", "input", "doc", "parent", "token", "text", "i", "bStart", "bText", "bEnd", "tag", "tags", "lastIndex", "commitTextNode", "<PERSON>", "callback", "node", "index", "promises", "WalkerS<PERSON>", "HTMLString", "AttrString", "RenderFn", "mark", "v", "__unsafeHTML", "fn", "ESCAPE_CHARS", "c", "attrs", "attributes", "attrStr", "key", "value", "html", "tmpl", "vals", "buf", "expr", "walk", "walkSync", "canSelfClose", "n", "renderElement", "name", "render", "res", "isSelfClosing", "renderElementSync", "renderSync", "parseTransformArgs", "markup", "transformers", "transform", "newDoc", "t", "transformSync"]}